#!/usr/bin/env python3
"""
Create clean token file from the current token data.
"""

import json
from pathlib import Path

def extract_and_save_token():
    """Extract access token and save it properly."""
    
    # Token file paths
    home = Path.home()
    credentials_dir = home / ".credentials"
    token_file = credentials_dir / "onenote_graph_token.txt"
    langchain_token_file = credentials_dir / "langchain_onenote_token.txt"
    
    print(f"📁 Reading token from: {token_file}")
    
    try:
        # Read the current token file
        with open(token_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        print(f"📊 File size: {len(content)} characters")
        
        # Try to parse as JSON
        try:
            token_data = json.loads(content)
            access_token = token_data.get('access_token')
            
            if access_token:
                print(f"✅ Found access token: {len(access_token)} characters")
                
                # Save just the access token
                with open(token_file, 'w', encoding='utf-8') as f:
                    f.write(access_token)
                
                # Also create LangChain version
                with open(langchain_token_file, 'w', encoding='utf-8') as f:
                    f.write(access_token)
                
                print(f"✅ Token files updated successfully")
                print(f"   📄 Main: {token_file}")
                print(f"   📄 LangChain: {langchain_token_file}")
                
                return True
            else:
                print("❌ No access token found in JSON")
                return False
                
        except json.JSONDecodeError:
            # Already a raw token
            if len(content) > 100:
                print("✅ File already contains raw token")
                
                # Create LangChain version
                with open(langchain_token_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ LangChain token file created: {langchain_token_file}")
                return True
            else:
                print("❌ Token too short, may be invalid")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Creating Clean Token File")
    print("=" * 30)
    
    success = extract_and_save_token()
    
    if success:
        print("\n✅ Token file fixed!")
        print("📋 Ready to run OneNote processing")
    else:
        print("\n❌ Failed to fix token file")
        print("📋 May need to re-authenticate")
