#!/usr/bin/env python3
"""
Run entity deduplication to fix duplicate entities like C-G and G-C.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger
from utils.auto_deduplication import deduplicate_all_entities

logger = get_logger(__name__)

async def main():
    """Run entity deduplication."""
    logger.info("🚀 STARTING ENTITY DEDUPLICATION")
    logger.info("=" * 50)
    
    try:
        # Run deduplication for all entities
        success = await deduplicate_all_entities()
        
        if success:
            logger.info("✅ Entity deduplication completed successfully!")
            logger.info("   Duplicate entities like 'C-G' and 'G-C' should now be merged")
        else:
            logger.error("❌ Entity deduplication failed")
            
    except Exception as e:
        logger.error(f"❌ Error during entity deduplication: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
