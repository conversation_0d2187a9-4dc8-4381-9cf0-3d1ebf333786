#!/usr/bin/env python3
"""
Check current entities in the database.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter

async def check_entities():
    adapter = GraphitiFalkorDBAdapter('graphiti')
    
    # Check if there are any entities now
    query = 'MATCH (e:Entity) RETURN count(e) as count'
    result = adapter.execute_cypher(query)
    print('Current entity count:', result)
    
    # Get a sample of entities
    query = 'MATCH (e:Entity) RETURN e.name as name, e.type as type LIMIT 10'
    result = adapter.execute_cypher(query)
    print('Sample entities:')
    if result and len(result) > 1:
        headers = result[0]
        for row in result[1]:
            entity_data = dict(zip(headers, row))
            print(f'  - {entity_data["name"]} ({entity_data["type"]})')
    else:
        print('  No entities found')

if __name__ == "__main__":
    asyncio.run(check_entities())
