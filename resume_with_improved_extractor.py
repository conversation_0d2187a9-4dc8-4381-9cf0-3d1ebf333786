#!/usr/bin/env python3
"""
Resume processing all remaining documents with the IMPROVED extractor
"""

import asyncio
import os
import csv
from pathlib import Path
from datetime import datetime

async def resume_all_processing():
    """Resume processing all documents with the improved extractor."""
    
    print("🚀 RESUMING WITH IMPROVED EXTRACTOR")
    print("=" * 60)
    
    try:
        # Get all documents from database
        from database.falkordb_adapter import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Episode)
        RETURN e.uuid as document_id, 
               e.name as filename, 
               e.file_path as file_path
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        
        documents = []
        if result and len(result) > 1:
            for row in result[1]:
                doc_data = {
                    'document_id': row[0] if len(row) > 0 else '',
                    'filename': row[1] if len(row) > 1 else '',
                    'file_path': row[2] if len(row) > 2 else ''
                }
                documents.append(doc_data)
        
        print(f"📄 Found {len(documents)} total documents in database")
        
        # Check which documents already have aggressive CSV files
        ref_dir = Path("references")
        ref_dir.mkdir(exist_ok=True)
        
        existing_aggressive = set()
        for csv_file in ref_dir.glob("*_aggressive_references.csv"):
            doc_id = csv_file.name.split('_')[0]
            existing_aggressive.add(doc_id)
        
        print(f"📁 Found {len(existing_aggressive)} existing aggressive CSV files")
        
        # Find documents that still need processing
        remaining_docs = []
        for doc in documents:
            doc_id = doc['document_id']
            if doc_id not in existing_aggressive and doc['file_path']:
                remaining_docs.append(doc)
        
        print(f"❌ Documents still needing processing: {len(remaining_docs)}")
        
        if not remaining_docs:
            print("\n🎉 All documents already processed!")
            await count_final_results()
            return
        
        # Show what we'll process
        print(f"\n📋 DOCUMENTS TO PROCESS WITH IMPROVED EXTRACTOR:")
        for i, doc in enumerate(remaining_docs[:15], 1):
            filename = doc['filename'][:50] + "..." if len(doc['filename']) > 50 else doc['filename']
            print(f"   {i:2d}. {filename}")
        
        if len(remaining_docs) > 15:
            print(f"   ... and {len(remaining_docs) - 15} more documents")
        
        # Process all remaining documents
        print(f"\n🚀 Processing {len(remaining_docs)} documents with IMPROVED extractor...")
        
        from services.improved_aggressive_reference_extractor import get_improved_aggressive_reference_extractor
        from utils.mistral_ocr import MistralOCRProcessor
        
        extractor = await get_improved_aggressive_reference_extractor()
        ocr_processor = MistralOCRProcessor()
        
        successful_count = 0
        total_new_refs = 0
        failed_docs = []
        
        for i, doc in enumerate(remaining_docs, 1):
            print(f"\n📖 Processing {i}/{len(remaining_docs)}: {doc['filename'][:40]}...")
            
            try:
                file_path = doc['file_path']
                if not file_path or not os.path.exists(file_path):
                    print(f"   ⚠️ File not found, skipping")
                    failed_docs.append((doc['filename'], "File not found"))
                    continue
                
                # Extract text with timeout
                print(f"   🔍 Extracting text...")
                try:
                    ocr_result = await asyncio.wait_for(
                        ocr_processor.process_pdf(file_path),
                        timeout=120  # 2 minute timeout
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ OCR timeout, skipping")
                    failed_docs.append((doc['filename'], "OCR timeout"))
                    continue
                
                if not ocr_result.get('success', False):
                    print(f"   ❌ OCR failed, skipping")
                    failed_docs.append((doc['filename'], "OCR failed"))
                    continue
                
                extracted_text = ocr_result.get('text', '')
                print(f"   📝 Extracted {len(extracted_text):,} characters")
                
                if len(extracted_text) < 100:
                    print(f"   ⚠️ Text too short, skipping")
                    failed_docs.append((doc['filename'], "Text too short"))
                    continue
                
                # Extract references with IMPROVED extractor
                print(f"   🚀 Extracting references with IMPROVED extractor...")
                try:
                    ref_result = await asyncio.wait_for(
                        extractor.extract_references_from_text(extracted_text, doc['filename']),
                        timeout=180  # 3 minute timeout (generous)
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ Reference extraction timeout, skipping")
                    failed_docs.append((doc['filename'], "Reference extraction timeout"))
                    continue
                
                references_found = ref_result.get('total_found', 0)
                confidence = ref_result.get('confidence_score', 0.0)
                processing_time = ref_result.get('processing_time', 0.0)
                
                # Save to CSV
                csv_filename = f"{doc['document_id']}_{Path(file_path).stem}_aggressive_references.csv"
                csv_path = ref_dir / csv_filename
                
                await save_references_to_csv(ref_result, csv_path)
                
                print(f"   ✅ Found {references_found} references (confidence: {confidence:.2f}, time: {processing_time:.1f}s)")
                print(f"   💾 Saved to: {csv_filename}")
                
                # Show extraction methods used
                methods = ref_result.get('extraction_methods', {})
                if methods:
                    method_summary = ", ".join([f"{method}: {count}" for method, count in methods.items()])
                    print(f"   🔧 Methods: {method_summary}")
                
                successful_count += 1
                total_new_refs += references_found
                
                # Brief pause every 5 documents
                if i % 5 == 0:
                    print(f"   ⏸️ Brief pause... ({successful_count}/{i} successful so far)")
                    await asyncio.sleep(3)
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                failed_docs.append((doc['filename'], str(e)))
                continue
        
        print(f"\n🎉 IMPROVED EXTRACTOR PROCESSING COMPLETE!")
        print(f"   ✅ Successfully processed: {successful_count}/{len(remaining_docs)} documents")
        print(f"   📚 New references found: {total_new_refs}")
        print(f"   ❌ Failed documents: {len(failed_docs)}")
        
        if failed_docs:
            print(f"\n⚠️ FAILED DOCUMENTS:")
            for filename, reason in failed_docs[:10]:
                print(f"   - {filename[:60]}... ({reason})")
            if len(failed_docs) > 10:
                print(f"   ... and {len(failed_docs) - 10} more")
        
        # Count final results
        await count_final_results()
        
        # Save processing summary
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = f"improved_extractor_processing_{timestamp}.json"
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'total_documents': len(remaining_docs),
                'successful_count': successful_count,
                'failed_count': len(failed_docs),
                'total_references_found': total_new_refs,
                'failed_documents': failed_docs,
                'extractor_version': 'improved_v1.0'
            }, f, indent=2)
        
        print(f"   💾 Processing summary saved to: {summary_file}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def save_references_to_csv(ref_result, csv_path):
    """Save reference results to CSV file"""
    try:
        detailed_refs = ref_result.get('detailed_references', [])
        
        if not detailed_refs:
            simple_refs = ref_result.get('references', [])
            detailed_refs = [{'text': ref, 'extraction_method': 'improved_aggressive'} for ref in simple_refs]
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'reference_number', 'text', 'authors', 'title', 'journal', 
                'year', 'doi', 'pmid', 'url', 'confidence', 
                'extraction_method', 'source_section'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for i, ref in enumerate(detailed_refs, 1):
                writer.writerow({
                    'reference_number': ref.get('number', i),
                    'text': ref.get('text', ''),
                    'authors': ref.get('authors', ''),
                    'title': ref.get('title', ''),
                    'journal': ref.get('journal', ''),
                    'year': ref.get('year', ''),
                    'doi': ref.get('doi', ''),
                    'pmid': ref.get('pmid', ''),
                    'url': ref.get('url', ''),
                    'confidence': ref.get('confidence', 0.0),
                    'extraction_method': ref.get('extraction_method', 'improved_aggressive'),
                    'source_section': ref.get('source_section', 'document')
                })
        
    except Exception as e:
        print(f"   ⚠️ Error saving CSV: {e}")

async def count_final_results():
    """Count the final reference results"""
    print(f"\n📊 FINAL REFERENCE COUNT:")
    
    ref_dir = Path("references")
    total_refs = 0
    aggressive_refs = 0
    improved_refs = 0
    file_count = 0
    
    if ref_dir.exists():
        for csv_file in ref_dir.glob("*.csv"):
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    ref_count = len(rows) - 1  # Subtract header
                    
                    if ref_count > 0:
                        total_refs += ref_count
                        file_count += 1
                        
                        if 'aggressive' in csv_file.name:
                            aggressive_refs += ref_count
                            
                        # Check if it's from improved extractor
                        try:
                            with open(csv_file, 'r', encoding='utf-8') as f2:
                                reader2 = csv.DictReader(f2)
                                first_row = next(reader2, {})
                                if 'improved' in first_row.get('extraction_method', ''):
                                    improved_refs += ref_count
                        except:
                            pass
                            
            except Exception:
                continue
    
    print(f"   📁 Total CSV files: {file_count}")
    print(f"   🚀 Aggressive references: {aggressive_refs}")
    print(f"   ⚡ Improved extractor references: {improved_refs}")
    print(f"   📚 Total references: {total_refs}")
    
    improvement = total_refs / 396 if total_refs > 396 else 1
    print(f"   📈 Improvement over original: {improvement:.1f}x ({total_refs} vs 396)")
    
    if total_refs >= 3000:
        print(f"   🎉 EXCELLENT! Massive improvement achieved!")
    elif total_refs >= 1000:
        print(f"   🎯 GREAT! Significant improvement achieved!")
    else:
        print(f"   📊 Good progress, {total_refs} references found")
    
    # Test API response
    try:
        import requests
        response = requests.get("http://localhost:9753/api/fast/graph-stats", timeout=10)
        if response.status_code == 200:
            api_data = response.json()
            api_refs = api_data.get('total_references', 0)
            print(f"   📱 API will show: {api_refs} references (refresh dashboard to see)")
        else:
            print(f"   📱 API check failed: {response.status_code}")
    except Exception as e:
        print(f"   📱 API check failed: {e}")

if __name__ == "__main__":
    asyncio.run(resume_all_processing())
