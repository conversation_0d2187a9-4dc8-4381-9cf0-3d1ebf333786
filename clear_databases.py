#!/usr/bin/env python3
"""
Clear all databases for fresh start with enhanced processing.
"""

import falkordb
import redis
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_falkordb():
    """Clear FalkorDB completely."""
    try:
        # Connect to FalkorDB
        falkor_client = falkordb.FalkorDB(host='localhost', port=6379)
        
        # Get list of all graphs
        graphs_result = falkor_client.list_graphs()
        logger.info(f"Found graphs: {graphs_result}")
        
        # Delete the main graph
        graph_name = "graphiti_knowledge"
        try:
            graph = falkor_client.select_graph(graph_name)
            # Delete all nodes and relationships
            graph.query("MATCH (n) DETACH DELETE n")
            logger.info(f"✅ Cleared all nodes and relationships from {graph_name}")
        except Exception as e:
            logger.info(f"Graph {graph_name} might not exist: {e}")
        
        # Try to delete the graph entirely
        try:
            falkor_client.delete_graph(graph_name)
            logger.info(f"✅ Deleted graph {graph_name}")
        except Exception as e:
            logger.info(f"Could not delete graph {graph_name}: {e}")
        
        logger.info("✅ FalkorDB cleared successfully")
        
    except Exception as e:
        logger.error(f"❌ Error clearing FalkorDB: {e}")

def clear_redis():
    """Clear Redis embeddings and vector data."""
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        
        # Get all embedding keys
        embedding_keys = redis_client.keys('embedding:*')
        vector_keys = redis_client.keys('vector:*')
        chunk_keys = redis_client.keys('chunk:*')
        doc_keys = redis_client.keys('doc:*')
        
        all_keys = embedding_keys + vector_keys + chunk_keys + doc_keys
        
        if all_keys:
            # Delete all keys
            deleted = redis_client.delete(*all_keys)
            logger.info(f"✅ Deleted {deleted} keys from Redis")
        else:
            logger.info("✅ No keys to delete in Redis")
        
        logger.info("✅ Redis cleared successfully")
        
    except Exception as e:
        logger.error(f"❌ Error clearing Redis: {e}")

def main():
    """Clear both databases."""
    print("🧹 CLEARING ALL DATABASES FOR FRESH START")
    print("=" * 50)
    
    print("\n🔄 Clearing FalkorDB...")
    clear_falkordb()
    
    print("\n🔄 Clearing Redis...")
    clear_redis()
    
    print("\n✅ ALL DATABASES CLEARED!")
    print("Ready for fresh processing with enhanced AI system.")

if __name__ == "__main__":
    main()
