#!/usr/bin/env python3
"""
Test the Q&A system to see if it can find relevant information.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger
from services.qa_service import get_relevant_facts

logger = get_logger(__name__)

async def test_qa_queries():
    """Test Q&A system with sample queries."""
    try:
        logger.info("🔄 TESTING Q&A SYSTEM")
        logger.info("=" * 50)
        
        # Test queries that should find results in medical documents
        test_questions = [
            "cocoa and human health",
            "cancer and antioxidants", 
            "ginger health benefits",
            "vitamin C immune system",
            "inflammation and disease"
        ]
        
        for question in test_questions:
            logger.info(f"\n🔍 Testing question: '{question}'")
            
            try:
                facts = await get_relevant_facts(question, limit=3)
                logger.info(f"   Found {len(facts)} relevant chunks")
                
                if facts:
                    for i, fact in enumerate(facts[:2]):
                        body = fact.get('body', '')[:150]
                        doc_name = fact.get('document_name', 'Unknown')
                        logger.info(f"   {i+1}. {body}...")
                        logger.info(f"      From: {doc_name}")
                else:
                    logger.warning(f"   ❌ No facts found for '{question}'")
                    
            except Exception as e:
                logger.error(f"   ❌ Error processing '{question}': {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing Q&A system: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_chunk_search():
    """Test direct chunk search to verify data exists."""
    try:
        logger.info("\n🔍 TESTING DIRECT CHUNK SEARCH")
        logger.info("=" * 40)
        
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        # Search for chunks containing specific terms
        search_terms = ["cocoa", "cancer", "antioxidant", "ginger", "vitamin"]
        
        for term in search_terms:
            query = f"""
            MATCH (c:Chunk)
            WHERE toLower(c.text) CONTAINS toLower('{term}')
            RETURN c.text, c.document_id
            LIMIT 2
            """
            
            result = adapter.execute_cypher(query)
            
            if result and len(result) > 1 and len(result[1]) > 0:
                logger.info(f"✅ Found chunks containing '{term}': {len(result[1])}")
                for row in result[1][:1]:
                    text_snippet = row[0][:100] if row[0] else "No text"
                    logger.info(f"   Sample: {text_snippet}...")
            else:
                logger.info(f"❌ No chunks found containing '{term}'")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in direct chunk search: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 TESTING Q&A SYSTEM CONNECTIVITY")
    logger.info("=" * 50)
    
    # Test direct chunk search first
    chunk_search_ok = await test_chunk_search()
    
    if chunk_search_ok:
        # Test Q&A system
        qa_ok = await test_qa_queries()
        
        if qa_ok:
            logger.info("\n🎉 Q&A SYSTEM TESTS COMPLETED!")
            logger.info("   Check the results above to see if relevant chunks were found")
        else:
            logger.error("\n❌ Q&A SYSTEM TESTS FAILED")
    else:
        logger.error("\n❌ CHUNK SEARCH FAILED - No data available for Q&A")

if __name__ == "__main__":
    asyncio.run(main())
