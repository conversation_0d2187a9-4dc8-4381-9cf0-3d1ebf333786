{"operation_id": "e47081f0-7cb3-4941-91e6-f8767af842dd", "document_name": "<PERSON><PERSON> - cure for alll diseases.docx", "status": "completed", "progress_percentage": 100, "processing_time": "2025-07-16T09:37:33.770398", "facts_count": 907, "entities_count": 9153, "references_count": 0, "embeddings_count": 907, "embeddings_in_redis_count": 0, "start_time": "2025-07-15T23:37:24.475473+00:00", "completion_time": "2025-07-16T01:31:52.285948+00:00", "step_history": [{"step": 1, "step_name": "Validating document", "progress_percentage": 10, "timestamp": "2025-07-15T23:37:24.480065+00:00"}, {"step": 2, "step_name": "Extracting text from document", "progress_percentage": 20, "timestamp": "2025-07-15T23:37:24.484628+00:00"}, {"step": 3, "step_name": "Chunking document text", "progress_percentage": 35, "timestamp": "2025-07-15T23:37:30.842377+00:00"}, {"step": 4, "step_name": "Storing in knowledge graph", "progress_percentage": 50, "timestamp": "2025-07-15T23:37:31.200986+00:00"}, {"step": 5, "step_name": "Extracting entities", "progress_percentage": 65, "timestamp": "2025-07-15T23:37:33.770398+00:00"}, {"step": 6, "step_name": "Extracting references", "progress_percentage": 80, "timestamp": "2025-07-16T01:07:48.260701+00:00"}, {"step": 7, "step_name": "Generating embeddings", "progress_percentage": 95, "timestamp": "2025-07-16T01:08:18.044673+00:00"}, {"step": 7, "step_name": "Processing complete", "progress_percentage": 100, "timestamp": "2025-07-16T01:31:52.197804+00:00"}, {"step": 7, "step_name": "Processing complete", "progress_percentage": 100, "timestamp": "2025-07-16T01:31:52.285948+00:00"}], "all_details": {"start_time": "2025-07-15T23:37:24.475473+00:00", "facts_count": 907, "entities_count": 9153, "references_count": 0, "embeddings_count": 907, "embeddings_in_redis_count": 0, "processing_time": "2025-07-16T09:37:33.770398", "completion_time": "2025-07-16T01:31:52.285948+00:00", "success": true, "episode_id": "c29d8d7b-6793-44d7-808b-a017639653f9", "file_path": "C:\\Users\\<USER>\\Graphiti\\graphiti\\uploads\\bddccf7b-7988-434a-802a-623a32922c75_<PERSON><PERSON> - cure for alll diseases.docx", "file_type": "docling", "chunks": 907, "text_length": 1114661, "metadata": {"title": "bddccf7b-7988-434a-802a-623a32922c75_<PERSON><PERSON> - cure for alll diseases", "file_size": 829372, "file_extension": ".docx", "extraction_timestamp": "2025-07-16T09:37:30.822380", "word_count": 177516, "character_count": 1114661, "extraction_method": "docling", "docling_version": "unknown"}, "ocr_provider": "docling", "entities": 9153, "entity_types": ["Health_Condition", "Mycotoxin", "Irritant", "Medication/Refrigerant", "Virus", "Toxic_Element", "Food/Nutrient", "<PERSON><PERSON>", "Herb/Botanical_Source", "Bioactive_Compound", "Food", "Source_of_Toxins", "Toxic_Compound", "Biological_Structure", "Dentalware", "Organization/Bacteria", "Body_Part", "Food/Application", "Metabolite", "Substance", "Flavoring", "Cell", "Pathogen", "Biochemical_Pathway", "Therapeutic Intervention", "Specimen", "Environmental_Factor", "Therapeutic_Intervention", "Nutrient/Metabolite", "Equipment", "Biological Entity/Virus", "Cell_Type", "Neurotransmitter", "Therapeutic Intervention/Treatment", "Bioactive_Compound/Biomarker", "Medication/Toxin", "Organ", "Biological_Agent", "Concept/Research", "Toxic_Substance", "Product", "Herb/Parasite", "Genetic_Element", "Ingredient", "Biochemical_Marker", "Symptom", "Disease", "<PERSON><PERSON>", "<PERSON>", "Toxic_Chemical", "Therapeutic_Approach", "Compound", "Treatment", "Process", "Diagnostic_Test", "Disease/Parasite", "Receptor", "Parasite", "Bacterium", "Water_Source", "Concept/Process", "Body Fluid", "Diagnostic_Technique", "Researcher", "Research", "Nutrient", "Dental_Material", "Toxic_Metal", "Location", "Medication", "Toxic Metal", "Biochemical_Element", "Physical_Toxin", "Clinical_Trial", "Medical_Device", "Process/Health Condition", "Toxic Substance", "Part_Used", "Allergen", "Biochemical_Pathway_Disruptor", "Diet", "Disease Causing Agent", "Body_Fluid", "Mechanism", "Biological Structure", "Food_Additive", "Disease_Causing_Agent", "Toxic Compound", "Dosage", "Nutrient/Treatment", "Organization", "Solvent", "Person", "Biomarker", "Parasite Stage", "Biological Sample", "Property", "Food/Part_Used", "Biochemical_Pathway/Organ", "Health Condition", "Biological_Sample", "Organization/Biological Entity", "Toxic_Source", "Material", "Source_of_Exposure", "Preparation", "Condition", "Component", "Anatomical_Structure", "Biological Entity/Cell Type", "Toxic_Metabolite", "Power_Source", "Device/Research", "Biological Entity", "Water_Treatment", "Biological_Entity", "Concept", "Enzyme", "Application", "Unit", "Diagnostic_Process", "Chemical_Compound", "Botanical_Source", "Chemical_Element", "Chemical", "Hormone", "Bacteria", "Pollutant", "Beverage", "Microorganism", "Toxin", "Biological_Source", "Parasite/Disease", "Organism"], "references": 0, "reference_file": "data\\references\\references_bddccf7b-7988-434a-802a-623a32922c75_<PERSON><PERSON> - cure for alll diseases_20250716_110818.csv", "reference_extraction_method": "improved_mistral_ocr", "embeddings": 907}, "saved_at": "2025-07-16T01:31:52.285948+00:00"}