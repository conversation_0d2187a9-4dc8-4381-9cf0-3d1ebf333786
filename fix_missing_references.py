#!/usr/bin/env python3
"""
Fix the missing references by reprocessing documents that don't have aggressive CSV files
"""

import asyncio
import os
import json
from pathlib import Path

async def fix_missing_references():
    """Find and reprocess documents missing aggressive reference CSV files"""
    
    print("🔧 FIXING MISSING REFERENCE CSV FILES")
    print("=" * 50)
    
    try:
        # Get all documents from database
        from database.falkordb_adapter import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Episode)
        RETURN e.uuid as document_id, 
               e.name as filename, 
               e.file_path as file_path
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        
        documents = []
        if result and len(result) > 1:
            for row in result[1]:
                doc_data = {
                    'document_id': row[0] if len(row) > 0 else '',
                    'filename': row[1] if len(row) > 1 else '',
                    'file_path': row[2] if len(row) > 2 else ''
                }
                documents.append(doc_data)
        
        print(f"📄 Found {len(documents)} documents in database")
        
        # Check which documents have aggressive CSV files
        ref_dir = Path("references")
        existing_aggressive_files = set()
        
        if ref_dir.exists():
            for csv_file in ref_dir.glob("*_aggressive_references.csv"):
                # Extract document ID from filename
                filename_parts = csv_file.name.split('_')
                if len(filename_parts) > 0:
                    doc_id = filename_parts[0]
                    existing_aggressive_files.add(doc_id)
        
        print(f"📁 Found {len(existing_aggressive_files)} existing aggressive CSV files")
        
        # Find documents missing aggressive CSV files
        missing_docs = []
        for doc in documents:
            doc_id = doc['document_id']
            if doc_id not in existing_aggressive_files:
                missing_docs.append(doc)
        
        print(f"❌ Found {len(missing_docs)} documents missing aggressive CSV files")
        
        if not missing_docs:
            print("✅ All documents already have aggressive CSV files!")
            return
        
        # Show which documents are missing
        print(f"\n📋 MISSING AGGRESSIVE CSV FILES:")
        for i, doc in enumerate(missing_docs[:10], 1):
            filename = doc['filename'][:60] + "..." if len(doc['filename']) > 60 else doc['filename']
            print(f"   {i:2d}. {filename}")
        
        if len(missing_docs) > 10:
            print(f"   ... and {len(missing_docs) - 10} more documents")
        
        # Ask if we should reprocess
        print(f"\n🔄 REPROCESSING PLAN:")
        print(f"   📄 Documents to reprocess: {len(missing_docs)}")
        print(f"   🚀 Using aggressive reference extraction")
        print(f"   💾 Will save results to CSV files")
        print(f"   ⏱️  Estimated time: {len(missing_docs) * 2} minutes")
        
        # Start reprocessing
        print(f"\n🚀 Starting reprocessing...")
        
        from services.aggressive_reference_extractor import get_aggressive_reference_extractor
        from utils.mistral_ocr import MistralOCRProcessor
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        
        extractor = await get_aggressive_reference_extractor()
        ocr_processor = MistralOCRProcessor()
        doc_processor = EnhancedDocumentProcessor()
        
        processed_count = 0
        total_new_refs = 0
        
        for i, doc in enumerate(missing_docs, 1):
            print(f"\n📖 Processing {i}/{len(missing_docs)}: {doc['filename'][:50]}...")
            
            try:
                file_path = doc['file_path']
                if not file_path or not os.path.exists(file_path):
                    print(f"   ⚠️ File not found: {file_path}")
                    continue
                
                # Extract text
                print(f"   🔍 Extracting text...")
                ocr_result = await ocr_processor.process_pdf(file_path)
                
                if not ocr_result.get('success', False):
                    print(f"   ❌ OCR failed")
                    continue
                
                extracted_text = ocr_result.get('text', '')
                if len(extracted_text) < 100:
                    print(f"   ⚠️ Text too short")
                    continue
                
                # Extract references
                print(f"   🚀 Extracting references...")
                ref_result = await extractor.extract_references_from_text(extracted_text, doc['filename'])
                
                references_found = ref_result.get('total_found', 0)
                confidence = ref_result.get('confidence_score', 0.0)
                
                # Save to CSV
                csv_path = await doc_processor._save_aggressive_references_to_csv(
                    ref_result, Path(file_path)
                )
                
                print(f"   ✅ Found {references_found} references (confidence: {confidence:.2f})")
                print(f"   💾 Saved to: {Path(csv_path).name}")
                
                processed_count += 1
                total_new_refs += references_found
                
                # Small delay to avoid overwhelming the system
                if i % 5 == 0:
                    print(f"   ⏸️ Brief pause...")
                    await asyncio.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        print(f"\n🎉 REPROCESSING COMPLETE!")
        print(f"   ✅ Successfully processed: {processed_count}/{len(missing_docs)} documents")
        print(f"   📚 New references found: {total_new_refs}")
        
        # Check final totals
        print(f"\n📊 FINAL REFERENCE COUNT CHECK:")
        
        # Recount CSV files
        total_csv_refs = 0
        if ref_dir.exists():
            for csv_file in ref_dir.glob("*.csv"):
                try:
                    import csv
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                        ref_count = len(rows) - 1
                        if ref_count > 0:
                            total_csv_refs += ref_count
                except:
                    continue
        
        print(f"   📁 Total CSV references now: {total_csv_refs}")
        print(f"   🔄 Original batch found: 1,043 references")
        
        if total_csv_refs >= 1000:
            print(f"   🎯 SUCCESS! CSV files now contain most/all references")
        else:
            remaining = 1043 - total_csv_refs
            print(f"   ⚠️ Still missing ~{remaining} references - may need another pass")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(fix_missing_references())
