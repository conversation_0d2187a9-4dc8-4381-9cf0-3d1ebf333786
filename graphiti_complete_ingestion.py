#!/usr/bin/env python3
"""
Complete Graphiti ingestion pipeline with intelligent reference extraction.
Combines native Graphiti v0.18.0 with your existing intelligent reference extraction system.
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

# Import Graphiti
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

# Import your existing systems
from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config
from services.intelligent_reference_extractor import get_intelligent_reference_extractor

class GraphitiCompleteProcessor:
    """Complete document processor using Graphiti + intelligent reference extraction."""
    
    def __init__(self):
        self.graphiti = None
        self.config = get_config()
        self.intelligent_extractor = None
        
        # Statistics
        self.stats = {
            'documents_processed': 0,
            'episodes_created': 0,
            'references_extracted': 0,
            'csv_files_created': 0,
            'total_entities_extracted': 0
        }
    
    async def initialize(self):
        """Initialize Graphiti and reference extraction systems."""
        try:
            # Try to connect to your FalkorDB instance using Neo4j-style connection
            # Since FalkorDB is running on port 6379, we'll try to connect there
            falkor_host = os.environ.get('FALKORDB_HOST', 'localhost')
            falkor_port = os.environ.get('FALKORDB_PORT', '6379')

            # For now, let's use a temporary Neo4j connection since FalkorDB driver isn't available
            # We'll set up a local Neo4j instance for testing
            neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
            neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
            neo4j_password = os.environ.get('NEO4J_PASSWORD', 'password')

            logger.info(f"🔄 Attempting to connect to graph database...")
            logger.info(f"   Note: FalkorDB driver not available in current version")
            logger.info(f"   Using Neo4j connection: {neo4j_uri}")

            # Initialize Graphiti with Neo4j (temporary solution)
            self.graphiti = Graphiti(
                uri=neo4j_uri,
                user=neo4j_user,
                password=neo4j_password
            )

            # Build indices and constraints
            await self.graphiti.build_indices_and_constraints()

            # Initialize intelligent reference extractor
            self.intelligent_extractor = get_intelligent_reference_extractor()

            logger.info("✅ Graphiti initialized successfully")
            logger.info(f"   Connected to: {neo4j_uri}")
            logger.info(f"✅ Intelligent reference extractor initialized")
            logger.info(f"   AI-powered extraction: {'✅' if self.intelligent_extractor.ai_available else '❌'}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize: {e}")
            logger.error(f"   Make sure Neo4j is running on bolt://localhost:7687")
            logger.error(f"   Or set NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD environment variables")
            raise
    
    async def process_document_complete(self, file_path: str) -> Dict[str, Any]:
        """Process a document with both Graphiti knowledge graph and reference extraction."""
        try:
            logger.info(f"🔄 Processing document: {file_path}")
            
            # Step 1: Extract text using OCR
            ocr_processor = MistralOCRProcessor()
            ocr_result = await ocr_processor.process_document(file_path)
            
            if not ocr_result.get('success'):
                return {'success': False, 'error': 'OCR processing failed'}
            
            text_content = ocr_result.get('text', '')
            
            if not text_content.strip():
                return {'success': False, 'error': 'No text content extracted'}
            
            # Step 2: Create Graphiti episode (knowledge graph)
            doc_name = Path(file_path).stem
            episode_name = f"Document: {doc_name}"
            
            logger.info(f"📝 Creating Graphiti episode: {episode_name}")
            episode_result = await self.graphiti.add_episode(
                name=episode_name,
                episode_body=text_content,
                source=EpisodeType.text,
                source_description=f"Document processing of {Path(file_path).name}",
                reference_time=datetime.now(timezone.utc)
            )
            
            self.stats['episodes_created'] += 1
            logger.info(f"✅ Graphiti episode created: {episode_result}")
            
            # Step 3: Extract references using intelligent extractor
            logger.info(f"🧠 Extracting references using intelligent system...")
            ref_result = await self.intelligent_extractor.extract_references_comprehensive(
                text_content, 
                Path(file_path).name
            )
            
            # Step 4: Save references to CSV
            csv_path = await self._save_references_to_csv(ref_result, Path(file_path))
            
            self.stats['documents_processed'] += 1
            self.stats['references_extracted'] += ref_result.get('total_found', 0)
            if csv_path:
                self.stats['csv_files_created'] += 1
            
            logger.info(f"✅ Complete processing finished for {file_path}")
            logger.info(f"   Episode UUID: {episode_result}")
            logger.info(f"   References found: {ref_result.get('total_found', 0)}")
            logger.info(f"   CSV saved: {csv_path}")
            
            return {
                'success': True,
                'episode_uuid': str(episode_result),
                'episode_name': episode_name,
                'text_length': len(text_content),
                'references_found': ref_result.get('total_found', 0),
                'csv_path': csv_path,
                'extraction_methods': ref_result.get('extraction_methods', {}),
                'confidence_score': ref_result.get('confidence_score', 0.0),
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing document {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _save_references_to_csv(self, ref_result: Dict[str, Any], file_path: Path) -> Optional[str]:
        """Save references to CSV file using your existing format."""
        try:
            # Create references directory
            ref_dir = Path("references")
            ref_dir.mkdir(exist_ok=True)
            
            # Generate CSV filename (matching your existing pattern)
            doc_id = file_path.stem.split('_')[0] if '_' in file_path.stem else file_path.stem
            csv_filename = f"{doc_id}_{file_path.stem}_intelligent_references.csv"
            csv_path = ref_dir / csv_filename
            
            # Get references from result
            references = ref_result.get('references', [])
            
            if not references:
                logger.warning(f"No references to save for {file_path.name}")
                return None
            
            # Save to CSV with your existing format
            import csv
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'reference_number', 'text', 'authors', 'title', 'journal', 
                    'year', 'doi', 'pmid', 'url', 'confidence', 
                    'extraction_method', 'source_section'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, ref in enumerate(references, 1):
                    if isinstance(ref, dict):
                        ref_text = ref.get('text', str(ref))
                        confidence = ref.get('confidence', 0.9)
                    else:
                        ref_text = str(ref)
                        confidence = 0.9
                    
                    writer.writerow({
                        'reference_number': i,
                        'text': ref_text,
                        'authors': '',  # Could be parsed from ref_text
                        'title': '',    # Could be parsed from ref_text
                        'journal': '',  # Could be parsed from ref_text
                        'year': '',     # Could be parsed from ref_text
                        'doi': '',      # Could be parsed from ref_text
                        'pmid': '',     # Could be parsed from ref_text
                        'url': '',      # Could be parsed from ref_text
                        'confidence': confidence,
                        'extraction_method': 'intelligent_ai',
                        'source_section': 'document'
                    })
            
            logger.info(f"✅ References saved to CSV: {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"❌ Error saving references to CSV: {e}")
            return None
    
    async def search_knowledge_graph(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search the knowledge graph using Graphiti's hybrid search."""
        try:
            logger.info(f"🔍 Searching knowledge graph for: {query}")
            
            results = await self.graphiti.search(query, limit=limit)
            
            search_results = []
            for result in results:
                search_results.append({
                    'uuid': result.uuid,
                    'fact': result.fact,
                    'source_node_uuid': result.source_node_uuid,
                    'target_node_uuid': result.target_node_uuid,
                    'valid_at': result.valid_at.isoformat() if result.valid_at else None,
                    'invalid_at': result.invalid_at.isoformat() if result.invalid_at else None,
                    'score': getattr(result, 'score', None)
                })
            
            logger.info(f"✅ Found {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"❌ Search error: {e}")
            return []
    
    async def process_directory(self, directory_path: str, file_extensions: List[str] = None) -> Dict[str, Any]:
        """Process all documents in a directory."""
        if file_extensions is None:
            file_extensions = ['.pdf', '.txt', '.docx']
        
        directory = Path(directory_path)
        if not directory.exists():
            return {'success': False, 'error': f'Directory not found: {directory_path}'}
        
        # Find all matching files
        files = []
        for ext in file_extensions:
            files.extend(directory.glob(f'**/*{ext}'))
        
        if not files:
            return {'success': False, 'error': f'No files found with extensions {file_extensions}'}
        
        logger.info(f"🔄 Processing {len(files)} files from {directory_path}")
        
        results = []
        successful = 0
        failed = 0
        total_references = 0
        
        for file_path in files:
            try:
                result = await self.process_document_complete(str(file_path))
                results.append({
                    'file': str(file_path),
                    'success': result['success'],
                    'episode_uuid': result.get('episode_uuid'),
                    'references_found': result.get('references_found', 0),
                    'csv_path': result.get('csv_path'),
                    'error': result.get('error')
                })
                
                if result['success']:
                    successful += 1
                    total_references += result.get('references_found', 0)
                else:
                    failed += 1
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                results.append({
                    'file': str(file_path),
                    'success': False,
                    'error': str(e)
                })
                failed += 1
        
        return {
            'success': True,
            'files_processed': len(files),
            'successful': successful,
            'failed': failed,
            'total_references_extracted': total_references,
            'results': results
        }
    
    def print_stats(self):
        """Print processing statistics."""
        print(f"\n📊 COMPLETE GRAPHITI + REFERENCE PROCESSING STATISTICS:")
        print(f"  📄 Documents processed: {self.stats['documents_processed']}")
        print(f"  📝 Graphiti episodes created: {self.stats['episodes_created']}")
        print(f"  📚 References extracted: {self.stats['references_extracted']}")
        print(f"  📋 CSV files created: {self.stats['csv_files_created']}")
        print(f"  🎯 Using: Graphiti v0.18.0 + Intelligent Reference Extraction")
        print(f"  🤖 AI-powered extraction: {'✅' if self.intelligent_extractor and self.intelligent_extractor.ai_available else '❌'}")
    
    async def close(self):
        """Close Graphiti connection."""
        if self.graphiti:
            await self.graphiti.close()
            logger.info("✅ Graphiti connection closed")

async def main():
    """Main processing function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python graphiti_complete_ingestion.py <document_path>")
        print("  Directory:   python graphiti_complete_ingestion.py --dir <directory_path>")
        print("  Search:      python graphiti_complete_ingestion.py --search '<query>'")
        print("  Stats:       python graphiti_complete_ingestion.py --stats")
        return
    
    processor = GraphitiCompleteProcessor()
    
    try:
        await processor.initialize()
        
        if sys.argv[1] == '--dir':
            if len(sys.argv) < 3:
                print("Error: Directory path required")
                return
            
            directory_path = sys.argv[2]
            result = await processor.process_directory(directory_path)
            
            if result['success']:
                print(f"✅ Batch processing complete:")
                print(f"  📁 Files processed: {result['files_processed']}")
                print(f"  ✅ Successful: {result['successful']}")
                print(f"  ❌ Failed: {result['failed']}")
                print(f"  📚 Total references: {result['total_references_extracted']}")
                processor.print_stats()
            else:
                print(f"❌ Batch processing failed: {result.get('error')}")
        
        elif sys.argv[1] == '--search':
            if len(sys.argv) < 3:
                print("Error: Search query required")
                return
            
            query = sys.argv[2]
            results = await processor.search_knowledge_graph(query)
            
            print(f"🔍 Search Results for: '{query}'")
            print("=" * 60)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['fact']}")
                    print(f"   UUID: {result['uuid']}")
                    if result['valid_at']:
                        print(f"   Valid from: {result['valid_at']}")
                    print()
            else:
                print("No results found.")
        
        else:
            # Single document processing
            document_path = sys.argv[1]
            result = await processor.process_document_complete(document_path)
            
            if result['success']:
                print(f"✅ Successfully processed: {document_path}")
                print(f"   Episode UUID: {result['episode_uuid']}")
                print(f"   Text length: {result['text_length']} characters")
                print(f"   References found: {result['references_found']}")
                print(f"   CSV saved: {result['csv_path']}")
                print(f"   Confidence: {result['confidence_score']:.2f}")
                processor.print_stats()
            else:
                print(f"❌ Failed to process: {document_path}")
                print(f"   Error: {result.get('error')}")
    
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())
