#!/usr/bin/env python3
"""
Test script to verify AI-powered reference statistics are working correctly.
"""

import asyncio
from services.reference_service import get_reference_statistics

async def test_reference_statistics():
    """Test the reference statistics function."""
    
    print("🧪 Testing AI-Powered Reference Statistics")
    print("=" * 50)
    
    try:
        # Get statistics
        print("📊 Loading reference statistics...")
        stats = await get_reference_statistics()
        
        print(f"✅ Statistics loaded successfully")
        
        # Display key statistics
        print(f"\n📊 KEY STATISTICS:")
        print(f"   Total References: {stats.get('total_references', 0):,}")
        print(f"   AI-Enhanced: {stats.get('ai_enhanced_references', 0):,}")
        print(f"   High Quality (>0.7): {stats.get('high_quality_references', 0):,}")
        print(f"   Documents with References: {stats.get('documents_with_references', 0)}")
        
        # Quality distribution
        quality_dist = stats.get('quality_distribution', {})
        print(f"\n🎯 QUALITY DISTRIBUTION:")
        total_refs = stats.get('total_references', 1)
        print(f"   High (0.7-1.0): {quality_dist.get('high', 0):,} ({quality_dist.get('high', 0)/total_refs*100:.1f}%)")
        print(f"   Medium (0.4-0.7): {quality_dist.get('medium', 0):,} ({quality_dist.get('medium', 0)/total_refs*100:.1f}%)")
        print(f"   Low (0.1-0.4): {quality_dist.get('low', 0):,} ({quality_dist.get('low', 0)/total_refs*100:.1f}%)")
        print(f"   Unscored (0.0): {quality_dist.get('unscored', 0):,} ({quality_dist.get('unscored', 0)/total_refs*100:.1f}%)")
        
        # Extraction methods
        extraction_methods = stats.get('references_by_extraction_method', {})
        print(f"\n🔧 EXTRACTION METHODS:")
        for method, count in extraction_methods.items():
            print(f"   {method}: {count:,}")
        
        # Top documents
        doc_refs = stats.get('references_by_document', {})
        top_docs = sorted(doc_refs.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"\n📄 TOP DOCUMENTS BY REFERENCES:")
        for i, (doc, count) in enumerate(top_docs, 1):
            doc_name = doc.split('_')[-1] if '_' in doc else doc
            print(f"   {i}. {doc_name}: {count} references")
        
        print(f"\n✅ Reference statistics test completed successfully!")
        
        # Format for UI
        ui_stats = {
            "total_count": stats.get('total_references', 0),
            "ai_enhanced_count": stats.get('ai_enhanced_references', 0),
            "high_quality_count": stats.get('high_quality_references', 0),
            "unique_documents": stats.get('documents_with_references', 0),
            "quality_distribution": quality_dist
        }
        
        print(f"\n🎨 UI-FORMATTED STATS:")
        print(f"   Total References: {ui_stats['total_count']:,}")
        print(f"   AI-Enhanced: {ui_stats['ai_enhanced_count']:,}")
        print(f"   High Quality: {ui_stats['high_quality_count']:,}")
        print(f"   Documents: {ui_stats['unique_documents']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reference statistics: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_reference_statistics())
    if success:
        print(f"\n🎉 Statistics test passed! AI-powered metrics are working correctly.")
    else:
        print(f"\n💥 Statistics test failed! Check the error messages above.")
