#!/usr/bin/env python3
"""
Test if cocoa-related content exists in the database.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.database_service import get_falkordb_adapter

async def test_cocoa_content():
    """Test if cocoa-related content exists."""
    try:
        adapter = await get_falkordb_adapter()
        
        # Test different cocoa-related terms
        search_terms = ["cocoa", "chocolate", "cacao", "flavonoid", "polyphenol"]
        
        print("🔍 SEARCHING FOR COCOA-RELATED CONTENT")
        print("=" * 50)
        
        for term in search_terms:
            query = f"""
            MATCH (c:Chunk)
            WHERE toLower(c.text) CONTAINS toLower('{term}')
            RETURN count(c) as count
            """
            
            result = adapter.execute_cypher(query)
            count = result[1][0][0] if result and len(result) > 1 and len(result[1]) > 0 else 0
            
            print(f"📊 '{term}': {count} chunks found")
            
            if count > 0:
                # Get a sample
                sample_query = f"""
                MATCH (c:Chunk)
                WHERE toLower(c.text) CONTAINS toLower('{term}')
                RETURN c.text
                LIMIT 1
                """
                
                sample_result = adapter.execute_cypher(sample_query)
                if sample_result and len(sample_result) > 1 and len(sample_result[1]) > 0:
                    sample_text = sample_result[1][0][0][:200]
                    print(f"   Sample: {sample_text}...")
        
        # Test the keyword extraction for "cocoa and human health"
        print(f"\n🔍 TESTING KEYWORD EXTRACTION")
        print("=" * 40)
        
        from services.qa_service import extract_keywords
        
        test_question = "cocoa and human health"
        keywords = extract_keywords(test_question)
        print(f"Question: '{test_question}'")
        print(f"Keywords: {keywords}")
        
        # Test each keyword individually
        for keyword in keywords:
            query = f"""
            MATCH (c:Chunk)
            WHERE toLower(c.text) CONTAINS toLower('{keyword}')
            RETURN count(c) as count
            """
            
            result = adapter.execute_cypher(query)
            count = result[1][0][0] if result and len(result) > 1 and len(result[1]) > 0 else 0
            print(f"   '{keyword}': {count} chunks")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_cocoa_content())
