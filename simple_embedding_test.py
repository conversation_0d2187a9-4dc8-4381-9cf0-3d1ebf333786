#!/usr/bin/env python3
"""
Simple test to see what's happening with embedding generation.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def simple_embedding_test():
    """Simple test of embedding queries."""
    
    print("🔍 Simple embedding test...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # Get latest episode
        query = '''
        MATCH (ep:Episode)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN ep.uuid as uuid
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or not result[1]:
            print("❌ No episodes found")
            return
            
        episode_id = result[1][0][0]
        print(f"✅ Episode: {episode_id}")
        
        # Test 1: Count all facts
        print("\n1. Counting all facts...")
        all_facts_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN count(f) as total_facts
        '''
        all_result = adapter.execute_cypher(all_facts_query)
        total_facts = all_result[1][0][0] if all_result and len(all_result) > 1 else 0
        print(f"✅ Total facts: {total_facts}")
        
        # Test 2: Check embedding property existence
        print("\n2. Checking embedding properties...")
        embedding_check_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN 
            count(f) as total,
            count(f.embedding) as has_embedding_property,
            sum(CASE WHEN f.embedding IS NULL THEN 1 ELSE 0 END) as null_embeddings,
            sum(CASE WHEN f.embedding = '' THEN 1 ELSE 0 END) as empty_embeddings,
            sum(CASE WHEN NOT EXISTS(f.embedding) THEN 1 ELSE 0 END) as no_embedding_property
        '''
        embedding_result = adapter.execute_cypher(embedding_check_query)
        
        if embedding_result and len(embedding_result) > 1 and embedding_result[1]:
            row = embedding_result[1][0]
            print(f"   Total facts: {row[0]}")
            print(f"   Has embedding property: {row[1]}")
            print(f"   NULL embeddings: {row[2]}")
            print(f"   Empty embeddings: {row[3]}")
            print(f"   No embedding property: {row[4]}")
        
        # Test 3: Test the exact embedding processor query (NEW VERSION)
        print("\n3. Testing NEW embedding processor query...")
        processor_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        WHERE NOT EXISTS(f.has_embedding) OR f.has_embedding IS NULL OR f.has_embedding = false
        RETURN count(f) as facts_needing_embeddings
        '''
        processor_result = adapter.execute_cypher(processor_query)
        facts_needing = processor_result[1][0][0] if processor_result and len(processor_result) > 1 else 0
        print(f"✅ Facts needing embeddings: {facts_needing}")
        
        # Test 4: Show first fact details
        print("\n4. First fact details...")
        first_fact_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid, f.has_embedding, EXISTS(f.has_embedding), f.body
        ORDER BY f.chunk_index
        LIMIT 1
        '''
        first_fact_result = adapter.execute_cypher(first_fact_query)
        
        if first_fact_result and len(first_fact_result) > 1 and first_fact_result[1]:
            row = first_fact_result[1][0]
            print(f"   UUID: {row[0]}")
            print(f"   Has embedding flag: {repr(row[1])}")
            print(f"   Has embedding property exists: {row[2]}")
            print(f"   Body length: {len(row[3]) if row[3] else 0}")
            
        # Test 5: Check Redis Vector Search for embeddings
        print("\n5. Checking Redis Vector Search...")
        try:
            import redis
            # Check Redis Vector Search (port 6380)
            r_vector = redis.Redis(host='localhost', port=6380, decode_responses=True)
            r_vector.ping()

            # Check for fact embeddings
            fact_keys = r_vector.keys("*embedding*")
            print(f"✅ Redis Vector Search embeddings: {len(fact_keys)}")

            if fact_keys:
                print("   Sample keys:")
                for key in fact_keys[:3]:
                    print(f"     {key}")

            # Also check regular Redis (port 6379)
            print("\n   Also checking regular Redis (port 6379)...")
            r_regular = redis.Redis(host='localhost', port=6379, decode_responses=True)
            r_regular.ping()
            regular_keys = r_regular.keys("fact:*")
            print(f"   Regular Redis fact keys: {len(regular_keys)}")

        except Exception as e:
            print(f"❌ Redis check failed: {e}")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_embedding_test())
