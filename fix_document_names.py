#!/usr/bin/env python3
"""
Fix document names issue - documents showing as "Unknown" instead of real names.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter

logger = get_logger(__name__)

async def test_document_service_step_by_step():
    """Test the document service step by step to find the issue."""
    try:
        logger.info("🔍 DEBUGGING DOCUMENT NAMES ISSUE")
        logger.info("=" * 50)
        
        adapter = await get_falkordb_adapter()
        
        # Step 1: Test raw query
        logger.info("Step 1: Testing raw document query...")
        query = """
        MATCH (e:Document)
        RETURN e.id as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at
        ORDER BY e.processed_at DESC
        LIMIT 3
        """
        
        result = adapter.execute_cypher(query)
        logger.info(f"Raw query result: {result}")
        
        if result and len(result) > 1:
            headers = result[0]
            logger.info(f"Headers: {headers}")
            
            for i, row in enumerate(result[1]):
                logger.info(f"\nDocument {i+1} raw data:")
                for j, header in enumerate(headers):
                    value = row[j] if j < len(row) else None
                    logger.info(f"   {header}: '{value}' (type: {type(value)})")
        
        # Step 2: Test document service function
        logger.info("\nStep 2: Testing document service function...")
        from services.document_service import get_document_list
        
        doc_list = await get_document_list(page=1, page_size=3)
        logger.info(f"Document service result:")
        logger.info(f"   Total: {doc_list.total}")
        logger.info(f"   Documents: {len(doc_list.documents)}")
        
        for i, doc in enumerate(doc_list.documents):
            logger.info(f"\nDocument {i+1} processed:")
            logger.info(f"   UUID: {doc.uuid}")
            logger.info(f"   Filename: '{doc.filename}'")
            logger.info(f"   File type: {doc.file_type}")
            logger.info(f"   Upload date: {doc.upload_date}")
        
        # Step 3: Test API endpoint
        logger.info("\nStep 3: Testing API endpoint...")
        import requests
        
        try:
            response = requests.get('http://localhost:9753/api/documents?page=1&page_size=3')
            if response.status_code == 200:
                api_data = response.json()
                logger.info(f"API response:")
                logger.info(f"   Total: {api_data.get('total', 0)}")
                logger.info(f"   Documents: {len(api_data.get('documents', []))}")
                
                for i, doc in enumerate(api_data.get('documents', [])[:3]):
                    logger.info(f"\nAPI Document {i+1}:")
                    logger.info(f"   UUID: {doc.get('uuid', 'N/A')}")
                    logger.info(f"   Filename: '{doc.get('filename', 'N/A')}'")
                    logger.info(f"   File type: {doc.get('file_type', 'N/A')}")
            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"API test failed: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

async def fix_document_names_directly():
    """Try to fix document names by updating the service logic."""
    try:
        logger.info("\n🔧 ATTEMPTING TO FIX DOCUMENT NAMES")
        logger.info("=" * 50)
        
        # The issue might be in the document processing logic
        # Let's create a simple test to see what's happening
        
        adapter = await get_falkordb_adapter()
        
        # Get a sample document with all its data
        query = """
        MATCH (e:Document)
        RETURN e.id, e.name, e.file_path, e.processed_at, e.text_length
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        if result and len(result) > 1 and len(result[1]) > 0:
            row = result[1][0]
            logger.info(f"Sample document data:")
            logger.info(f"   ID: {row[0]}")
            logger.info(f"   Name: '{row[1]}'")
            logger.info(f"   File path: '{row[2]}'")
            logger.info(f"   Processed at: {row[3]}")
            logger.info(f"   Text length: {row[4] if len(row) > 4 else 'N/A'}")
            
            # Extract filename from path if name is problematic
            if row[2]:  # file_path exists
                import os
                filename_from_path = os.path.basename(row[2])
                filename_clean = os.path.splitext(filename_from_path)[0]
                logger.info(f"   Extracted filename: '{filename_clean}'")
                
                # Check if the name field is actually the issue
                if row[1] and row[1].strip():
                    logger.info("✅ Name field has data - issue might be elsewhere")
                else:
                    logger.info("❌ Name field is empty - this is the problem")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing document names: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function."""
    logger.info("🚀 FIXING DOCUMENT NAMES ISSUE")
    logger.info("=" * 50)
    
    # Step 1: Debug the issue
    debug_success = await test_document_service_step_by_step()
    
    if debug_success:
        # Step 2: Try to fix it
        fix_success = await fix_document_names_directly()
        
        if fix_success:
            logger.info("\n✅ Document names debugging completed!")
            logger.info("   Check the logs above to identify the exact issue")
        else:
            logger.error("\n❌ Failed to fix document names")
    else:
        logger.error("\n❌ Failed to debug document names issue")

if __name__ == "__main__":
    asyncio.run(main())
