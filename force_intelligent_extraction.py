#!/usr/bin/env python3
"""
Force the Intelligent Reference Extractor to process the kidney document directly.
"""

import asyncio
import sys
from pathlib import Path
from services.intelligent_reference_extractor import get_intelligent_reference_extractor
from utils.mistral_ocr import MistralOCRProcessor
from utils.logging_utils import get_logger
import pandas as pd

logger = get_logger(__name__)

async def force_intelligent_extraction():
    """Force intelligent extraction on the kidney document."""
    print("🔄 FORCING Intelligent Reference Extraction on Kidney Document")
    print("=" * 70)
    
    # Find the kidney document
    document_path = Path("uploads/5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal O<PERSON>ki.pdf")
    
    if not document_path.exists():
        print(f"❌ Document not found: {document_path}")
        return False
    
    print(f"📄 Found document: {document_path}")
    print(f"📊 File size: {document_path.stat().st_size:,} bytes")
    
    try:
        # Step 1: Extract text using OCR
        print(f"\n🔍 Step 1: Extracting text from PDF...")
        ocr_processor = MistralOCRProcessor()

        # Extract text using OCR
        ocr_result = await ocr_processor.process_pdf(str(document_path))
        
        if not ocr_result.get('success', False):
            print(f"❌ OCR extraction failed: {ocr_result.get('error', 'Unknown error')}")
            return False
        
        extracted_text = ocr_result.get('text', '')
        print(f"✅ Text extracted: {len(extracted_text):,} characters")
        
        if len(extracted_text) < 1000:
            print(f"⚠️ Warning: Very short text extracted, may indicate OCR issues")
        
        # Step 2: Apply Intelligent Reference Extractor
        print(f"\n🧠 Step 2: Applying Intelligent Reference Extractor...")
        extractor = get_intelligent_reference_extractor()
        
        result = await extractor.extract_references_comprehensive(
            extracted_text, 
            document_path.name
        )
        
        print(f"\n✅ Intelligent extraction completed!")
        print(f"📊 Total references found: {result['total_found']}")
        print(f"🎯 Confidence score: {result['confidence_score']}")
        print(f"🔧 Extraction methods: {result['extraction_methods']}")
        
        # Step 3: Save results to new CSV
        csv_filename = f"intelligent_references_{document_path.stem}.csv"
        csv_path = Path("references") / csv_filename
        
        print(f"\n💾 Step 3: Saving results to CSV...")
        
        # Create DataFrame
        references_data = []
        for i, ref_data in enumerate(result['references'], 1):
            references_data.append({
                'reference_number': i,
                'text': ref_data['text'],
                'confidence': ref_data['confidence'],
                'extraction_method': 'intelligent_extractor',
                'source_section': 'document_body'
            })
        
        df = pd.DataFrame(references_data)
        
        # Ensure references directory exists
        csv_path.parent.mkdir(exist_ok=True)
        
        # Save to CSV
        df.to_csv(csv_path, index=False)
        print(f"✅ References saved to: {csv_path}")
        
        # Step 4: Display sample references
        print(f"\n📋 Sample references found:")
        for i, ref_data in enumerate(result['references'][:10], 1):
            ref_text = ref_data['text']
            print(f"{i:2d}. {ref_text[:120]}{'...' if len(ref_text) > 120 else ''}")
        
        if result['total_found'] > 10:
            print(f"... and {result['total_found'] - 10} more references")
        
        # Step 5: Check for expected references
        print(f"\n🎯 Step 5: Checking for expected references...")
        expected_authors = [
            "Australian Institute of Health and Welfare",
            "Liyanage T",
            "Zoccali, C.",
            "Hill, N. R.",
            "Miwa, K.",
            "Bowe B",
            "Mahalingasivam V",
            "Huang C",
            "Hsu CM",
            "Gur E"
        ]
        
        found_authors = []
        all_ref_text = ' '.join([ref['text'] for ref in result['references']])
        
        for author in expected_authors:
            if author in all_ref_text:
                found_authors.append(author)
        
        print(f"📊 Expected authors found: {len(found_authors)}/{len(expected_authors)}")
        for author in found_authors:
            print(f"  ✅ {author}")
        
        missing_authors = [author for author in expected_authors if author not in found_authors]
        if missing_authors:
            print(f"\n❌ Missing authors:")
            for author in missing_authors:
                print(f"  ❌ {author}")
        
        # Step 6: Performance assessment
        print(f"\n🎯 Performance Assessment:")
        if result['total_found'] >= 40:
            print(f"🎉 EXCELLENT: Found {result['total_found']} references!")
            print(f"📈 This is a {result['total_found']/6:.1f}x improvement over the previous 6 references")
        elif result['total_found'] >= 30:
            print(f"✅ VERY GOOD: Found {result['total_found']} references")
            print(f"📈 This is a {result['total_found']/6:.1f}x improvement over the previous 6 references")
        elif result['total_found'] >= 20:
            print(f"✅ GOOD: Found {result['total_found']} references")
            print(f"📈 This is a {result['total_found']/6:.1f}x improvement over the previous 6 references")
        else:
            print(f"⚠️ NEEDS IMPROVEMENT: Found {result['total_found']} references")
            print(f"Expected 40+ based on your reference list")
        
        # Step 7: Compare with old results
        old_csv_path = Path("references/5d2b67d0-76d5-4e63-abb2-b230c9a8a210_5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal Oseki_aggressive_references.csv")
        if old_csv_path.exists():
            print(f"\n📊 Comparison with old extractor:")
            old_df = pd.read_csv(old_csv_path)
            print(f"  Old extractor: {len(old_df)} references (mostly DOIs/URLs)")
            print(f"  New extractor: {result['total_found']} references (full citations)")
            print(f"  Improvement: {result['total_found'] - len(old_df):+d} more references")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during forced extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function."""
    print("🚀 FORCING Intelligent Reference Extraction")
    print("This will bypass the unified pipeline and directly apply the new extractor")
    print("=" * 70)
    
    success = await force_intelligent_extraction()
    
    if success:
        print(f"\n🎯 SUCCESS!")
        print(f"The Intelligent Reference Extractor has been applied directly.")
        print(f"Check the new CSV file for the complete list of extracted references.")
        print(f"This should now find the full bibliographic references you listed.")
    else:
        print(f"\n❌ FAILED!")
        print(f"Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
