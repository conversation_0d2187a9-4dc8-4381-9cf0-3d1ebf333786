#!/usr/bin/env python3
"""
Test if MedGemma is working for entity extraction.
"""

import requests
import json
import time

def test_medgemma_simple():
    """Test MedGemma with a simple medical text."""
    print("🧪 TESTING MEDGEMMA ENTITY EXTRACTION")
    print("=" * 50)
    
    # Simple medical text
    test_text = """
    Ginger contains gingerols and shogaols. These compounds have anti-inflammatory properties 
    and may help reduce nausea in cancer patients. The recommended dosage is 1-3 grams per day.
    """
    
    print(f"📝 Test Text: {test_text.strip()}")
    
    # Create a simple prompt for entity extraction
    prompt = f"""
Extract medical entities from this text. Return only a JSON object with this format:
{{
  "entities": [
    {{"name": "entity_name", "type": "entity_type", "confidence": 0.95}}
  ]
}}

Entity types: Herb, Compound, Disease, Symptom, Treatment, Medication, Dosage, Process

Text: {test_text.strip()}

JSON:"""
    
    print(f"\n🔄 Sending request to MedGemma...")
    
    try:
        start_time = time.time()
        
        # Make request to Ollama
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                "model": "alibayram/medgemma:4b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 500
                }
            },
            timeout=120  # Give it 2 minutes
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"✅ MedGemma Response:")
            print(f"   Response length: {len(response_text)} characters")
            print(f"   Raw response: {response_text}")
            
            # Try to extract JSON
            try:
                import re
                
                # Look for JSON object in the response
                json_match = re.search(r'\{[^{}]*"entities"[^{}]*\[[^\]]*\][^{}]*\}', response_text, re.DOTALL)
                
                if json_match:
                    json_str = json_match.group()
                    print(f"\n📊 Extracted JSON: {json_str}")
                    
                    try:
                        parsed = json.loads(json_str)
                        entities = parsed.get('entities', [])
                        
                        print(f"\n🎯 EXTRACTED ENTITIES ({len(entities)} found):")
                        for i, entity in enumerate(entities):
                            name = entity.get('name', 'Unknown')
                            entity_type = entity.get('type', 'Unknown')
                            confidence = entity.get('confidence', 0)
                            print(f"   {i+1}. {name} ({entity_type}) - confidence: {confidence}")
                        
                        # Analyze quality
                        print(f"\n📈 QUALITY ANALYSIS:")
                        expected_entities = ['ginger', 'gingerols', 'shogaols', 'nausea', 'cancer']
                        found_names = [e.get('name', '').lower() for e in entities]
                        
                        matches = 0
                        for expected in expected_entities:
                            if any(expected in name for name in found_names):
                                matches += 1
                                print(f"   ✅ Found: {expected}")
                            else:
                                print(f"   ❌ Missing: {expected}")
                        
                        accuracy = matches / len(expected_entities) * 100
                        print(f"\n🎯 Accuracy: {accuracy:.1f}% ({matches}/{len(expected_entities)} expected entities found)")
                        
                        if accuracy >= 80:
                            print("   🏆 EXCELLENT - MedGemma is working very well!")
                        elif accuracy >= 60:
                            print("   ✅ GOOD - MedGemma is working adequately")
                        elif accuracy >= 40:
                            print("   ⚠️ FAIR - MedGemma is working but could be better")
                        else:
                            print("   ❌ POOR - MedGemma may need tuning")
                        
                        return {
                            'success': True,
                            'time': processing_time,
                            'entity_count': len(entities),
                            'accuracy': accuracy,
                            'entities': entities
                        }
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON parsing error: {e}")
                        return {'success': False, 'error': f'JSON parsing error: {e}'}
                else:
                    print(f"⚠️ No JSON found in response")
                    return {'success': False, 'error': 'No JSON in response'}
                    
            except Exception as e:
                print(f"❌ Error processing response: {e}")
                return {'success': False, 'error': f'Processing error: {e}'}
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out (>120 seconds)")
        return {'success': False, 'error': 'Timeout'}
    except Exception as e:
        print(f"❌ Request error: {e}")
        return {'success': False, 'error': str(e)}

def compare_with_current_system():
    """Compare MedGemma performance with what we know about the current system."""
    print(f"\n📊 COMPARISON WITH CURRENT SYSTEM")
    print("=" * 50)
    
    print(f"📋 Current System (OpenRouter Llama Maverick):")
    print(f"   ✅ Extracting 80-758 entities per document")
    print(f"   ✅ Average ~374 entities per document")
    print(f"   ✅ Good medical entity recognition")
    print(f"   ⚠️ Requires internet connection")
    print(f"   ⚠️ API costs per request")
    
    # Test MedGemma
    medgemma_result = test_medgemma_simple()
    
    print(f"\n📋 MedGemma Results:")
    if medgemma_result.get('success'):
        time_taken = medgemma_result.get('time', 0)
        entity_count = medgemma_result.get('entity_count', 0)
        accuracy = medgemma_result.get('accuracy', 0)
        
        print(f"   ✅ Processing time: {time_taken:.2f} seconds")
        print(f"   ✅ Entities found: {entity_count}")
        print(f"   ✅ Accuracy: {accuracy:.1f}%")
        print(f"   ✅ Local processing (no internet needed)")
        print(f"   ✅ No API costs")
        
        # Recommendation
        print(f"\n💡 RECOMMENDATION:")
        if accuracy >= 70 and time_taken <= 30:
            print("   🏆 SWITCH TO MEDGEMMA!")
            print("   MedGemma shows good performance and offers local processing benefits")
        elif accuracy >= 50:
            print("   ⚠️ MEDGEMMA IS VIABLE")
            print("   Consider testing with real documents before switching")
        else:
            print("   ❌ STICK WITH OPENROUTER")
            print("   MedGemma performance is not sufficient for production use")
    else:
        error = medgemma_result.get('error', 'Unknown error')
        print(f"   ❌ Failed to run: {error}")
        print(f"\n💡 RECOMMENDATION:")
        print("   ❌ STICK WITH OPENROUTER")
        print("   MedGemma is not working properly")

def main():
    """Main test function."""
    print("🚀 MEDGEMMA FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Run the comparison
    compare_with_current_system()

if __name__ == "__main__":
    main()
