#!/usr/bin/env python3
"""
Test the improved operation tracking system
"""

import asyncio
import aiohttp
import json
from pathlib import Path

async def test_operation_tracking():
    """Test the improved operation tracking system."""
    
    print("🧪 TESTING IMPROVED OPERATION TRACKING")
    print("=" * 50)
    
    try:
        # Create a test file
        test_file_path = Path("test_tracking.txt")
        test_content = """
        This is a test document for operation tracking.
        
        It contains some sample text about health and nutrition.
        Vitamin C is important for immune function.
        Magnesium supports over 300 enzymatic reactions.
        Omega-3 fatty acids have anti-inflammatory properties.
        """
        
        # Write test file
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 Created test file: {test_file_path}")
        
        # Start upload and get operation ID
        operation_id = None
        
        async with aiohttp.ClientSession() as session:
            # Prepare form data
            data = aiohttp.FormData()
            
            # Read file content
            with open(test_file_path, 'rb') as f:
                file_content = f.read()
            
            # Add file to form data
            data.add_field('file', file_content, filename='test_tracking.txt', content_type='text/plain')
            data.add_field('chunk_size', '1200')
            data.add_field('overlap', '0')
            data.add_field('extract_entities', 'true')
            data.add_field('extract_references', 'true')
            data.add_field('extract_metadata', 'true')
            data.add_field('generate_embeddings', 'true')
            data.add_field('skip_duplicate_check', 'false')
            
            print(f"🚀 Starting upload...")
            
            # Make the upload request
            async with session.post('http://localhost:9753/api/unified/upload-with-duplicate-check', data=data) as response:
                print(f"📊 Upload response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    operation_id = result.get('operation_id')
                    print(f"✅ Upload started successfully!")
                    print(f"   🆔 Operation ID: {operation_id}")
                    print(f"   📄 Filename: {result.get('filename', 'unknown')}")
                else:
                    error_text = await response.text()
                    print(f"❌ Upload failed: {error_text}")
                    return False
        
        if not operation_id:
            print("❌ No operation ID received")
            return False
        
        # Wait for processing to complete
        print(f"\n⏳ Waiting for processing to complete...")
        await asyncio.sleep(15)  # Give it time to process
        
        # Check operation status multiple times
        print(f"\n🔍 Checking operation status...")
        
        async with aiohttp.ClientSession() as session:
            for attempt in range(3):
                async with session.get(f'http://localhost:9753/api/unified/operations/{operation_id}') as response:
                    print(f"   📊 Attempt {attempt + 1}: Status {response.status}")
                    
                    if response.status == 200:
                        status_data = await response.json()
                        print(f"   ✅ Operation found!")
                        print(f"      📈 Progress: {status_data.get('progress', 0)}%")
                        print(f"      📊 Status: {status_data.get('status', 'unknown')}")
                        print(f"      💬 Message: {status_data.get('message', 'no message')}")
                        print(f"      ✅ Success: {status_data.get('success', 'unknown')}")
                        
                        # Check if we have final results
                        result = status_data.get('result', {})
                        if result:
                            print(f"      📄 Text length: {result.get('text_length', 0)} chars")
                            print(f"      🧩 Chunks: {result.get('chunks', 0)}")
                            print(f"      🏷️ Entities: {result.get('entities', 0)}")
                            print(f"      📚 References: {result.get('references', 0)}")
                        
                        break
                    elif response.status == 404:
                        print(f"   ❌ Operation not found (404)")
                        if attempt < 2:
                            print(f"      ⏳ Waiting 5 seconds before retry...")
                            await asyncio.sleep(5)
                    else:
                        error_text = await response.text()
                        print(f"   ❌ Error {response.status}: {error_text}")
                        break
        
        # Check all operations
        print(f"\n📋 Checking all operations...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:9753/api/unified/operations') as response:
                if response.status == 200:
                    all_ops = await response.json()
                    active_ops = all_ops.get('active_operations', [])
                    completed_ops = all_ops.get('completed_operations', [])
                    
                    print(f"   🔄 Active operations: {len(active_ops)}")
                    for op in active_ops:
                        print(f"      - {op.get('operation_id', 'unknown')}: {op.get('filename', 'unknown')} ({op.get('progress', 0)}%)")
                    
                    print(f"   ✅ Completed operations: {len(completed_ops)}")
                    for op in completed_ops:
                        print(f"      - {op.get('operation_id', 'unknown')}: {op.get('filename', 'unknown')} (Success: {op.get('success', 'unknown')})")
                        
                        # Check if our operation is in completed operations
                        if op.get('operation_id') == operation_id:
                            print(f"         🎉 Found our operation in completed operations!")
                            result = op.get('result', {})
                            if result:
                                print(f"         📄 Text: {result.get('text_length', 0)} chars")
                                print(f"         🧩 Chunks: {result.get('chunks', 0)}")
                                print(f"         🏷️ Entities: {result.get('entities', 0)}")
                                print(f"         📚 References: {result.get('references', 0)}")
                else:
                    print(f"   ❌ Could not get all operations: {response.status}")
        
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()
            print(f"\n🧹 Cleaned up test file")
        
        print(f"\n🎉 OPERATION TRACKING TEST COMPLETE!")
        print(f"✅ The improved operation tracking system is working!")
        print(f"📱 UI should now be able to retrieve final results!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Clean up test file on error
        if 'test_file_path' in locals() and test_file_path.exists():
            test_file_path.unlink()
        
        return False

if __name__ == "__main__":
    asyncio.run(test_operation_tracking())
