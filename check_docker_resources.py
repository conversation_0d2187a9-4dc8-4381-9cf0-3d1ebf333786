#!/usr/bin/env python3
"""
Check Docker and system resources for running Ollama with MedGemma.
"""

import subprocess
import json
import psutil
import requests
from pathlib import Path

def check_system_resources():
    """Check system RAM, CPU, and disk resources."""
    print("🖥️ SYSTEM RESOURCES")
    print("=" * 30)
    
    # RAM
    memory = psutil.virtual_memory()
    total_ram_gb = memory.total / (1024**3)
    available_ram_gb = memory.available / (1024**3)
    used_ram_gb = memory.used / (1024**3)
    
    print(f"💾 RAM:")
    print(f"   Total: {total_ram_gb:.1f} GB")
    print(f"   Used: {used_ram_gb:.1f} GB ({memory.percent:.1f}%)")
    print(f"   Available: {available_ram_gb:.1f} GB")
    
    # CPU
    cpu_count = psutil.cpu_count()
    cpu_usage = psutil.cpu_percent(interval=1)
    
    print(f"\n🔧 CPU:")
    print(f"   Cores: {cpu_count}")
    print(f"   Usage: {cpu_usage:.1f}%")
    
    # Disk
    disk = psutil.disk_usage('/')
    total_disk_gb = disk.total / (1024**3)
    free_disk_gb = disk.free / (1024**3)
    
    print(f"\n💿 Disk:")
    print(f"   Total: {total_disk_gb:.1f} GB")
    print(f"   Free: {free_disk_gb:.1f} GB")
    
    return {
        'total_ram_gb': total_ram_gb,
        'available_ram_gb': available_ram_gb,
        'cpu_count': cpu_count,
        'free_disk_gb': free_disk_gb
    }

def check_docker_resources():
    """Check Docker container resources and status."""
    print(f"\n🐳 DOCKER RESOURCES")
    print("=" * 30)
    
    try:
        # Check if Docker is running
        result = subprocess.run(['docker', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ Docker is not running or not installed")
            return None
        
        print("✅ Docker is running")
        
        # Check Docker system info
        result = subprocess.run(['docker', 'system', 'df'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"\n📊 Docker System Usage:")
            print(result.stdout)
        
        # Check running containers
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"\n🏃 Running Containers:")
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                for line in lines:
                    if 'ollama' in line.lower():
                        print(f"   🎯 {line}")
                    elif line.startswith('CONTAINER'):
                        print(f"   {line}")
                    else:
                        print(f"   {line}")
            else:
                print("   No containers running")
        
        # Check Ollama container specifically
        result = subprocess.run(['docker', 'ps', '--filter', 'name=ollama'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                print(f"\n🦙 Ollama Container Status:")
                for line in lines:
                    print(f"   {line}")
                
                # Get container stats
                result = subprocess.run(['docker', 'stats', '--no-stream', '--format', 'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"\n📈 Container Resource Usage:")
                    print(result.stdout)
            else:
                print(f"\n⚠️ No Ollama container found running")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Docker command timed out")
        return None
    except FileNotFoundError:
        print("❌ Docker command not found - Docker may not be installed")
        return None
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return None

def check_ollama_status():
    """Check if Ollama is accessible and what models are available."""
    print(f"\n🦙 OLLAMA STATUS")
    print("=" * 30)
    
    try:
        # Check if Ollama API is accessible
        response = requests.get('http://localhost:11434/api/tags', timeout=10)
        
        if response.status_code == 200:
            print("✅ Ollama API is accessible")
            
            models = response.json().get('models', [])
            print(f"\n📚 Available Models ({len(models)} total):")
            
            total_size_gb = 0
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                size_gb = size / (1024**3)
                total_size_gb += size_gb
                
                modified = model.get('modified_at', 'Unknown')
                print(f"   📦 {name}")
                print(f"      Size: {size_gb:.1f} GB")
                print(f"      Modified: {modified}")
                
                # Highlight MedGemma
                if 'medgemma' in name.lower():
                    print(f"      🎯 This is MedGemma!")
            
            print(f"\n💾 Total Model Storage: {total_size_gb:.1f} GB")
            
            return {
                'accessible': True,
                'model_count': len(models),
                'total_size_gb': total_size_gb,
                'has_medgemma': any('medgemma' in m.get('name', '').lower() for m in models)
            }
        else:
            print(f"❌ Ollama API not accessible: {response.status_code}")
            return {'accessible': False, 'error': f'HTTP {response.status_code}'}
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama (connection refused)")
        return {'accessible': False, 'error': 'Connection refused'}
    except requests.exceptions.Timeout:
        print("❌ Ollama API timeout")
        return {'accessible': False, 'error': 'Timeout'}
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return {'accessible': False, 'error': str(e)}

def analyze_resource_requirements():
    """Analyze if we have enough resources for MedGemma."""
    print(f"\n🎯 RESOURCE ANALYSIS FOR MEDGEMMA")
    print("=" * 40)
    
    # MedGemma 4B model requirements (estimated)
    medgemma_ram_gb = 4.0  # Minimum for 4B model
    medgemma_recommended_ram_gb = 8.0  # Recommended
    medgemma_disk_gb = 2.5  # Model size
    
    print(f"📋 MedGemma 4B Requirements:")
    print(f"   Minimum RAM: {medgemma_ram_gb} GB")
    print(f"   Recommended RAM: {medgemma_recommended_ram_gb} GB")
    print(f"   Disk Space: {medgemma_disk_gb} GB")
    
    return {
        'min_ram_gb': medgemma_ram_gb,
        'recommended_ram_gb': medgemma_recommended_ram_gb,
        'disk_gb': medgemma_disk_gb
    }

def provide_recommendations(system_resources, ollama_status, requirements):
    """Provide recommendations based on resource analysis."""
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 30)
    
    available_ram = system_resources.get('available_ram_gb', 0)
    total_ram = system_resources.get('total_ram_gb', 0)
    free_disk = system_resources.get('free_disk_gb', 0)
    
    min_ram = requirements.get('min_ram_gb', 4)
    recommended_ram = requirements.get('recommended_ram_gb', 8)
    disk_needed = requirements.get('disk_gb', 2.5)
    
    print(f"🔍 Resource Check:")
    
    # RAM Check
    if available_ram >= recommended_ram:
        print(f"   ✅ RAM: Excellent ({available_ram:.1f} GB available)")
    elif available_ram >= min_ram:
        print(f"   ⚠️ RAM: Adequate ({available_ram:.1f} GB available, may be slow)")
    else:
        print(f"   ❌ RAM: Insufficient ({available_ram:.1f} GB available, need {min_ram} GB minimum)")
    
    # Disk Check
    if free_disk >= disk_needed * 2:
        print(f"   ✅ Disk: Plenty of space ({free_disk:.1f} GB free)")
    elif free_disk >= disk_needed:
        print(f"   ⚠️ Disk: Adequate space ({free_disk:.1f} GB free)")
    else:
        print(f"   ❌ Disk: Insufficient space ({free_disk:.1f} GB free, need {disk_needed} GB)")
    
    # Ollama Status
    if ollama_status.get('accessible'):
        if ollama_status.get('has_medgemma'):
            print(f"   ✅ Ollama: Running with MedGemma available")
        else:
            print(f"   ⚠️ Ollama: Running but MedGemma not found")
    else:
        print(f"   ❌ Ollama: Not accessible ({ollama_status.get('error', 'Unknown error')})")
    
    # Overall recommendation
    print(f"\n🎯 OVERALL RECOMMENDATION:")
    
    if (available_ram >= min_ram and free_disk >= disk_needed and 
        ollama_status.get('accessible') and ollama_status.get('has_medgemma')):
        print("   ✅ READY TO USE MEDGEMMA!")
        print("   Your system has sufficient resources to run MedGemma effectively.")
        
    elif available_ram >= min_ram and free_disk >= disk_needed:
        print("   ⚠️ SYSTEM CAPABLE BUT OLLAMA NEEDS ATTENTION")
        print("   Your system has enough resources, but Ollama needs to be fixed.")
        
    elif total_ram >= recommended_ram:
        print("   ⚠️ SYSTEM CAPABLE BUT MEMORY OPTIMIZATION NEEDED")
        print("   Close other applications to free up RAM for MedGemma.")
        
    else:
        print("   ❌ INSUFFICIENT RESOURCES")
        print("   Consider upgrading RAM or using OpenRouter instead.")
    
    # Specific actions
    print(f"\n🔧 SUGGESTED ACTIONS:")
    
    if not ollama_status.get('accessible'):
        print("   1. Restart Ollama Docker container")
        print("   2. Check Docker container logs: docker logs <ollama-container>")
        
    if ollama_status.get('accessible') and not ollama_status.get('has_medgemma'):
        print("   1. Pull MedGemma model: docker exec <ollama-container> ollama pull alibayram/medgemma:4b")
        
    if available_ram < min_ram:
        print("   1. Close unnecessary applications")
        print("   2. Restart your computer to free up memory")
        print("   3. Consider increasing Docker memory limits")
        
    if available_ram >= min_ram:
        print("   1. Test MedGemma with a simple query")
        print("   2. Monitor performance during entity extraction")

def main():
    """Main resource checking function."""
    print("🚀 DOCKER & OLLAMA RESOURCE ANALYSIS")
    print("=" * 50)
    
    # Check system resources
    system_resources = check_system_resources()
    
    # Check Docker
    docker_status = check_docker_resources()
    
    # Check Ollama
    ollama_status = check_ollama_status()
    
    # Analyze requirements
    requirements = analyze_resource_requirements()
    
    # Provide recommendations
    provide_recommendations(system_resources, ollama_status, requirements)

if __name__ == "__main__":
    main()
