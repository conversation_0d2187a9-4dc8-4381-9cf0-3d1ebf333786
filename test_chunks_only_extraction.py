#!/usr/bin/env python3
"""
Test entity extraction from chunks only
"""

import asyncio
from pathlib import Path

async def test_chunks_only_extraction():
    """Test entity extraction from chunks only."""
    
    print("🧪 TESTING ENTITY EXTRACTION FROM CHUNKS ONLY")
    print("=" * 50)
    
    try:
        # Test 1: Import the unified pipeline
        print("1️⃣ Testing unified pipeline import...")
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print(f"   ✅ Unified pipeline imported successfully")
        
        # Test 2: Initialize components
        print("\n2️⃣ Testing component initialization...")
        await pipeline._initialize_components()
        print(f"   ✅ Components initialized")
        
        # Test 3: Test chunk creation
        print("\n3️⃣ Testing chunk creation...")
        test_text = """
        This document discusses Lactate and Pyruvate levels in patients.
        L-Carnitine and Coenzyme Q10 are important nutrients for mitochondrial function.
        Vitamin B6 deficiency can affect Tryptophan metabolism and Serotonin production.
        Glutathione is a crucial antioxidant for cellular protection.
        Magnesium is essential for over 300 enzymatic reactions in the body.
        Zinc plays a role in immune function and protein synthesis.
        Vitamin D is important for bone health and immune regulation.
        Omega-3 fatty acids have anti-inflammatory properties.
        """
        
        chunks = pipeline._create_text_chunks(test_text, chunk_size=200, overlap=0)
        print(f"   ✅ Created {len(chunks)} chunks from test text")
        
        for i, chunk in enumerate(chunks[:2], 1):
            print(f"      Chunk {i}: {len(chunk)} chars - {chunk[:50]}...")
        
        # Test 4: Test entity extraction from chunks only
        print("\n4️⃣ Testing entity extraction from chunks only...")
        
        result = await pipeline._extract_entities_from_chunks(
            test_text, "test_document.txt", 200, 0
        )
        
        print(f"   ✅ Entity extraction successful!")
        print(f"   🏷️ Total entities: {result.get('count', 0)}")
        print(f"   📄 Document entities: {result.get('document_entities', 0)}")
        print(f"   🧩 Chunk entities: {result.get('chunk_entities', 0)}")
        print(f"   📊 Chunks processed: {result.get('chunks_processed', 0)}")
        
        # Test 5: Verify no document-level extraction
        print("\n5️⃣ Verifying no document-level extraction...")
        
        if result.get('document_entities', -1) == 0:
            print(f"   ✅ No document-level extraction confirmed!")
        else:
            print(f"   ❌ Document-level extraction still happening: {result.get('document_entities', -1)} entities")
        
        print(f"\n🎉 CHUNKS-ONLY ENTITY EXTRACTION TEST COMPLETE!")
        print(f"✅ Entity extraction now only processes chunks, not the full document!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_chunks_only_extraction())
