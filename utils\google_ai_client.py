#!/usr/bin/env python3
"""
Google AI Studio Client for MedGemma

This module provides a client for accessing Google's AI models including MedGemma
through the Google AI Studio API (Generative AI API).
"""

import os
import logging
import httpx
from typing import Dict, Any, List, Optional
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class GoogleAIClient:
    """Client for Google AI Studio API (Generative AI)."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gemini-1.5-flash"):
        """
        Initialize the Google AI client.
        
        Args:
            api_key: Google AI Studio API key. If None, reads from environment.
            model: Model name to use (default: gemini-1.5-flash)
        """
        self.api_key = api_key or os.environ.get('GOOGLE_AI_STUDIO_API_KEY') or os.environ.get('GEMINI_API_KEY')
        self.model = model
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        
        if not self.api_key:
            raise ValueError("Google AI Studio API key not found. Set GOOGLE_AI_STUDIO_API_KEY or GEMINI_API_KEY environment variable.")
        
        logger.info(f"Initialized GoogleAIClient with model: {self.model}")
    
    def generate_completion(self, system_prompt: str, user_prompt: str, temperature: float = 0.3, max_tokens: int = 4000) -> str:
        """
        Generate a completion using Google AI Studio API.
        
        Args:
            system_prompt: The system prompt
            user_prompt: The user prompt  
            temperature: Temperature for generation (default: 0.3)
            max_tokens: Maximum number of tokens to generate (default: 4000)
            
        Returns:
            The generated text
        """
        try:
            # Combine system and user prompts for Google AI format
            combined_prompt = f"System: {system_prompt}\n\nUser: {user_prompt}"
            
            # Prepare the request payload for Google AI Studio
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": combined_prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": temperature,
                    "maxOutputTokens": max_tokens,
                    "topP": 0.8,
                    "topK": 10
                }
            }
            
            # Make the API request
            url = f"{self.base_url}/models/{self.model}:generateContent"
            
            with httpx.Client() as client:
                response = client.post(
                    url,
                    json=payload,
                    params={"key": self.api_key},
                    headers={"Content-Type": "application/json"},
                    timeout=120.0
                )
                
                response.raise_for_status()
                result = response.json()
                
                # Extract the generated text
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]
                    if "content" in candidate and "parts" in candidate["content"]:
                        parts = candidate["content"]["parts"]
                        if len(parts) > 0 and "text" in parts[0]:
                            return parts[0]["text"]
                
                logger.error(f"Unexpected response format: {result}")
                return "I'm sorry, but I couldn't generate a response at this time."
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error from Google AI Studio: {e}")
            logger.error(f"Response content: {e.response.text if hasattr(e, 'response') else 'No response content'}")
            return f"Error: HTTP {e.response.status_code if hasattr(e, 'response') else 'Unknown'}"
        except httpx.RequestError as e:
            logger.error(f"Request error to Google AI Studio: {e}")
            return f"Error: {e}"
        except Exception as e:
            logger.error(f"Unexpected error with Google AI Studio: {e}")
            return f"Error: {e}"
    
    def generate_completion_with_messages(self, messages: List[Dict[str, str]], temperature: float = 0.3, max_tokens: int = 4000) -> Dict[str, Any]:
        """
        Generate a completion using messages format (compatible with OpenAI-style APIs).
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            
        Returns:
            Response in OpenAI-compatible format
        """
        try:
            # Convert messages to Google AI format
            system_prompt = ""
            user_prompt = ""
            
            for message in messages:
                if message["role"] == "system":
                    system_prompt = message["content"]
                elif message["role"] == "user":
                    user_prompt = message["content"]
            
            # Generate completion
            content = self.generate_completion(system_prompt, user_prompt, temperature, max_tokens)
            
            # Return in OpenAI-compatible format
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": content
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": len(system_prompt.split()) + len(user_prompt.split()),
                    "completion_tokens": len(content.split()),
                    "total_tokens": len(system_prompt.split()) + len(user_prompt.split()) + len(content.split())
                }
            }
            
        except Exception as e:
            logger.error(f"Error in generate_completion_with_messages: {e}")
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant", 
                            "content": f"Error: {e}"
                        },
                        "finish_reason": "error"
                    }
                ],
                "error": str(e)
            }

    def list_models(self) -> List[str]:
        """
        List available models from Google AI Studio.
        
        Returns:
            List of available model names
        """
        try:
            url = f"{self.base_url}/models"
            
            with httpx.Client() as client:
                response = client.get(
                    url,
                    params={"key": self.api_key},
                    timeout=30.0
                )
                
                response.raise_for_status()
                result = response.json()
                
                models = []
                if "models" in result:
                    for model in result["models"]:
                        if "name" in model:
                            # Extract model name from full path
                            model_name = model["name"].split("/")[-1]
                            models.append(model_name)
                
                return models
                
        except Exception as e:
            logger.error(f"Error listing Google AI models: {e}")
            return []

def test_google_ai_client():
    """Test the Google AI client."""
    try:
        client = GoogleAIClient()
        
        # Test basic completion
        response = client.generate_completion(
            system_prompt="You are a helpful medical AI assistant.",
            user_prompt="What is intestinal dysbiosis?",
            temperature=0.3,
            max_tokens=200
        )
        
        print(f"✅ Google AI Client Test Response: {response[:100]}...")
        
        # Test listing models
        models = client.list_models()
        print(f"✅ Available models: {models[:5]}...")  # Show first 5
        
        return True
        
    except Exception as e:
        print(f"❌ Google AI Client Test Failed: {e}")
        return False

if __name__ == "__main__":
    test_google_ai_client()
