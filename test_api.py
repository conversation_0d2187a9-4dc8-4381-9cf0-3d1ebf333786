#!/usr/bin/env python3
"""
Test the API endpoints to see if data is loading.
"""

import requests
import json

def test_api():
    """Test the API endpoints."""
    try:
        # Test the graph statistics endpoint
        print("🔄 Testing API endpoints...")
        
        response = requests.get('http://localhost:9753/api/entities/statistics')
        if response.status_code == 200:
            stats = response.json()
            print('✅ API Statistics Working:')
            print(f'   Documents: {stats.get("total_documents", 0)}')
            print(f'   Entities: {stats.get("total_entities", 0)}')
            print(f'   Relationships: {stats.get("total_relationships", 0)}')
            print(f'   References: {stats.get("total_references", 0)}')
            print(f'   Chunks: {stats.get("total_chunks", 0)}')
            
            if stats.get("total_entities", 0) > 0:
                print("\n✅ Data is loading correctly!")
                
                # Test entity listing
                entity_response = requests.get('http://localhost:9753/api/entities?limit=5')
                if entity_response.status_code == 200:
                    entities = entity_response.json()
                    print(f'\n✅ Entity API Working:')
                    print(f'   Total entities: {entities.get("count", 0)}')
                    print(f'   Sample entities:')
                    for entity in entities.get("entities", [])[:3]:
                        print(f'     - {entity.get("name", "Unknown")} ({entity.get("type", "Unknown")})')
                else:
                    print(f'❌ Entity API Error: {entity_response.status_code}')
            else:
                print("\n⚠️ No data found - check graph name")
        else:
            print(f'❌ API Error: {response.status_code}')
            print(f'   Response: {response.text}')
            
    except requests.exceptions.ConnectionError:
        print('❌ Connection Error: Cannot connect to http://localhost:9753')
        print('   Make sure the application is running')
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    test_api()
