#!/usr/bin/env python3
"""
Fix UI to work with the new FalkorDB system.
Updates all database connections and naming conventions.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter

logger = get_logger(__name__)

async def test_falkordb_connection():
    """Test FalkorDB connection and basic queries."""
    try:
        logger.info("🔄 Testing FalkorDB connection...")
        adapter = await get_falkordb_adapter()
        
        # Test basic connection
        result = adapter.execute_cypher("MATCH (n) RETURN count(n) as total_nodes LIMIT 1")
        total_nodes = result[1][0][0] if len(result) > 1 and len(result[1]) > 0 else 0
        
        logger.info(f"✅ FalkorDB connected successfully")
        logger.info(f"   Total nodes in graph: {total_nodes}")
        
        # Test entity query
        entity_query = "MATCH (e:Entity) RETURN count(e) as entity_count LIMIT 1"
        entity_result = adapter.execute_cypher(entity_query)
        entity_count = entity_result[1][0][0] if len(entity_result) > 1 and len(entity_result[1]) > 0 else 0
        
        logger.info(f"   Total entities: {entity_count}")
        
        # Test document query
        doc_query = "MATCH (d:Document) RETURN count(d) as doc_count LIMIT 1"
        doc_result = adapter.execute_cypher(doc_query)
        doc_count = doc_result[1][0][0] if len(doc_result) > 1 and len(doc_result[1]) > 0 else 0
        
        logger.info(f"   Total documents: {doc_count}")
        
        # Test chunk query
        chunk_query = "MATCH (c:Chunk) RETURN count(c) as chunk_count LIMIT 1"
        chunk_result = adapter.execute_cypher(chunk_query)
        chunk_count = chunk_result[1][0][0] if len(chunk_result) > 1 and len(chunk_result[1]) > 0 else 0
        
        logger.info(f"   Total chunks: {chunk_count}")
        
        # Test reference query
        ref_query = "MATCH (r:Reference) RETURN count(r) as ref_count LIMIT 1"
        ref_result = adapter.execute_cypher(ref_query)
        ref_count = ref_result[1][0][0] if len(ref_result) > 1 and len(ref_result[1]) > 0 else 0
        
        logger.info(f"   Total references: {ref_count}")
        
        # Test relationship query
        rel_query = "MATCH ()-[r]->() RETURN count(r) as rel_count LIMIT 1"
        rel_result = adapter.execute_cypher(rel_query)
        rel_count = rel_result[1][0][0] if len(rel_result) > 1 and len(rel_result[1]) > 0 else 0
        
        logger.info(f"   Total relationships: {rel_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FalkorDB connection failed: {e}")
        return False

async def test_entity_endpoints():
    """Test entity endpoints with the new FalkorDB system."""
    try:
        logger.info("🔄 Testing entity endpoints...")
        
        # Import here to avoid circular imports
        from routes.entity_routes import get_all_entities, get_graph_statistics
        
        # Test graph statistics
        stats = await get_graph_statistics()
        logger.info(f"✅ Graph statistics working:")
        logger.info(f"   Documents: {stats.get('total_documents', 0)}")
        logger.info(f"   Entities: {stats.get('total_entities', 0)}")
        logger.info(f"   Relationships: {stats.get('total_relationships', 0)}")
        logger.info(f"   References: {stats.get('total_references', 0)}")
        
        # Test entity listing (small sample)
        entities_result = await get_all_entities(limit=5, offset=0)
        logger.info(f"✅ Entity listing working:")
        logger.info(f"   Retrieved {len(entities_result.get('entities', []))} entities")
        logger.info(f"   Total count: {entities_result.get('count', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Entity endpoints test failed: {e}")
        return False

async def test_knowledge_graph_endpoints():
    """Test knowledge graph endpoints."""
    try:
        logger.info("🔄 Testing knowledge graph endpoints...")
        
        from services.knowledge_graph_service import get_knowledge_graph
        
        # Test knowledge graph data
        graph_data = await get_knowledge_graph(limit=10)
        logger.info(f"✅ Knowledge graph working:")
        logger.info(f"   Nodes: {len(graph_data.nodes)}")
        logger.info(f"   Relationships: {len(graph_data.relationships)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Knowledge graph endpoints test failed: {e}")
        return False

def check_ui_files():
    """Check if UI template files exist."""
    logger.info("🔄 Checking UI template files...")
    
    templates_dir = Path("templates")
    required_templates = [
        "dashboard_modern.html",
        "entities_modern.html", 
        "knowledge_graph_modern.html",
        "documents_modern.html",
        "references_modern.html",
        "qa_modern.html",
        "search_modern.html",
        "settings_modern.html"
    ]
    
    missing_templates = []
    for template in required_templates:
        template_path = templates_dir / template
        if not template_path.exists():
            missing_templates.append(template)
        else:
            logger.info(f"   ✅ {template}")
    
    if missing_templates:
        logger.warning(f"⚠️ Missing templates: {missing_templates}")
        return False
    else:
        logger.info("✅ All required templates found")
        return True

def check_static_files():
    """Check if static files exist."""
    logger.info("🔄 Checking static files...")
    
    static_dir = Path("static")
    css_dir = static_dir / "css"
    js_dir = static_dir / "js"
    
    # Check for essential files
    essential_files = [
        static_dir / "styles.css",
        js_dir / "dashboard.js" if (js_dir).exists() else None,
        css_dir / "modern.css" if (css_dir).exists() else None
    ]
    
    missing_files = []
    for file_path in essential_files:
        if file_path and not file_path.exists():
            missing_files.append(str(file_path))
        elif file_path:
            logger.info(f"   ✅ {file_path.name}")
    
    if missing_files:
        logger.warning(f"⚠️ Missing static files: {missing_files}")
    else:
        logger.info("✅ Essential static files found")
    
    return len(missing_files) == 0

async def main():
    """Main function to test and fix UI for FalkorDB."""
    logger.info("🚀 FIXING UI FOR FALKORDB SYSTEM")
    logger.info("=" * 50)
    
    # Test database connection
    db_ok = await test_falkordb_connection()
    
    # Test entity endpoints
    entities_ok = await test_entity_endpoints() if db_ok else False
    
    # Test knowledge graph endpoints
    kg_ok = await test_knowledge_graph_endpoints() if db_ok else False
    
    # Check UI files
    templates_ok = check_ui_files()
    static_ok = check_static_files()
    
    # Summary
    logger.info("\n📊 SYSTEM STATUS SUMMARY:")
    logger.info("=" * 30)
    logger.info(f"   Database Connection: {'✅' if db_ok else '❌'}")
    logger.info(f"   Entity Endpoints: {'✅' if entities_ok else '❌'}")
    logger.info(f"   Knowledge Graph: {'✅' if kg_ok else '❌'}")
    logger.info(f"   Template Files: {'✅' if templates_ok else '❌'}")
    logger.info(f"   Static Files: {'✅' if static_ok else '❌'}")
    
    if all([db_ok, entities_ok, kg_ok, templates_ok]):
        logger.info("\n🎉 UI IS READY FOR FALKORDB!")
        logger.info("   You can now start the application with: python app.py")
        return True
    else:
        logger.error("\n❌ UI NEEDS FIXES")
        logger.error("   Some components are not working properly")
        return False

if __name__ == "__main__":
    asyncio.run(main())
