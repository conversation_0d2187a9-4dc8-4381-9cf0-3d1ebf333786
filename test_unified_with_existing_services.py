#!/usr/bin/env python3
"""
Test the unified pipeline integration with existing working services
"""

import asyncio
from pathlib import Path

async def test_unified_with_existing_services():
    """Test the unified pipeline using existing working services."""
    
    print("🧪 TESTING UNIFIED PIPELINE WITH EXISTING SERVICES")
    print("=" * 60)
    
    try:
        # Test 1: Import the unified pipeline
        print("1️⃣ Testing unified pipeline import...")
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print(f"   ✅ Unified pipeline imported successfully")
        
        # Test 2: Initialize components
        print("\n2️⃣ Testing component initialization...")
        await pipeline._initialize_components()
        print(f"   ✅ Components initialized")
        
        # Check if document processor is available
        if hasattr(pipeline, 'document_processor'):
            print(f"   ✅ Document processor available: {type(pipeline.document_processor)}")
        else:
            print(f"   ❌ Document processor not available")
            return False
        
        # Test 3: Look for test files
        print("\n3️⃣ Looking for test files...")
        test_dirs = ["uploads", "documents", "temp"]
        test_files = []
        
        for test_dir in test_dirs:
            if Path(test_dir).exists():
                for file_path in Path(test_dir).glob("*.pdf"):
                    test_files.append(file_path)
                    if len(test_files) >= 1:  # Just need 1 file for testing
                        break
        
        if test_files:
            test_file = test_files[0]
            print(f"   📄 Found test file: {test_file.name}")
            
            # Test 4: Test the existing service integration
            print("\n4️⃣ Testing existing service integration...")
            
            try:
                result = await pipeline._process_with_existing_service(
                    file_path=test_file,
                    chunk_size=1200,
                    overlap=0,
                    extract_entities=True,
                    generate_embeddings=True
                )
                
                print(f"   ✅ Existing service integration successful!")
                print(f"   🏷️ Entities: {result.get('entities', 0)}")
                print(f"   🧩 Chunks: {result.get('chunks', 0)}")
                print(f"   🎯 Success: {result.get('success', False)}")
                
                if result.get('full_result'):
                    full_result = result.get('full_result')
                    print(f"   📊 Full result keys: {list(full_result.keys())}")
                
            except Exception as e:
                print(f"   ❌ Existing service integration failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test 5: Test full unified pipeline
            print("\n5️⃣ Testing full unified pipeline...")
            
            try:
                full_result = await pipeline.process_document(
                    file_path=test_file,
                    chunk_size=1200,
                    overlap=0,
                    extract_entities=True,
                    extract_references=True,
                    extract_metadata=True,
                    generate_embeddings=True,
                    force_reprocess=True  # Force reprocess for testing
                )
                
                print(f"   ✅ Full pipeline successful!")
                print(f"   📄 Filename: {full_result.get('filename', 'unknown')}")
                print(f"   🏷️ Entities: {full_result.get('entities', 0)}")
                print(f"   🧩 Chunks: {full_result.get('chunks', 0)}")
                print(f"   📚 References: {full_result.get('references', 0)}")
                print(f"   🎯 Success: {full_result.get('success', False)}")
                
            except Exception as e:
                print(f"   ❌ Full pipeline failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"   ℹ️ No test files found - skipping processing test")
            print(f"   ✅ But the integration and import tests passed!")
        
        print(f"\n🎉 UNIFIED PIPELINE WITH EXISTING SERVICES TEST COMPLETE!")
        print(f"✅ Successfully integrated existing working services!")
        print(f"📱 The unified pipeline now uses proven, working components")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_unified_with_existing_services())
