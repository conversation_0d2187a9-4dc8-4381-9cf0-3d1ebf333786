#!/usr/bin/env python3
"""
Quick script to check what documents exist in the database.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter

async def check_documents():
    adapter = GraphitiFalkorDBAdapter('graphiti')
    
    # Get all episodes
    query = 'MATCH (ep:Episode) RETURN ep.uuid as uuid, ep.name as name, ep.file_path as file_path'
    result = adapter.execute_cypher(query)
    print('Documents in database:')
    if result and len(result) > 1:
        headers = result[0]
        for row in result[1]:
            doc_data = dict(zip(headers, row))
            print(f'  UUID: {doc_data["uuid"]}')
            print(f'  Name: {doc_data["name"]}')
            print(f'  Path: {doc_data["file_path"]}')
            print('  ---')
    else:
        print('  No documents found')

if __name__ == "__main__":
    asyncio.run(check_documents())
