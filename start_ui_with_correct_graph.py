#!/usr/bin/env python3
"""
Start the UI with the correct FalkorDB graph configuration.
"""

import os
import sys
from pathlib import Path

# Set the correct graph name as environment variable
os.environ["FALKORDB_GRAPH"] = "graphiti_knowledge"

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Clear any cached modules
if 'utils.config' in sys.modules:
    del sys.modules['utils.config']
if 'database.falkordb_adapter' in sys.modules:
    del sys.modules['database.falkordb_adapter']
if 'database.database_service' in sys.modules:
    del sys.modules['database.database_service']

# Now import and start the app
import uvicorn
from app import app
from utils.config import HOST, PORT, FALKORDB_GRAPH

print(f"🚀 Starting Graphiti Knowledge Graph Application")
print(f"📊 Using FalkorDB graph: {FALKORDB_GRAPH}")
print(f"📍 Server: http://localhost:{PORT}")
print(f"📊 Dashboard: http://localhost:{PORT}/")
print(f"📚 API Docs: http://localhost:{PORT}/docs")
print("=" * 50)

if __name__ == "__main__":
    uvicorn.run(
        app,
        host=HOST,
        port=PORT,
        reload=False
    )
