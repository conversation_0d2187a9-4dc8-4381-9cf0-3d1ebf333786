#!/usr/bin/env python3
"""
Verification script for Graphiti-compliant data structures.
Checks that the new ingestion pipeline creates proper Graphiti base class structures.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.redis_vector_search import get_redis_vector_search_client
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class GraphitiIntegrationVerifier:
    """Verifies proper Graphiti integration."""
    
    def __init__(self, graph_name: str = "graphiti"):
        self.graph_name = graph_name
        self.falkor_adapter = None
        self.redis_client = None
        self.compliance_score = 0
        self.max_score = 100
        
    async def initialize(self):
        """Initialize database connections."""
        try:
            self.falkor_adapter = GraphitiFalkorDBAdapter(self.graph_name)
            self.redis_client = get_redis_vector_search_client()
            logger.info("✅ Database connections initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize: {e}")
            raise
    
    def check_episodic_nodes(self) -> Dict[str, Any]:
        """Check EpisodicNode compliance."""
        print("\n🔍 CHECKING EPISODIC NODES")
        print("=" * 50)
        
        try:
            # Get episodic nodes
            result = self.falkor_adapter.execute_cypher("""
                MATCH (ep:Episodic)
                RETURN ep.uuid, ep.name, ep.group_id, ep.source, ep.content, ep.created_at, ep.valid_at
                LIMIT 5
            """)
            
            if not result or len(result) <= 1:
                print("❌ No Episodic nodes found")
                return {'compliant': False, 'count': 0, 'issues': ['No Episodic nodes found']}
            
            count = len(result) - 1  # Subtract header
            issues = []
            compliant_nodes = 0
            
            for i, row in enumerate(result[1:], 1):
                if len(row) >= 7:
                    uuid_val, name, group_id, source, content, created_at, valid_at = row[:7]
                    
                    node_issues = []
                    if not uuid_val:
                        node_issues.append("Missing uuid")
                    if not name:
                        node_issues.append("Missing name")
                    if not group_id:
                        node_issues.append("Missing group_id")
                    if not source:
                        node_issues.append("Missing source")
                    if not content:
                        node_issues.append("Missing content")
                    if not created_at:
                        node_issues.append("Missing created_at")
                    if not valid_at:
                        node_issues.append("Missing valid_at")
                    
                    if not node_issues:
                        compliant_nodes += 1
                        if i <= 3:
                            print(f"  ✅ Episode {i}: {name[:50]}...")
                    else:
                        issues.extend(node_issues)
                        if i <= 3:
                            print(f"  ❌ Episode {i}: {', '.join(node_issues)}")
            
            compliance_rate = (compliant_nodes / count) * 100 if count > 0 else 0
            print(f"\n📊 Episodic Nodes: {compliant_nodes}/{count} compliant ({compliance_rate:.1f}%)")
            
            if compliance_rate >= 90:
                self.compliance_score += 20
                print("✅ Episodic nodes are Graphiti-compliant!")
            elif compliance_rate >= 70:
                self.compliance_score += 15
                print("⚠️  Episodic nodes mostly compliant")
            else:
                self.compliance_score += 5
                print("❌ Episodic nodes need improvement")
            
            return {
                'compliant': compliance_rate >= 90,
                'count': count,
                'compliant_nodes': compliant_nodes,
                'compliance_rate': compliance_rate,
                'issues': list(set(issues))
            }
            
        except Exception as e:
            print(f"❌ Error checking Episodic nodes: {e}")
            return {'compliant': False, 'count': 0, 'issues': [str(e)]}
    
    def check_entity_nodes(self) -> Dict[str, Any]:
        """Check EntityNode compliance."""
        print("\n🔍 CHECKING ENTITY NODES")
        print("=" * 50)
        
        try:
            # Get entity nodes
            result = self.falkor_adapter.execute_cypher("""
                MATCH (e:Entity)
                RETURN e.uuid, e.name, e.group_id, e.type, e.created_at, e.summary
                LIMIT 10
            """)
            
            if not result or len(result) <= 1:
                print("❌ No Entity nodes found")
                return {'compliant': False, 'count': 0, 'issues': ['No Entity nodes found']}
            
            count = len(result) - 1
            issues = []
            compliant_nodes = 0
            
            for i, row in enumerate(result[1:], 1):
                if len(row) >= 6:
                    uuid_val, name, group_id, entity_type, created_at, summary = row[:6]
                    
                    node_issues = []
                    if not uuid_val:
                        node_issues.append("Missing uuid")
                    if not name:
                        node_issues.append("Missing name")
                    if not group_id:
                        node_issues.append("Missing group_id")
                    if not entity_type:
                        node_issues.append("Missing type")
                    if not created_at:
                        node_issues.append("Missing created_at")
                    
                    if not node_issues:
                        compliant_nodes += 1
                        if i <= 5:
                            print(f"  ✅ Entity {i}: {name} ({entity_type})")
                    else:
                        issues.extend(node_issues)
                        if i <= 5:
                            print(f"  ❌ Entity {i}: {', '.join(node_issues)}")
            
            compliance_rate = (compliant_nodes / count) * 100 if count > 0 else 0
            print(f"\n📊 Entity Nodes: {compliant_nodes}/{count} compliant ({compliance_rate:.1f}%)")
            
            if compliance_rate >= 90:
                self.compliance_score += 25
                print("✅ Entity nodes are Graphiti-compliant!")
            elif compliance_rate >= 70:
                self.compliance_score += 20
                print("⚠️  Entity nodes mostly compliant")
            else:
                self.compliance_score += 10
                print("❌ Entity nodes need improvement")
            
            return {
                'compliant': compliance_rate >= 90,
                'count': count,
                'compliant_nodes': compliant_nodes,
                'compliance_rate': compliance_rate,
                'issues': list(set(issues))
            }
            
        except Exception as e:
            print(f"❌ Error checking Entity nodes: {e}")
            return {'compliant': False, 'count': 0, 'issues': [str(e)]}
    
    def check_episodic_edges(self) -> Dict[str, Any]:
        """Check EpisodicEdge compliance."""
        print("\n🔍 CHECKING EPISODIC EDGES")
        print("=" * 50)
        
        try:
            # Get episodic edges (MENTIONS relationships)
            result = self.falkor_adapter.execute_cypher("""
                MATCH (ep:Episodic)-[r:MENTIONS]->(e:Entity)
                RETURN r.uuid, r.group_id, r.created_at, ep.uuid, e.uuid
                LIMIT 10
            """)
            
            if not result or len(result) <= 1:
                print("❌ No MENTIONS relationships found")
                return {'compliant': False, 'count': 0, 'issues': ['No MENTIONS relationships found']}
            
            count = len(result) - 1
            issues = []
            compliant_edges = 0
            
            for i, row in enumerate(result[1:], 1):
                if len(row) >= 5:
                    edge_uuid, group_id, created_at, ep_uuid, entity_uuid = row[:5]
                    
                    edge_issues = []
                    if not edge_uuid:
                        edge_issues.append("Missing edge uuid")
                    if not group_id:
                        edge_issues.append("Missing group_id")
                    if not created_at:
                        edge_issues.append("Missing created_at")
                    if not ep_uuid:
                        edge_issues.append("Missing source node uuid")
                    if not entity_uuid:
                        edge_issues.append("Missing target node uuid")
                    
                    if not edge_issues:
                        compliant_edges += 1
                        if i <= 3:
                            print(f"  ✅ Edge {i}: Episode -> Entity")
                    else:
                        issues.extend(edge_issues)
                        if i <= 3:
                            print(f"  ❌ Edge {i}: {', '.join(edge_issues)}")
            
            compliance_rate = (compliant_edges / count) * 100 if count > 0 else 0
            print(f"\n📊 Episodic Edges: {compliant_edges}/{count} compliant ({compliance_rate:.1f}%)")
            
            if compliance_rate >= 90:
                self.compliance_score += 20
                print("✅ Episodic edges are Graphiti-compliant!")
            elif compliance_rate >= 70:
                self.compliance_score += 15
                print("⚠️  Episodic edges mostly compliant")
            else:
                self.compliance_score += 5
                print("❌ Episodic edges need improvement")
            
            return {
                'compliant': compliance_rate >= 90,
                'count': count,
                'compliant_edges': compliant_edges,
                'compliance_rate': compliance_rate,
                'issues': list(set(issues))
            }
            
        except Exception as e:
            print(f"❌ Error checking Episodic edges: {e}")
            return {'compliant': False, 'count': 0, 'issues': [str(e)]}
    
    def check_embeddings_integration(self) -> Dict[str, Any]:
        """Check Redis embeddings integration."""
        print("\n🔍 CHECKING EMBEDDINGS INTEGRATION")
        print("=" * 50)
        
        try:
            # Get embedding count
            keys = self.redis_client.keys('embedding:*')
            embedding_count = len(keys)
            
            print(f"📊 Found {embedding_count} embeddings in Redis")
            
            if embedding_count == 0:
                print("❌ No embeddings found")
                return {'compliant': False, 'count': 0, 'issues': ['No embeddings found']}
            
            # Check sample embedding structure
            sample_key = keys[0] if keys else None
            if sample_key:
                data = self.redis_client.hgetall(sample_key)
                
                required_fields = [b'fact_uuid', b'episode_uuid', b'body', b'embedding']
                missing_fields = [f.decode() for f in required_fields if f not in data]
                
                if not missing_fields:
                    print("✅ Embedding structure is correct")
                    self.compliance_score += 15
                    return {
                        'compliant': True,
                        'count': embedding_count,
                        'issues': []
                    }
                else:
                    print(f"❌ Missing fields in embeddings: {missing_fields}")
                    self.compliance_score += 5
                    return {
                        'compliant': False,
                        'count': embedding_count,
                        'issues': missing_fields
                    }
            
        except Exception as e:
            print(f"❌ Error checking embeddings: {e}")
            return {'compliant': False, 'count': 0, 'issues': [str(e)]}
    
    def check_group_id_consistency(self) -> Dict[str, Any]:
        """Check group_id consistency across nodes."""
        print("\n🔍 CHECKING GROUP_ID CONSISTENCY")
        print("=" * 50)
        
        try:
            # Get group_id distribution
            result = self.falkor_adapter.execute_cypher("""
                MATCH (n)
                WHERE n.group_id IS NOT NULL
                RETURN n.group_id, count(n)
                ORDER BY count(n) DESC
                LIMIT 10
            """)
            
            if not result or len(result) <= 1:
                print("❌ No nodes with group_id found")
                return {'compliant': False, 'groups': 0, 'issues': ['No group_id assignments']}
            
            groups = len(result) - 1
            print(f"📊 Found {groups} different group_id values:")
            
            for i, row in enumerate(result[1:], 1):
                if len(row) >= 2:
                    group_id, count = row[:2]
                    print(f"  • {group_id}: {count} nodes")
            
            if groups > 0:
                self.compliance_score += 20
                print("✅ Group IDs are properly assigned")
                return {'compliant': True, 'groups': groups, 'issues': []}
            else:
                print("❌ No group ID assignments found")
                return {'compliant': False, 'groups': 0, 'issues': ['No group_id assignments']}
            
        except Exception as e:
            print(f"❌ Error checking group_id consistency: {e}")
            return {'compliant': False, 'groups': 0, 'issues': [str(e)]}
    
    async def run_verification(self):
        """Run complete Graphiti integration verification."""
        print("🚀 STARTING GRAPHITI INTEGRATION VERIFICATION")
        print("=" * 80)
        
        await self.initialize()
        
        # Run all checks
        episodic_result = self.check_episodic_nodes()
        entity_result = self.check_entity_nodes()
        edge_result = self.check_episodic_edges()
        embedding_result = self.check_embeddings_integration()
        group_result = self.check_group_id_consistency()
        
        # Generate final report
        print(f"\n📋 GRAPHITI INTEGRATION REPORT")
        print("=" * 80)
        print(f"🎯 Overall Compliance Score: {self.compliance_score}/{self.max_score} ({(self.compliance_score/self.max_score)*100:.1f}%)")
        
        if self.compliance_score >= 90:
            print("🎉 EXCELLENT! Full Graphiti compliance achieved!")
        elif self.compliance_score >= 70:
            print("✅ GOOD! Mostly Graphiti-compliant with minor issues")
        elif self.compliance_score >= 50:
            print("⚠️  MODERATE compliance - some issues need attention")
        else:
            print("❌ POOR compliance - significant issues require fixes")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"  📝 Episodic Nodes: {episodic_result.get('count', 0)} found")
        print(f"  🏷️  Entity Nodes: {entity_result.get('count', 0)} found")
        print(f"  🔗 Episodic Edges: {edge_result.get('count', 0)} found")
        print(f"  🧠 Embeddings: {embedding_result.get('count', 0)} found")
        print(f"  🏗️  Group IDs: {group_result.get('groups', 0)} found")

async def main():
    """Main verification function."""
    verifier = GraphitiIntegrationVerifier()
    await verifier.run_verification()

if __name__ == "__main__":
    asyncio.run(main())
