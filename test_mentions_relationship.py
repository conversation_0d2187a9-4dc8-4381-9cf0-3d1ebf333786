#!/usr/bin/env python3
"""
Test MENTIONS relationship creation.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def test_mentions_relationship():
    """Test creating MENTIONS relationships."""
    
    print("🔍 Testing MENTIONS relationship creation...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # Get a recent fact and entity to test with
        print("\n1. Getting a recent fact...")
        fact_query = '''
        MATCH (ep:Episode)-[:CONTAINS]->(f:Fact)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN f.uuid as fact_uuid, f.body as body
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        fact_result = adapter.execute_cypher(fact_query)
        
        if not fact_result or len(fact_result) <= 1 or not fact_result[1]:
            print("❌ No facts found")
            return
            
        fact_uuid = fact_result[1][0][0]
        fact_body = fact_result[1][0][1]
        print(f"✅ Found fact: {fact_uuid}")
        print(f"   Body sample: {fact_body[:100]}...")
        
        print("\n2. Getting a recent entity...")
        entity_query = '''
        MATCH (e:Entity)
        WHERE e.extraction_method = 'llm_unified_pipeline'
        RETURN e.uuid as entity_uuid, e.name as name, e.type as type
        ORDER BY e.created_at DESC
        LIMIT 1
        '''
        entity_result = adapter.execute_cypher(entity_query)
        
        if not entity_result or len(entity_result) <= 1 or not entity_result[1]:
            print("❌ No entities found")
            return
            
        entity_uuid = entity_result[1][0][0]
        entity_name = entity_result[1][0][1]
        entity_type = entity_result[1][0][2]
        print(f"✅ Found entity: {entity_name} ({entity_type})")
        
        print("\n3. Testing MENTIONS relationship creation...")
        
        # Test the exact query from the unified pipeline
        mentions_query = """
        MATCH (f:Fact {uuid: $fact_uuid})
        MATCH (e:Entity {uuid: $entity_uuid})
        MERGE (f)-[r:MENTIONS]->(e)
        ON CREATE SET
            r.created_at = timestamp(),
            r.confidence = $confidence
        RETURN r
        """
        
        mentions_result = adapter.execute_cypher(mentions_query, {
            'fact_uuid': fact_uuid,
            'entity_uuid': entity_uuid,
            'confidence': 1.0
        })
        
        print(f"📊 MENTIONS creation result: {mentions_result}")
        
        if mentions_result and len(mentions_result) > 1 and mentions_result[1]:
            print("✅ MENTIONS relationship created successfully!")
        else:
            print("❌ MENTIONS relationship creation failed")
        
        print("\n4. Verifying the relationship...")
        verify_query = f'''
        MATCH (f:Fact {{uuid: '{fact_uuid}'}})-[r:MENTIONS]->(e:Entity {{uuid: '{entity_uuid}'}})
        RETURN f.uuid as fact_uuid, e.name as entity_name, r.confidence as confidence
        '''
        verify_result = adapter.execute_cypher(verify_query)
        
        if verify_result and len(verify_result) > 1 and verify_result[1]:
            print("✅ MENTIONS relationship verified!")
            for row in verify_result[1]:
                print(f"   Fact {row[0][:8]}... -> {row[1]} (confidence: {row[2]})")
        else:
            print("❌ MENTIONS relationship not found")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mentions_relationship())
