#!/usr/bin/env python3
"""
Simple test to verify Redis storage works.
"""

import asyncio
from utils.redis_vector_search import store_embedding

async def test_redis_storage():
    """Test Redis storage directly."""
    
    print("🔍 Testing Redis storage directly...")
    
    try:
        # Test data
        fact_uuid = "test-fact-uuid"
        episode_uuid = "test-episode-uuid"
        body = "This is a test fact body for embedding storage."
        embedding = [0.1, 0.2, 0.3, 0.4, 0.5] * 204  # 1020 dimensions
        embedding.extend([0.6, 0.7, 0.8, 0.9])  # Make it 1024 dimensions
        
        print(f"✅ Test data prepared:")
        print(f"   Fact UUID: {fact_uuid}")
        print(f"   Episode UUID: {episode_uuid}")
        print(f"   Body length: {len(body)}")
        print(f"   Embedding dimensions: {len(embedding)}")
        
        # Test storage
        print(f"\n🚀 Testing storage...")
        success = store_embedding(
            fact_uuid=fact_uuid,
            episode_uuid=episode_uuid,
            body=body,
            embedding=embedding
        )
        
        if success:
            print("✅ Redis storage successful!")
            
            # Check if it was stored
            import redis
            r = redis.Redis(host='localhost', port=6380, decode_responses=True)
            
            # Check for the stored embedding
            keys = r.keys(f"*{fact_uuid}*")
            print(f"✅ Found {len(keys)} keys in Redis for test fact")
            
            if keys:
                for key in keys:
                    print(f"   Key: {key}")
                    
        else:
            print("❌ Redis storage failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_redis_storage())
