#!/usr/bin/env python3
"""
Check entity structure to see what fields they have.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def check_entity_structure():
    """Check what fields entities actually have."""
    
    print("🔍 Checking entity structure...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # Get recent entities and all their properties
        print("\n1. Checking entity properties...")
        entity_query = '''
        MATCH (e:Entity)
        WHERE e.extraction_method = 'llm_unified_pipeline'
        RETURN e
        ORDER BY e.created_at DESC
        LIMIT 3
        '''
        entity_result = adapter.execute_cypher(entity_query)
        
        if entity_result and len(entity_result) > 1 and entity_result[1]:
            print(f"✅ Found {len(entity_result[1])} recent entities:")
            for i, row in enumerate(entity_result[1]):
                entity = row[0]
                print(f"\n   Entity {i+1}: {entity}")
                
                # Try to access individual properties
                try:
                    # Check if it's a dict-like object or has properties
                    if hasattr(entity, 'properties'):
                        print(f"   Properties: {entity.properties}")
                    elif isinstance(entity, dict):
                        print(f"   Dict keys: {entity.keys()}")
                        for key, value in entity.items():
                            print(f"     {key}: {value}")
                    else:
                        print(f"   Type: {type(entity)}")
                        print(f"   Dir: {dir(entity)}")
                except Exception as e:
                    print(f"   Error accessing properties: {e}")
        else:
            print("❌ No entities found")
            
        # Also check a specific entity by name
        print("\n2. Checking specific entity by name...")
        specific_query = '''
        MATCH (e:Entity {name: 'Heavy metal / chemical exposure'})
        RETURN e.uuid as uuid, e.name as name, e.type as type, e.extraction_method as method, e.created_at as created_at
        '''
        specific_result = adapter.execute_cypher(specific_query)
        
        if specific_result and len(specific_result) > 1 and specific_result[1]:
            print("✅ Found specific entity:")
            for row in specific_result[1]:
                print(f"   UUID: {row[0]}")
                print(f"   Name: {row[1]}")
                print(f"   Type: {row[2]}")
                print(f"   Method: {row[3]}")
                print(f"   Created: {row[4]}")
        else:
            print("❌ Specific entity not found")
        
    except Exception as e:
        print(f"❌ Error during checking: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_entity_structure())
