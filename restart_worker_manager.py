#!/usr/bin/env python3
"""
Restart the worker manager to clear stuck queues.
"""

import asyncio
from utils.logging_utils import get_logger
from workers import get_worker_manager

logger = get_logger(__name__)

async def restart_worker_manager():
    """Restart the worker manager to clear stuck queues."""
    
    try:
        logger.info("Getting worker manager...")
        worker_manager = await get_worker_manager()
        
        # Get current status
        status = worker_manager.get_status()
        logger.info(f"Current worker manager status: {status}")
        
        # Get queue sizes before restart
        queue_sizes = worker_manager.get_queue_sizes()
        logger.info(f"Queue sizes before restart: {queue_sizes}")
        
        # Stop the worker manager
        logger.info("Stopping worker manager...")
        await worker_manager.stop()
        logger.info("✅ Worker manager stopped")
        
        # Start it again
        logger.info("Starting worker manager...")
        await worker_manager.start()
        logger.info("✅ Worker manager restarted")
        
        # Get queue sizes after restart
        queue_sizes_after = worker_manager.get_queue_sizes()
        logger.info(f"Queue sizes after restart: {queue_sizes_after}")
        
        # Get new status
        new_status = worker_manager.get_status()
        logger.info(f"New worker manager status: {new_status}")
        
        logger.info("🎉 Worker manager successfully restarted - queues should be cleared!")
        
    except Exception as e:
        logger.error(f"Error restarting worker manager: {e}")

if __name__ == "__main__":
    asyncio.run(restart_worker_manager())
