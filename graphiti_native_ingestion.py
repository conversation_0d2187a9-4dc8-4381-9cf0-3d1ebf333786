#!/usr/bin/env python3
"""
Native Graphiti ingestion pipeline using the latest graphiti-core v0.18.0 with FalkorDB support.
This uses the official Graphiti API instead of custom implementations.
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

# Import Graphiti
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

# Import existing utilities for document processing
from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config

class GraphitiNativeProcessor:
    """Document processor using native Graphiti v0.18.0 with FalkorDB."""
    
    def __init__(self):
        self.graphiti = None
        self.config = get_config()
        
        # Statistics
        self.stats = {
            'documents_processed': 0,
            'episodes_created': 0,
            'total_entities_extracted': 0,
            'total_relationships_created': 0
        }
    
    async def initialize(self):
        """Initialize Graphiti with FalkorDB connection."""
        try:
            # FalkorDB connection parameters
            falkor_host = os.environ.get('FALKORDB_HOST', 'localhost')
            falkor_port = os.environ.get('FALKORDB_PORT', '6379')
            falkor_password = os.environ.get('FALKORDB_PASSWORD', '')

            # Try different connection methods for FalkorDB
            # Method 1: Redis-style URI (FalkorDB uses Redis protocol)
            falkor_uri = f"redis://{falkor_host}:{falkor_port}"

            try:
                # Initialize Graphiti with Redis-style URI for FalkorDB
                self.graphiti = Graphiti(
                    uri=falkor_uri,
                    user="",  # FalkorDB typically doesn't use user/password
                    password=falkor_password
                )
                logger.info(f"✅ Connected to FalkorDB using Redis URI: {falkor_uri}")

            except Exception as e1:
                logger.warning(f"Redis URI connection failed: {e1}")

                # Method 2: Try bolt-style URI (in case Graphiti expects Neo4j format)
                bolt_uri = f"bolt://{falkor_host}:{falkor_port}"
                try:
                    self.graphiti = Graphiti(
                        uri=bolt_uri,
                        user="neo4j",  # Default Neo4j user
                        password=falkor_password or "password"
                    )
                    logger.info(f"✅ Connected to FalkorDB using Bolt URI: {bolt_uri}")

                except Exception as e2:
                    logger.error(f"Both connection methods failed:")
                    logger.error(f"  Redis URI: {e1}")
                    logger.error(f"  Bolt URI: {e2}")
                    raise e2

            # Build indices and constraints (only needs to be done once)
            await self.graphiti.build_indices_and_constraints()

            logger.info("✅ Graphiti with FalkorDB initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Graphiti: {e}")
            raise
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document using native Graphiti API."""
        try:
            logger.info(f"🔄 Processing document: {file_path}")
            
            # Extract text from document using existing OCR
            ocr_processor = MistralOCRProcessor()
            ocr_result = await ocr_processor.process_document(file_path)
            
            if not ocr_result.get('success'):
                return {'success': False, 'error': 'OCR processing failed'}
            
            text_content = ocr_result.get('text', '')
            
            if not text_content.strip():
                return {'success': False, 'error': 'No text content extracted'}
            
            # Create episode name from file path
            doc_name = Path(file_path).stem
            episode_name = f"Document: {doc_name}"
            
            # Add episode to Graphiti
            # This automatically extracts entities, creates relationships, and builds the knowledge graph
            episode_result = await self.graphiti.add_episode(
                name=episode_name,
                episode_body=text_content,
                source=EpisodeType.text,
                source_description=f"Document processing of {Path(file_path).name}",
                reference_time=datetime.now(timezone.utc)
            )
            
            self.stats['documents_processed'] += 1
            self.stats['episodes_created'] += 1
            
            logger.info(f"✅ Successfully processed {file_path}")
            logger.info(f"   Episode UUID: {episode_result}")
            
            return {
                'success': True,
                'episode_uuid': str(episode_result),
                'episode_name': episode_name,
                'text_length': len(text_content),
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing document {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def search_knowledge_graph(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search the knowledge graph using Graphiti's hybrid search."""
        try:
            logger.info(f"🔍 Searching for: {query}")
            
            # Use Graphiti's built-in hybrid search
            results = await self.graphiti.search(query, limit=limit)
            
            search_results = []
            for result in results:
                search_results.append({
                    'uuid': result.uuid,
                    'fact': result.fact,
                    'source_node_uuid': result.source_node_uuid,
                    'target_node_uuid': result.target_node_uuid,
                    'valid_at': result.valid_at.isoformat() if result.valid_at else None,
                    'invalid_at': result.invalid_at.isoformat() if result.invalid_at else None,
                    'score': getattr(result, 'score', None)
                })
            
            logger.info(f"✅ Found {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"❌ Search error: {e}")
            return []
    
    async def get_graph_statistics(self) -> Dict[str, Any]:
        """Get statistics about the knowledge graph."""
        try:
            # Note: These methods may need to be implemented based on the actual Graphiti API
            # For now, we'll return basic stats
            stats = {
                'episodes_processed': self.stats['episodes_created'],
                'documents_processed': self.stats['documents_processed'],
                'status': 'active'
            }
            
            logger.info(f"📊 Graph statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error getting statistics: {e}")
            return {'error': str(e)}
    
    async def process_directory(self, directory_path: str, file_extensions: List[str] = None) -> Dict[str, Any]:
        """Process all documents in a directory."""
        if file_extensions is None:
            file_extensions = ['.pdf', '.txt', '.docx']
        
        directory = Path(directory_path)
        if not directory.exists():
            return {'success': False, 'error': f'Directory not found: {directory_path}'}
        
        # Find all matching files
        files = []
        for ext in file_extensions:
            files.extend(directory.glob(f'**/*{ext}'))
        
        if not files:
            return {'success': False, 'error': f'No files found with extensions {file_extensions}'}
        
        logger.info(f"🔄 Processing {len(files)} files from {directory_path}")
        
        results = []
        successful = 0
        failed = 0
        
        for file_path in files:
            try:
                result = await self.process_document(str(file_path))
                results.append({
                    'file': str(file_path),
                    'success': result['success'],
                    'episode_uuid': result.get('episode_uuid'),
                    'error': result.get('error')
                })
                
                if result['success']:
                    successful += 1
                else:
                    failed += 1
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                results.append({
                    'file': str(file_path),
                    'success': False,
                    'error': str(e)
                })
                failed += 1
        
        return {
            'success': True,
            'files_processed': len(files),
            'successful': successful,
            'failed': failed,
            'results': results
        }
    
    def print_stats(self):
        """Print processing statistics."""
        print(f"\n📊 GRAPHITI NATIVE PROCESSING STATISTICS:")
        print(f"  📄 Documents processed: {self.stats['documents_processed']}")
        print(f"  📝 Episodes created: {self.stats['episodes_created']}")
        print(f"  🎯 Using: Graphiti v0.18.0 with native FalkorDB support")
    
    async def close(self):
        """Close Graphiti connection."""
        if self.graphiti:
            await self.graphiti.close()
            logger.info("✅ Graphiti connection closed")

async def main():
    """Main processing function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python graphiti_native_ingestion.py <document_path>")
        print("  Directory:   python graphiti_native_ingestion.py --dir <directory_path>")
        print("  Search:      python graphiti_native_ingestion.py --search '<query>'")
        print("  Stats:       python graphiti_native_ingestion.py --stats")
        return
    
    processor = GraphitiNativeProcessor()
    
    try:
        await processor.initialize()
        
        if sys.argv[1] == '--dir':
            if len(sys.argv) < 3:
                print("Error: Directory path required")
                return
            
            directory_path = sys.argv[2]
            result = await processor.process_directory(directory_path)
            
            if result['success']:
                print(f"✅ Batch processing complete:")
                print(f"  📁 Files processed: {result['files_processed']}")
                print(f"  ✅ Successful: {result['successful']}")
                print(f"  ❌ Failed: {result['failed']}")
                processor.print_stats()
            else:
                print(f"❌ Batch processing failed: {result.get('error')}")
        
        elif sys.argv[1] == '--search':
            if len(sys.argv) < 3:
                print("Error: Search query required")
                return
            
            query = sys.argv[2]
            results = await processor.search_knowledge_graph(query)
            
            print(f"🔍 Search Results for: '{query}'")
            print("=" * 60)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['fact']}")
                    print(f"   UUID: {result['uuid']}")
                    if result['valid_at']:
                        print(f"   Valid from: {result['valid_at']}")
                    print()
            else:
                print("No results found.")
        
        elif sys.argv[1] == '--stats':
            stats = await processor.get_graph_statistics()
            print("📊 Knowledge Graph Statistics:")
            print("=" * 40)
            for key, value in stats.items():
                print(f"  {key}: {value}")
        
        else:
            # Single document processing
            document_path = sys.argv[1]
            result = await processor.process_document(document_path)
            
            if result['success']:
                print(f"✅ Successfully processed: {document_path}")
                print(f"   Episode UUID: {result['episode_uuid']}")
                print(f"   Text length: {result['text_length']} characters")
                processor.print_stats()
            else:
                print(f"❌ Failed to process: {document_path}")
                print(f"   Error: {result.get('error')}")
    
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())
