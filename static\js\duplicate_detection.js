/**
 * Document Duplicate Detection Modal
 * 
 * This module handles the duplicate detection popup that appears when
 * a potentially duplicate document is uploaded.
 */

class DuplicateDetectionModal {
    constructor() {
        this.modal = null;
        this.currentFile = null;
        this.duplicateData = null;
        this.onDecisionCallback = null;
        
        this.initializeModal();
    }
    
    initializeModal() {
        // Create modal HTML if it doesn't exist
        if (!document.getElementById('duplicate-detection-modal')) {
            this.createModalHTML();
        }
        
        this.modal = new bootstrap.Modal(document.getElementById('duplicate-detection-modal'));
        this.bindEvents();
    }
    
    createModalHTML() {
        const modalHTML = `
        <!-- Duplicate Detection Modal -->
        <div class="modal fade" id="duplicate-detection-modal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle"></i> 
                            Potential Duplicate Document Detected
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-info-circle"></i>
                            <strong>We found similar documents in your knowledge base.</strong>
                            Please review the matches below and decide how to proceed.
                        </div>
                        
                        <!-- Current File Info -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-file-earmark-text"></i> 
                                    Current File
                                </h6>
                            </div>
                            <div class="card-body" id="current-file-info">
                                <!-- Current file details will be populated here -->
                            </div>
                        </div>
                        
                        <!-- Duplicate Matches -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-files"></i> 
                                    Similar Documents Found (<span id="match-count">0</span>)
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="duplicate-matches-container">
                                    <!-- Duplicate matches will be populated here -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recommendation -->
                        <div class="mt-4">
                            <div class="alert" id="recommendation-alert">
                                <div id="recommendation-content">
                                    <!-- Recommendation will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> Cancel Upload
                        </button>
                        <button type="button" class="btn btn-danger" id="skip-processing-btn">
                            <i class="bi bi-skip-forward"></i> Skip (Don't Process)
                        </button>
                        <button type="button" class="btn btn-success" id="proceed-processing-btn">
                            <i class="bi bi-check-circle"></i> Process Anyway
                        </button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    bindEvents() {
        // Skip processing button
        document.getElementById('skip-processing-btn').addEventListener('click', () => {
            this.handleDecision('skip');
        });
        
        // Proceed with processing button
        document.getElementById('proceed-processing-btn').addEventListener('click', () => {
            this.handleDecision('process');
        });
        
        // Cancel button (modal dismiss)
        this.modal._element.addEventListener('hidden.bs.modal', () => {
            if (this.onDecisionCallback) {
                this.handleDecision('cancel');
            }
        });
    }
    
    show(fileInfo, duplicateData, onDecisionCallback) {
        this.currentFile = fileInfo;
        this.duplicateData = duplicateData;
        this.onDecisionCallback = onDecisionCallback;
        
        this.populateModal();
        this.modal.show();
    }
    
    populateModal() {
        // Populate current file info
        this.populateCurrentFileInfo();
        
        // Populate duplicate matches
        this.populateDuplicateMatches();
        
        // Show recommendation
        this.showRecommendation();
        
        // Update match count
        document.getElementById('match-count').textContent = this.duplicateData.matches.length;
    }
    
    populateCurrentFileInfo() {
        const container = document.getElementById('current-file-info');
        const fileSize = this.formatFileSize(this.currentFile.size || 0);
        
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Filename:</strong> ${this.escapeHtml(this.currentFile.name)}
                </div>
                <div class="col-md-6">
                    <strong>File Size:</strong> ${fileSize}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <strong>Type:</strong> ${this.currentFile.type || 'Unknown'}
                </div>
                <div class="col-md-6">
                    <strong>Last Modified:</strong> ${this.formatDate(this.currentFile.lastModified)}
                </div>
            </div>
        `;
    }
    
    populateDuplicateMatches() {
        const container = document.getElementById('duplicate-matches-container');
        
        if (!this.duplicateData.matches || this.duplicateData.matches.length === 0) {
            container.innerHTML = '<p class="text-muted">No similar documents found.</p>';
            return;
        }
        
        let matchesHTML = '';
        
        this.duplicateData.matches.forEach((match, index) => {
            const similarityPercentage = Math.round(match.similarity_score * 100);
            const matchTypeIcon = this.getMatchTypeIcon(match.match_type);
            const matchTypeBadge = this.getMatchTypeBadge(match.match_type);
            const progressBarClass = this.getSimilarityBarClass(match.similarity_score);
            
            matchesHTML += `
                <div class="card mb-3 ${match.match_type === 'exact_hash' ? 'border-danger' : 'border-warning'}">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="card-title mb-1">
                                    ${matchTypeIcon} ${this.escapeHtml(match.filename)}
                                    ${matchTypeBadge}
                                </h6>
                                <p class="card-text text-muted mb-1">
                                    <small>
                                        <i class="bi bi-folder"></i> ${this.escapeHtml(match.file_path)}
                                    </small>
                                </p>
                                <p class="card-text text-muted mb-0">
                                    <small>
                                        <i class="bi bi-clock"></i> Processed: ${this.formatDate(match.processed_at)}
                                        ${match.file_size ? `| <i class="bi bi-file-earmark"></i> ${this.formatFileSize(match.file_size)}` : ''}
                                    </small>
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-2">
                                    <span class="badge bg-primary fs-6">${similarityPercentage}% Similar</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar ${progressBarClass}" 
                                         role="progressbar" 
                                         style="width: ${similarityPercentage}%"
                                         aria-valuenow="${similarityPercentage}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">Confidence: ${Math.round(match.confidence * 100)}%</small>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <button class="btn btn-sm btn-outline-primary" onclick="duplicateModal.viewDocumentDetails('${match.document_id}')">
                                    <i class="bi bi-eye"></i> View Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = matchesHTML;
    }
    
    showRecommendation() {
        const alert = document.getElementById('recommendation-alert');
        const content = document.getElementById('recommendation-content');
        
        let alertClass = 'alert-info';
        let icon = 'bi-info-circle';
        let message = '';
        
        switch (this.duplicateData.recommendation) {
            case 'skip':
                alertClass = 'alert-danger';
                icon = 'bi-x-circle';
                message = '<strong>Recommendation: Skip Processing</strong><br>An exact duplicate was found. Processing this document would create redundant data.';
                break;
            case 'review':
                alertClass = 'alert-warning';
                icon = 'bi-exclamation-triangle';
                message = '<strong>Recommendation: Review Required</strong><br>High similarity detected. Please review the matches and decide whether to proceed.';
                break;
            case 'process':
                alertClass = 'alert-success';
                icon = 'bi-check-circle';
                message = '<strong>Recommendation: Safe to Process</strong><br>While similar documents exist, this appears to be sufficiently different to warrant processing.';
                break;
        }
        
        alert.className = `alert ${alertClass}`;
        content.innerHTML = `<i class="${icon}"></i> ${message}`;
    }
    
    getMatchTypeIcon(matchType) {
        const icons = {
            'exact_hash': '<i class="bi bi-exclamation-triangle text-danger"></i>',
            'filename': '<i class="bi bi-file-text text-warning"></i>',
            'content': '<i class="bi bi-file-earmark-text text-info"></i>',
            'metadata': '<i class="bi bi-info-circle text-secondary"></i>'
        };
        return icons[matchType] || '<i class="bi bi-question-circle"></i>';
    }
    
    getMatchTypeBadge(matchType) {
        const badges = {
            'exact_hash': '<span class="badge bg-danger ms-2">Exact Match</span>',
            'filename': '<span class="badge bg-warning ms-2">Filename</span>',
            'content': '<span class="badge bg-info ms-2">Content</span>',
            'metadata': '<span class="badge bg-secondary ms-2">Metadata</span>'
        };
        return badges[matchType] || '<span class="badge bg-light text-dark ms-2">Unknown</span>';
    }
    
    getSimilarityBarClass(similarity) {
        if (similarity >= 0.95) return 'bg-danger';
        if (similarity >= 0.85) return 'bg-warning';
        if (similarity >= 0.70) return 'bg-info';
        return 'bg-success';
    }
    
    handleDecision(decision) {
        if (this.onDecisionCallback) {
            this.onDecisionCallback(decision, this.currentFile, this.duplicateData);
            this.onDecisionCallback = null; // Prevent multiple calls
        }
        this.modal.hide();
    }
    
    async viewDocumentDetails(documentId) {
        try {
            const response = await fetch(`/api/documents/${documentId}`);
            if (!response.ok) throw new Error('Failed to load document details');
            
            const doc = await response.json();
            
            // Create a simple details modal or redirect to document view
            alert(`Document Details:\n\nFilename: ${doc.name}\nChunks: ${doc.chunks}\nEntities: ${doc.entities}\nReferences: ${doc.references}\nProcessed: ${doc.processed_at}`);
            
        } catch (error) {
            console.error('Error loading document details:', error);
            alert('Error loading document details: ' + error.message);
        }
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatDate(dateInput) {
        if (!dateInput) return 'Unknown';
        
        let date;
        if (typeof dateInput === 'number') {
            date = new Date(dateInput);
        } else if (typeof dateInput === 'string') {
            date = new Date(dateInput);
        } else {
            return 'Unknown';
        }
        
        if (isNaN(date.getTime())) return 'Unknown';
        
        return date.toLocaleString();
    }
}

// Global instance
let duplicateModal = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    duplicateModal = new DuplicateDetectionModal();
});

// Export for use in other modules
window.DuplicateDetectionModal = DuplicateDetectionModal;
