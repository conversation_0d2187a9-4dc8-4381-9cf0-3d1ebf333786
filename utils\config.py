"""
Configuration utilities for the Graphiti application.
"""

import os
import logging
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file (override system variables)
load_dotenv(override=True)

# Define paths
BASE_DIR = Path(__file__).resolve().parent.parent
UPLOADS_DIR = BASE_DIR / "uploads"
PROCESSED_DIR = BASE_DIR / "processed"
DOCUMENTS_DIR = BASE_DIR / "documents"
REFERENCES_DIR = BASE_DIR / "references"
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"

# Ensure directories exist
os.makedirs(UPLOADS_DIR, exist_ok=True)
os.makedirs(PROCESSED_DIR, exist_ok=True)
os.makedirs(DOCUMENTS_DIR, exist_ok=True)
os.makedirs(REFERENCES_DIR, exist_ok=True)

# FalkorDB configuration
FALKORDB_HOST = os.getenv("FALKORDB_HOST", "localhost")
FALKORDB_PORT = int(os.getenv("FALKORDB_PORT", "6379"))
FALKORDB_PASSWORD = os.getenv("FALKORDB_PASSWORD", "")
FALKORDB_GRAPH = os.getenv("FALKORDB_GRAPH", "graphiti_knowledge")

# LLM configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", os.getenv("OPEN_ROUTER_API_KEY", ""))
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY", "")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")

# LLM model configuration
LLM_PROVIDER = os.getenv("QA_LLM_PROVIDER", "openrouter")
LLM_MODEL = os.getenv("QA_LLM_MODEL", "meta-llama/llama-4-maverick")

# Embedding configuration
USE_LOCAL_EMBEDDINGS = os.getenv("USE_LOCAL_EMBEDDINGS", "false").lower() == "true"
EMBEDDING_PROVIDER = os.getenv("EMBEDDING_PROVIDER", "openai")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "1200"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "0"))
RECURSIVE_CHUNKING = os.getenv("RECURSIVE_CHUNKING", "true").lower() == "true"

# OCR configuration
OCR_PROVIDER = os.getenv("OCR_PROVIDER", "mistral")
OCR_MODEL = os.getenv("OCR_MODEL", "mistral-ocr-latest")

# Web server configuration
HOST = os.getenv("HOST", "127.0.0.1")  # Changed from 0.0.0.0 to localhost for better Windows compatibility
PORT = int(os.getenv("PORT", "9753"))

# Function to get available LLM models from .env file
def get_available_models():
    """Get available LLM models from .env file"""
    available_models = []
    try:
        with open(BASE_DIR / ".env", "r") as f:
            lines = f.readlines()
            for line in lines:
                # Look for OPEN_ROUTER_MODELS or similar variables
                if "OPEN_ROUTER_MODELS" in line and "=" in line:
                    # Extract the value part (after the equals sign)
                    models_str = line.split("=", 1)[1].strip()
                    # Remove quotes if present
                    models_str = models_str.strip('"\'')
                    # Split by comma
                    models = [model.strip() for model in models_str.split(",")]
                    # Add models to the list if not already present
                    for model in models:
                        if model and model not in available_models:
                            available_models.append(model)

                # Also check for the old format
                if line.strip() == "#Free Models From Open Router":
                    # Process models in the old format (one per line)
                    for model_line in lines[lines.index(line)+1:]:
                        if model_line.strip() and not model_line.startswith("#") and "=" not in model_line:
                            model = model_line.strip()
                            if model and model not in available_models:  # Skip empty lines and duplicates
                                available_models.append(model)
                        if model_line.strip() == "" or (model_line.startswith("#") and "Free Models" not in model_line):
                            break
    except Exception as e:
        logger.error(f"Error reading available models from .env file: {e}")

    # Add default models if list is empty or too short
    default_models = [
        "meta-llama/llama-4-maverick",
        "anthropic/claude-3-opus-20240229",
        "anthropic/claude-3-sonnet-20240229",
        "google/gemini-1.5-pro-latest",
        "mistralai/mistral-large-latest",
        "qwen3-4b",
        "qwen3-30b-a3b",
        "nvidia/llama-3.3-nemotron-super-49b-v1:free",
        "huggingfaceh4/zephyr-7b-beta",
        "mistralai/mistral-nemo",
        "google/gemma-3-27b-it"
    ]

    # If no models found or list is too short, add default models
    if not available_models or len(available_models) < 3:
        logger.warning(f"No models or too few models found in .env file, using default models")
        available_models = default_models

    # Make sure we have at least the most important models
    for model in default_models:
        if model not in available_models:
            available_models.append(model)

    # Remove duplicates while preserving order
    unique_models = []
    for model in available_models:
        if model not in unique_models:
            unique_models.append(model)

    return unique_models

# Function to load worker configuration from YAML file
def load_worker_config():
    """Load worker configuration from YAML file"""
    worker_config = {}
    try:
        config_path = BASE_DIR / "config" / "workers.yaml"
        if config_path.exists():
            with open(config_path, "r") as f:
                worker_config = yaml.safe_load(f)
            logger.info(f"Loaded worker configuration from {config_path}")
        else:
            logger.warning(f"Worker configuration file not found: {config_path}")
    except Exception as e:
        logger.error(f"Error loading worker configuration: {e}")

    return worker_config

# Function to get configuration as a dictionary
def get_config():
    """Get configuration as a dictionary"""
    # Load worker configuration
    worker_config = load_worker_config()

    config = {
        "falkordb": {
            "host": FALKORDB_HOST,
            "port": FALKORDB_PORT,
            "password": FALKORDB_PASSWORD,
            "graph": FALKORDB_GRAPH
        },
        "llm": {
            "provider": LLM_PROVIDER,
            "model": LLM_MODEL,
            "available_models": get_available_models()
        },
        "embedding": {
            "provider": EMBEDDING_PROVIDER,
            "model": EMBEDDING_MODEL,
            "use_local": USE_LOCAL_EMBEDDINGS,
            "chunk_size": CHUNK_SIZE,
            "chunk_overlap": CHUNK_OVERLAP,
            "recursive_chunking": RECURSIVE_CHUNKING
        },
        "ocr": {
            "provider": OCR_PROVIDER,
            "model": OCR_MODEL
        },
        "server": {
            "host": HOST,
            "port": PORT
        },
        "paths": {
            "base_dir": str(BASE_DIR),
            "uploads_dir": str(UPLOADS_DIR),
            "processed_dir": str(PROCESSED_DIR),
            "documents_dir": str(DOCUMENTS_DIR),
            "references_dir": str(REFERENCES_DIR),
            "static_dir": str(STATIC_DIR),
            "templates_dir": str(TEMPLATES_DIR)
        }
    }

    # Add worker configuration if available
    if worker_config:
        config["workers"] = worker_config

    return config

# Function to get settings
def get_settings():
    """
    Get application settings.

    Returns:
        Dictionary of settings
    """
    # Create settings dictionary
    settings = {
        "llm_settings": {
            "provider": LLM_PROVIDER,
            "model": LLM_MODEL,
            "api_key": OPENROUTER_API_KEY if LLM_PROVIDER == "openrouter" else OPENAI_API_KEY
        },
        "embedding_settings": {
            "model": EMBEDDING_MODEL,
            "chunk_size": CHUNK_SIZE,
            "overlap": CHUNK_OVERLAP
        },
        "database_settings": {
            "falkordb_host": FALKORDB_HOST,
            "falkordb_port": FALKORDB_PORT,
            "redis_host": FALKORDB_HOST,  # Using same host for Redis
            "redis_port": 6380  # Using different port for Redis
        },
        "system_settings": {
            "max_file_size": 50,
            "max_parallel_processes": 4,
            "log_level": "INFO"
        }
    }

    return settings

# Function to update configuration in .env file
def update_config(updates):
    """
    Update configuration in .env file

    Args:
        updates: Dictionary of key-value pairs to update
    """
    try:
        # Read the current .env file
        with open(BASE_DIR / ".env", "r") as f:
            lines = f.readlines()

        # Update the values
        updated_lines = []
        for line in lines:
            updated = False
            for key, value in updates.items():
                if line.startswith(f"{key}="):
                    updated_lines.append(f"{key}={value}\n")
                    updated = True
                    break

            if not updated:
                updated_lines.append(line)

        # Write the updated .env file
        with open(BASE_DIR / ".env", "w") as f:
            f.writelines(updated_lines)

        # Reload environment variables
        load_dotenv()

        return True
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        return False