#!/usr/bin/env python3
"""
Test the documents API endpoint.
"""

import requests

def test_documents_api():
    """Test the documents API."""
    try:
        # Test the documents API endpoint
        response = requests.get('http://localhost:9753/api/documents?page=1&page_size=5')
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Documents API Working:')
            print(f'   Total documents: {data.get("total", 0)}')
            print(f'   Documents on page: {len(data.get("documents", []))}')
            
            # Show sample documents
            for doc in data.get('documents', [])[:3]:
                print(f'   - {doc.get("name", "Unknown")}')
                
            return True
        else:
            print(f'❌ Documents API Error: {response.status_code}')
            print(f'   Response: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    test_documents_api()
