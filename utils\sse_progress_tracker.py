"""
Server-Sent Events (SSE) Progress Tracker for Real-Time Updates

This provides a more reliable alternative to WebSocket for real-time progress updates.
SSE is simpler, more reliable, and automatically reconnects.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from fastapi import Request
from fastapi.responses import StreamingResponse

from utils.logging_utils import get_logger

logger = get_logger(__name__)


class SSEProgressManager:
    """Manages Server-Sent Events for real-time progress updates."""
    
    def __init__(self):
        self.active_streams: Dict[str, asyncio.Queue] = {}
        self.operation_data: Dict[str, Dict[str, Any]] = {}
    
    async def add_stream(self, operation_id: str) -> asyncio.Queue:
        """Add a new SSE stream for an operation."""
        if operation_id not in self.active_streams:
            self.active_streams[operation_id] = asyncio.Queue()
            logger.info(f"📡 SSE stream created for operation: {operation_id}")
        return self.active_streams[operation_id]
    
    async def remove_stream(self, operation_id: str):
        """Remove SSE stream for an operation."""
        if operation_id in self.active_streams:
            del self.active_streams[operation_id]
            logger.info(f"📡 SSE stream removed for operation: {operation_id}")
    
    async def send_update(self, operation_id: str, data: Dict[str, Any]):
        """Send update to SSE stream."""
        if operation_id in self.active_streams:
            try:
                # Store the latest data
                self.operation_data[operation_id] = data
                
                # Send to stream
                await self.active_streams[operation_id].put(data)
                logger.debug(f"📡 SSE update sent for {operation_id}: {data.get('status', 'unknown')}")
            except Exception as e:
                logger.warning(f"Failed to send SSE update: {e}")
    
    async def get_stream_response(self, operation_id: str) -> StreamingResponse:
        """Get SSE streaming response for an operation."""
        queue = await self.add_stream(operation_id)
        
        async def event_stream():
            try:
                # Send initial connection event
                yield f"data: {json.dumps({'type': 'connected', 'operation_id': operation_id})}\n\n"
                
                # Send any existing data
                if operation_id in self.operation_data:
                    yield f"data: {json.dumps(self.operation_data[operation_id])}\n\n"
                
                # Stream updates
                while True:
                    try:
                        # Wait for update with timeout
                        data = await asyncio.wait_for(queue.get(), timeout=30.0)
                        yield f"data: {json.dumps(data)}\n\n"
                    except asyncio.TimeoutError:
                        # Send keepalive
                        yield f"data: {json.dumps({'type': 'keepalive', 'timestamp': time.time()})}\n\n"
                    except Exception as e:
                        logger.error(f"SSE stream error: {e}")
                        break
                        
            except Exception as e:
                logger.error(f"SSE event stream error: {e}")
            finally:
                await self.remove_stream(operation_id)
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )


# Global SSE manager
sse_manager = SSEProgressManager()


class SSEProgressTracker:
    """Enhanced progress tracker that uses SSE for real-time updates."""
    
    def __init__(self, operation_id: str, filename: str):
        self.operation_id = operation_id
        self.filename = filename
        self.start_time = datetime.now(timezone.utc)
        self.current_step = 0
        self.total_steps = 8
        self.overall_progress = 0
        self.status = "initializing"
        self.current_message = "Starting processing..."
        self.statistics = {
            'total_characters': 0,
            'total_chunks': 0,
            'total_entities': 0,
            'total_references': 0,
            'total_embeddings': 0
        }
        
    async def update_progress(self, step: int = None, message: str = None, progress: int = None, **kwargs):
        """Update progress and send SSE update immediately."""
        if step is not None:
            self.current_step = step
        if message is not None:
            self.current_message = message
        if progress is not None:
            self.overall_progress = progress
            
        # Update statistics
        for key, value in kwargs.items():
            if key in self.statistics:
                self.statistics[key] = value
        
        # Send immediate SSE update
        await self._send_sse_update()
    
    async def complete_step(self, step_name: str, message: str = "", **kwargs):
        """Complete a step and send immediate update."""
        self.current_step += 1
        self.overall_progress = min(100, (self.current_step / self.total_steps) * 100)
        self.current_message = message or f"{step_name} completed"
        
        # Update statistics
        for key, value in kwargs.items():
            if key in self.statistics:
                self.statistics[key] = value
        
        logger.info(f"✅ {self.filename}: {step_name} - {message}")
        await self._send_sse_update()
    
    async def complete_processing(self, message: str = "Processing completed successfully"):
        """Mark processing as complete."""
        self.status = "completed"
        self.overall_progress = 100
        self.current_message = message
        
        duration = (datetime.now(timezone.utc) - self.start_time).total_seconds()
        logger.info(f"🎉 {self.filename}: Processing completed in {duration:.2f}s")
        
        await self._send_sse_update(final=True)
    
    async def _send_sse_update(self, final: bool = False):
        """Send SSE update with current progress data."""
        try:
            data = {
                'type': 'final_complete' if final else 'progress_update',
                'operation_id': self.operation_id,
                'filename': self.filename,
                'status': self.status,
                'overall_progress': self.overall_progress,
                'current_step': self.current_step,
                'total_steps': self.total_steps,
                'current_message': self.current_message,
                'statistics': self.statistics.copy(),
                'timestamp': time.time()
            }
            
            await sse_manager.send_update(self.operation_id, data)
            
        except Exception as e:
            logger.warning(f"Failed to send SSE update: {e}")
