#!/usr/bin/env python3
"""
Identify and clean duplicate files from uploads directory.
"""

import os
from pathlib import Path
from collections import defaultdict
import hashlib

def get_clean_filename(filename):
    """Extract clean filename without UUID prefix."""
    stem = Path(filename).stem
    # Remove UUID prefix if present (format: uuid_filename)
    if '_' in stem and len(stem.split('_')[0]) >= 8:
        return '_'.join(stem.split('_')[1:]) + Path(filename).suffix
    return filename

def get_file_hash(filepath):
    """Get MD5 hash of file content."""
    try:
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except:
        return None

def main():
    uploads_dir = Path("uploads")
    
    # Group files by clean name
    files_by_clean_name = defaultdict(list)
    files_by_hash = defaultdict(list)
    
    print("🔍 ANALYZING DUPLICATE FILES IN UPLOADS DIRECTORY")
    print("=" * 60)
    
    # Collect all files
    all_files = []
    for ext in ['.pdf', '.txt', '.docx', '.doc', '.pptx', '.ppt']:
        all_files.extend(uploads_dir.glob(f'*{ext}'))
    
    print(f"📁 Total files found: {len(all_files)}")
    
    # Group by clean filename
    for file_path in all_files:
        clean_name = get_clean_filename(file_path.name)
        files_by_clean_name[clean_name].append(file_path)
    
    # Find duplicates by name
    name_duplicates = {name: files for name, files in files_by_clean_name.items() if len(files) > 1}
    
    print(f"\n📊 DUPLICATE ANALYSIS:")
    print(f"   Unique filenames: {len(files_by_clean_name)}")
    print(f"   Duplicate groups: {len(name_duplicates)}")
    
    files_to_delete = []
    
    print(f"\n🗂️ DUPLICATE GROUPS TO CLEAN:")
    print("-" * 50)
    
    for clean_name, duplicate_files in name_duplicates.items():
        print(f"\n📄 {clean_name} ({len(duplicate_files)} copies):")
        
        # Sort by modification time (keep newest)
        duplicate_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # Keep the first (newest) file, mark others for deletion
        keep_file = duplicate_files[0]
        delete_files = duplicate_files[1:]
        
        print(f"   ✅ KEEP: {keep_file.name}")
        for delete_file in delete_files:
            print(f"   ❌ DELETE: {delete_file.name}")
            files_to_delete.append(delete_file)
    
    # Also check for identical content (different names)
    print(f"\n🔍 CHECKING FOR IDENTICAL CONTENT...")
    hash_groups = defaultdict(list)
    
    for file_path in all_files:
        if file_path not in files_to_delete:  # Don't hash files already marked for deletion
            file_hash = get_file_hash(file_path)
            if file_hash:
                hash_groups[file_hash].append(file_path)
    
    content_duplicates = {hash_val: files for hash_val, files in hash_groups.items() if len(files) > 1}
    
    if content_duplicates:
        print(f"   Found {len(content_duplicates)} groups with identical content")
        for hash_val, duplicate_files in content_duplicates.items():
            if len(duplicate_files) > 1:
                print(f"\n📄 Identical content group ({len(duplicate_files)} files):")
                # Keep the file with the shortest name (usually the cleanest)
                duplicate_files.sort(key=lambda x: len(x.name))
                keep_file = duplicate_files[0]
                delete_files = duplicate_files[1:]
                
                print(f"   ✅ KEEP: {keep_file.name}")
                for delete_file in delete_files:
                    print(f"   ❌ DELETE: {delete_file.name}")
                    if delete_file not in files_to_delete:
                        files_to_delete.append(delete_file)
    
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"   Total files: {len(all_files)}")
    print(f"   Files to delete: {len(files_to_delete)}")
    print(f"   Files to keep: {len(all_files) - len(files_to_delete)}")
    
    if files_to_delete:
        print(f"\n🗑️ DELETING {len(files_to_delete)} DUPLICATE FILES...")
        deleted_count = 0
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                print(f"   ✅ Deleted: {file_path.name}")
                deleted_count += 1
            except Exception as e:
                print(f"   ❌ Failed to delete {file_path.name}: {e}")
        
        print(f"\n✅ CLEANUP COMPLETE!")
        print(f"   Successfully deleted: {deleted_count} files")
        print(f"   Remaining files: {len(all_files) - deleted_count}")
    else:
        print(f"\n✅ No duplicate files found to delete!")

if __name__ == "__main__":
    main()
