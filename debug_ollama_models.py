#!/usr/bin/env python3
"""Debug Ollama model availability."""

import requests
import json

def check_ollama_models():
    """Check what models are available via API."""
    try:
        response = requests.get('http://localhost:11434/api/tags')
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"📊 Found {len(models)} models via API:")
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                modified = model.get('modified_at', 'Unknown')
                print(f"   📄 {name} ({size:,} bytes, modified: {modified})")
            
            # Check for qwen variants
            qwen_models = [m for m in models if 'qwen' in m.get('name', '').lower()]
            if qwen_models:
                print(f"\n🔍 Found qwen models:")
                for model in qwen_models:
                    print(f"   📄 {model.get('name', 'Unknown')}")
            else:
                print(f"\n❌ No qwen models found in API")
            
            return models
        else:
            print(f"❌ API request failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_model_variants():
    """Test different model name variants."""
    variants = [
        'qwen2.5vl:3b',
        'qwen2.5vl',
        'qwen2.5vl:latest',
        'qwen2.5-vl:3b',
        'qwen2.5-vl',
        'qwen:2.5vl-3b'
    ]
    
    print(f"\n🧪 Testing model name variants:")
    
    for variant in variants:
        try:
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': variant,
                    'prompt': 'Test',
                    'stream': False
                },
                timeout=10
            )
            
            status = response.status_code
            if status == 200:
                print(f"   ✅ {variant}: Working!")
                return variant
            elif status == 404:
                print(f"   ❌ {variant}: Not found")
            else:
                print(f"   ⚠️ {variant}: Status {status}")
                
        except Exception as e:
            print(f"   ❌ {variant}: Error - {e}")
    
    return None

def main():
    """Main debug function."""
    print("🔍 Debugging Ollama Model Availability")
    print("=" * 40)
    
    # Check available models
    models = check_ollama_models()
    
    # Test variants
    working_variant = test_model_variants()
    
    if working_variant:
        print(f"\n🎉 Found working model name: {working_variant}")
    else:
        print(f"\n❌ No working qwen model variants found")
        print(f"📋 This suggests the model may not be properly installed for API access")
        print(f"💡 Try: ollama pull qwen2.5vl:3b")

if __name__ == "__main__":
    main()
