#!/usr/bin/env python3
"""
Check Redis embeddings created by the FalkorDB ingestion system.
"""

import redis
import json

def main():
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        
        # Check for embeddings
        keys = redis_client.keys('embedding:*')
        print(f'📊 Found {len(keys)} embeddings in Redis')
        
        if keys:
            # Check a sample embedding
            sample_key = keys[0]
            embedding_data = redis_client.hgetall(sample_key)
            
            print(f'\n🔍 Sample embedding: {sample_key}')
            print(f'   Keys: {list(embedding_data.keys())}')
            
            if 'metadata' in embedding_data:
                metadata = json.loads(embedding_data['metadata'])
                print(f'   Document ID: {metadata.get("document_id", "N/A")}')
                print(f'   Chunk Index: {metadata.get("chunk_index", "N/A")}')
                print(f'   Text Length: {metadata.get("text_length", "N/A")}')
                print(f'   Model: {metadata.get("embedding_model", "N/A")}')
                print(f'   Created: {metadata.get("created_at", "N/A")}')
            
            if 'embedding' in embedding_data:
                embedding = json.loads(embedding_data['embedding'])
                print(f'   Embedding dimension: {len(embedding)}')
                print(f'   First 5 values: {embedding[:5]}')
            
            # Show a few more keys
            print(f'\n📋 All embedding keys:')
            for i, key in enumerate(keys[:5]):
                print(f'   {i+1}. {key}')
            if len(keys) > 5:
                print(f'   ... and {len(keys) - 5} more')
        
        print('\n✅ Redis embeddings working correctly!')
        
    except Exception as e:
        print(f'❌ Error checking Redis: {e}')

if __name__ == "__main__":
    main()
