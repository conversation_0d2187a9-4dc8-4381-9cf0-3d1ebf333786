#!/usr/bin/env python3
"""
Test the upload functionality to ensure the unified pipeline is working
"""

import asyncio
import requests
import json
from pathlib import Path

async def test_upload_api():
    """Test the upload API endpoint."""
    
    print("🧪 TESTING UPLOAD FUNCTIONALITY")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("1️⃣ Testing server connectivity...")
    try:
        response = requests.get("http://localhost:9753/api/fast/graph-stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Server is running")
            print(f"   📊 Current stats: {stats['total_references']} references, {stats['total_episodes']} episodes")
        else:
            print(f"   ❌ Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Server connection failed: {e}")
        return False
    
    # Test 2: Check upload endpoint availability
    print("\n2️⃣ Testing upload endpoint...")
    try:
        response = requests.get("http://localhost:9753/docs", timeout=10)
        if response.status_code == 200:
            print(f"   ✅ API docs accessible")
        else:
            print(f"   ⚠️ API docs returned status {response.status_code}")
    except Exception as e:
        print(f"   ⚠️ API docs check failed: {e}")
    
    # Test 3: Test unified pipeline import
    print("\n3️⃣ Testing unified pipeline import...")
    try:
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print(f"   ✅ Unified pipeline imported successfully")
        print(f"   🔧 Pipeline type: {type(pipeline)}")
    except Exception as e:
        print(f"   ❌ Unified pipeline import failed: {e}")
        return False
    
    # Test 4: Test improved extractor
    print("\n4️⃣ Testing improved extractor...")
    try:
        from services.improved_aggressive_reference_extractor import get_improved_aggressive_reference_extractor
        extractor = await get_improved_aggressive_reference_extractor()
        print(f"   ✅ Improved extractor imported successfully")
        print(f"   🔧 Extractor type: {type(extractor)}")
    except Exception as e:
        print(f"   ❌ Improved extractor import failed: {e}")
        return False
    
    # Test 5: Test a simple text processing
    print("\n5️⃣ Testing text processing...")
    try:
        test_text = """
        This is a test document with some references:
        1. Smith, J. (2023). Test paper. Journal of Testing, 1(1), 1-10.
        2. See also: https://example.com/test
        """
        
        result = await extractor.extract_references_from_text(test_text, "test.txt")
        refs_found = result.get('total_found', 0)
        print(f"   ✅ Text processing successful")
        print(f"   📚 Found {refs_found} references in test text")
        print(f"   🎯 Confidence: {result.get('confidence_score', 0):.2f}")
    except Exception as e:
        print(f"   ❌ Text processing failed: {e}")
        return False
    
    # Test 6: Check if there are any test files we can process
    print("\n6️⃣ Looking for test files...")
    test_dirs = ["uploads", "documents", "temp"]
    test_files = []
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            for file_path in Path(test_dir).glob("*.pdf"):
                test_files.append(file_path)
                if len(test_files) >= 3:  # Limit to 3 files
                    break
    
    if test_files:
        print(f"   📄 Found {len(test_files)} test files:")
        for i, file_path in enumerate(test_files, 1):
            print(f"      {i}. {file_path.name}")
        
        # Test processing one file
        print(f"\n   🧪 Testing file processing with: {test_files[0].name}")
        try:
            # Test just the reference extraction part
            from utils.mistral_ocr import MistralOCRProcessor
            ocr_processor = MistralOCRProcessor()
            
            # Extract text (with timeout)
            ocr_result = await asyncio.wait_for(
                ocr_processor.process_pdf(str(test_files[0])),
                timeout=60
            )
            
            if ocr_result.get('success', False):
                text = ocr_result.get('text', '')
                print(f"      ✅ OCR successful: {len(text):,} characters")
                
                # Test reference extraction
                ref_result = await asyncio.wait_for(
                    extractor.extract_references_from_text(text, test_files[0].name),
                    timeout=60
                )
                
                refs_found = ref_result.get('total_found', 0)
                print(f"      ✅ Reference extraction successful: {refs_found} references")
                
            else:
                print(f"      ⚠️ OCR failed for test file")
                
        except asyncio.TimeoutError:
            print(f"      ⏰ Processing timeout (this is expected for large files)")
        except Exception as e:
            print(f"      ⚠️ File processing test failed: {e}")
    else:
        print(f"   ℹ️ No test files found in {test_dirs}")
    
    print(f"\n🎉 UPLOAD FUNCTIONALITY TEST COMPLETE!")
    print(f"✅ The system appears to be working correctly")
    print(f"📱 You can now test uploads through the web interface at:")
    print(f"   http://localhost:9753")
    
    return True

if __name__ == "__main__":
    asyncio.run(test_upload_api())
