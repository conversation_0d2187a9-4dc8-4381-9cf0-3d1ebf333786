#!/usr/bin/env python3
"""
Comprehensive database integrity verification script.
Checks that entities, facts, episodes, and relationships are properly stored in FalkorDB.
"""

import asyncio
import json
from datetime import datetime
from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class DatabaseIntegrityChecker:
    def __init__(self):
        self.adapter = GraphitiFalkorDBAdapter('graphiti')
        self.issues = []
        self.stats = {}
    
    async def run_full_check(self):
        """Run comprehensive database integrity check."""
        print("🔍 Starting comprehensive database integrity check...")
        print("=" * 60)
        
        # Basic counts
        await self._check_basic_counts()
        
        # Schema validation
        await self._check_schema()
        
        # Data integrity
        await self._check_data_integrity()
        
        # Relationship integrity
        await self._check_relationship_integrity()
        
        # Recent document verification
        await self._check_recent_documents()
        
        # Generate report
        await self._generate_report()
    
    async def _check_basic_counts(self):
        """Check basic node and relationship counts."""
        print("\n📊 Basic Counts:")
        
        queries = {
            'Episodes': 'MATCH (ep:Episode) RETURN count(ep) as count',
            'Facts': 'MATCH (f:Fact) RETURN count(f) as count',
            'Entities': 'MATCH (e:Entity) RETURN count(e) as count',
            'CONTAINS relationships': 'MATCH ()-[r:CONTAINS]->() RETURN count(r) as count',
            'MENTIONS relationships': 'MATCH ()-[r:MENTIONS]->() RETURN count(r) as count'
        }
        
        for name, query in queries.items():
            result = self.adapter.execute_cypher(query)
            if result and len(result) > 1 and result[1]:
                count = result[1][0][0]
                self.stats[name] = count
                print(f"  {name}: {count:,}")
            else:
                self.stats[name] = 0
                self.issues.append(f"Failed to get count for {name}")
    
    async def _check_schema(self):
        """Check database schema consistency."""
        print("\n🏗️ Schema Validation:")
        
        # Check required labels exist
        required_labels = ['Episode', 'Fact', 'Entity']
        query = 'CALL db.labels()'
        result = self.adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            existing_labels = [row[0] for row in result[1]]
            for label in required_labels:
                if label in existing_labels:
                    print(f"  ✅ {label} label exists")
                else:
                    print(f"  ❌ {label} label missing")
                    self.issues.append(f"Missing required label: {label}")
        
        # Check required relationship types
        required_rels = ['CONTAINS', 'MENTIONS']
        query = 'CALL db.relationshipTypes()'
        result = self.adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            existing_rels = [row[0] for row in result[1]]
            for rel in required_rels:
                if rel in existing_rels:
                    print(f"  ✅ {rel} relationship exists")
                else:
                    print(f"  ❌ {rel} relationship missing")
                    self.issues.append(f"Missing required relationship: {rel}")
    
    async def _check_data_integrity(self):
        """Check data integrity issues."""
        print("\n🔍 Data Integrity:")
        
        # Check for entities without UUIDs
        query = 'MATCH (e:Entity) WHERE e.uuid IS NULL RETURN count(e) as count'
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} entities without UUID")
                self.issues.append(f"{count} entities missing UUID")
            else:
                print(f"  ✅ All entities have UUIDs")
        
        # Check for facts without UUIDs
        query = 'MATCH (f:Fact) WHERE f.uuid IS NULL RETURN count(f) as count'
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} facts without UUID")
                self.issues.append(f"{count} facts missing UUID")
            else:
                print(f"  ✅ All facts have UUIDs")
        
        # Check for episodes without UUIDs
        query = 'MATCH (ep:Episode) WHERE ep.uuid IS NULL RETURN count(ep) as count'
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} episodes without UUID")
                self.issues.append(f"{count} episodes missing UUID")
            else:
                print(f"  ✅ All episodes have UUIDs")
        
        # Check for entities without names
        query = 'MATCH (e:Entity) WHERE e.name IS NULL OR e.name = "" RETURN count(e) as count'
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} entities without names")
                self.issues.append(f"{count} entities missing names")
            else:
                print(f"  ✅ All entities have names")
        
        # Check for entities without types
        query = 'MATCH (e:Entity) WHERE e.type IS NULL OR e.type = "" RETURN count(e) as count'
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} entities without types")
                self.issues.append(f"{count} entities missing types")
            else:
                print(f"  ✅ All entities have types")
    
    async def _check_relationship_integrity(self):
        """Check relationship integrity."""
        print("\n🔗 Relationship Integrity:")
        
        # Check orphaned facts (facts not connected to episodes)
        query = '''
        MATCH (f:Fact)
        WHERE NOT EXISTS((f)<-[:CONTAINS]-(:Episode))
        RETURN count(f) as count
        '''
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} orphaned facts (not connected to episodes)")
                self.issues.append(f"{count} orphaned facts")
            else:
                print(f"  ✅ All facts connected to episodes")
        
        # Check orphaned entities (entities not mentioned by facts)
        query = '''
        MATCH (e:Entity)
        WHERE NOT EXISTS((e)<-[:MENTIONS]-(:Fact))
        RETURN count(e) as count
        '''
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ⚠️ {count} orphaned entities (not mentioned by facts)")
                # This might be normal for some use cases, so it's a warning not an error
            else:
                print(f"  ✅ All entities mentioned by facts")
        
        # Check episodes without facts
        query = '''
        MATCH (ep:Episode)
        WHERE NOT EXISTS((ep)-[:CONTAINS]->(:Fact))
        RETURN count(ep) as count
        '''
        result = self.adapter.execute_cypher(query)
        if result and len(result) > 1 and result[1]:
            count = result[1][0][0]
            if count > 0:
                print(f"  ❌ {count} episodes without facts")
                self.issues.append(f"{count} episodes without facts")
            else:
                print(f"  ✅ All episodes have facts")
    
    async def _check_recent_documents(self):
        """Check recent document processing."""
        print("\n📄 Recent Document Verification:")
        
        # Get the most recent document
        query = '''
        MATCH (ep:Episode)
        WHERE ep.processed_at IS NOT NULL
        RETURN ep.uuid, ep.name, ep.processed_at
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = self.adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            row = result[1][0]
            episode_uuid = row[0]
            episode_name = row[1]
            processed_at = row[2]
            
            print(f"  Most recent: {episode_name}")
            print(f"  Processed: {processed_at}")
            
            # Check facts for this episode
            query = f'''
            MATCH (ep:Episode {{uuid: '{episode_uuid}'}})-[:CONTAINS]->(f:Fact)
            RETURN count(f) as fact_count
            '''
            result = self.adapter.execute_cypher(query)
            if result and len(result) > 1 and result[1]:
                fact_count = result[1][0][0]
                print(f"  Facts: {fact_count}")
                
                if fact_count == 0:
                    self.issues.append(f"Recent episode {episode_name} has no facts")
            
            # Check entities for this episode
            query = f'''
            MATCH (ep:Episode {{uuid: '{episode_uuid}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(e:Entity)
            RETURN count(DISTINCT e) as entity_count
            '''
            result = self.adapter.execute_cypher(query)
            if result and len(result) > 1 and result[1]:
                entity_count = result[1][0][0]
                print(f"  Entities: {entity_count}")
                
                if entity_count == 0:
                    self.issues.append(f"Recent episode {episode_name} has no entities")
        else:
            print("  ❌ No recent documents found")
            self.issues.append("No recent documents found")
    
    async def _generate_report(self):
        """Generate final integrity report."""
        print("\n" + "=" * 60)
        print("📋 INTEGRITY REPORT")
        print("=" * 60)
        
        print(f"\n📊 Statistics:")
        for key, value in self.stats.items():
            print(f"  {key}: {value:,}")
        
        if self.issues:
            print(f"\n❌ Issues Found ({len(self.issues)}):")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
        else:
            print(f"\n✅ No critical issues found!")
        
        # Calculate some ratios
        if self.stats.get('Facts', 0) > 0 and self.stats.get('Entities', 0) > 0:
            ratio = self.stats['MENTIONS relationships'] / self.stats['Facts']
            print(f"\n📈 Metrics:")
            print(f"  Average entities per fact: {ratio:.2f}")
            
            if self.stats.get('Episodes', 0) > 0:
                facts_per_episode = self.stats['Facts'] / self.stats['Episodes']
                entities_per_episode = self.stats['Entities'] / self.stats['Episodes']
                print(f"  Average facts per episode: {facts_per_episode:.2f}")
                print(f"  Average entities per episode: {entities_per_episode:.2f}")
        
        print(f"\n🕐 Check completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    """Main function to run the integrity check."""
    checker = DatabaseIntegrityChecker()
    await checker.run_full_check()

if __name__ == "__main__":
    asyncio.run(main())
