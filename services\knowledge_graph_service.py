"""
Knowledge graph operations service for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter, create_relationship
from models.knowledge_graph import Node, Edge, GraphData, TaxonomyNode, Taxonomy

# Set up logger
logger = get_logger(__name__)

async def get_knowledge_graph(limit: int = 100) -> GraphData:
    """
    Get knowledge graph data.

    Args:
        limit: Maximum number of nodes to return

    Returns:
        Graph data
    """
    adapter = await get_falkordb_adapter()

    # Get nodes with direct property access (more efficient) - exclude test data
    nodes_query = f"""
    MATCH (n:Entity)
    WHERE n.uuid IS NOT NULL
      AND n.name IS NOT NULL
      AND n.name <> ''
      AND NOT n.name STARTS WITH 'Test'
      AND n.type IS NOT NULL
      AND n.type <> ''
    OPTIONAL MATCH (c:Chunk)-[:CONTAINS_ENTITY]->(n)
    WITH n, count(c) as mentions
    RETURN n.uuid, n.name, n.type, n.description, mentions
    ORDER BY mentions DESC, n.name ASC
    LIMIT {limit}
    """

    nodes_result = adapter.execute_cypher(nodes_query)

    nodes = []
    if nodes_result and len(nodes_result) > 1:
        for row in nodes_result[1]:
            try:
                # Direct property access format: [uuid, name, type, description, mentions]
                if len(row) >= 5:
                    uuid = row[0] if row[0] else ""
                    name = row[1] if row[1] else ""
                    node_type = row[2] if row[2] else "Unknown"
                    description = row[3] if row[3] else None
                    mentions = row[4] if row[4] else 1

                    # Only add nodes with valid data
                    if uuid and name and not name.startswith('Test'):
                        nodes.append(Node(
                            uuid=uuid,
                            type=node_type,
                            name=name,
                            body=description,
                            properties={
                                "uuid": uuid,
                                "name": name,
                                "type": node_type,
                                "description": description,
                                "mention_count": mentions
                            }
                        ))
                    else:
                        logger.warning(f"Skipping node with missing/test data: uuid={uuid}, name={name}")
                else:
                    logger.warning(f"Skipping row with insufficient data: {row}")

            except Exception as e:
                logger.error(f"Error processing node row: {e}")
                logger.error(f"Row data: {row}")
                continue

    # Get relationships with direct property access - exclude test data
    # Get relationships between real entities (not test data)
    relationships_query = f"""
    MATCH (n:Entity)-[r]->(m:Entity)
    WHERE n.uuid IS NOT NULL AND m.uuid IS NOT NULL
      AND n.name IS NOT NULL AND n.name <> ''
      AND m.name IS NOT NULL AND m.name <> ''
      AND NOT n.name STARTS WITH 'Test'
      AND NOT m.name STARTS WITH 'Test'
      AND n.type IS NOT NULL AND n.type <> ''
      AND m.type IS NOT NULL AND m.type <> ''
    RETURN n.uuid, m.uuid, type(r), n.name, m.name
    LIMIT {limit * 2}
    """

    relationships_result = adapter.execute_cypher(relationships_query)

    relationships = []
    if relationships_result and len(relationships_result) > 1:
        for row in relationships_result[1]:
            try:
                # Direct access format: [source_uuid, target_uuid, relationship_type, source_name, target_name]
                if len(row) >= 5:
                    source = row[0]
                    target = row[1]
                    rel_type = row[2]
                    source_name = row[3]
                    target_name = row[4]

                    # Only add valid relationships between real entities
                    if (source and target and rel_type and
                        source_name and target_name and
                        not source_name.startswith('Test') and
                        not target_name.startswith('Test')):
                        relationships.append(Edge(
                            source=source,
                            target=target,
                            type=rel_type,
                            properties={}
                        ))
                    else:
                        logger.warning(f"Skipping relationship with missing data: source={source}, target={target}, type={rel_type}")
                else:
                    logger.warning(f"Skipping relationship with insufficient data: {row}")
            except Exception as e:
                logger.error(f"Error processing relationship data: {e}")
                logger.error(f"Row data: {row}")
                continue

    return GraphData(nodes=nodes, relationships=relationships)

async def execute_graph_query(query: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Execute a Cypher query.

    Args:
        query: Cypher query
        params: Query parameters

    Returns:
        Query results
    """
    adapter = await get_falkordb_adapter()

    try:
        result = adapter.execute_cypher(query, params)

        if not result:
            return {
                "headers": [],
                "data": [],
                "summary": {"query": query, "params": params}
            }

        headers = result[0]
        data = result[1] if len(result) > 1 else []

        return {
            "headers": headers,
            "data": data,
            "summary": {"query": query, "params": params}
        }

    except Exception as e:
        logger.error(f"Error executing graph query: {e}")
        return {
            "headers": [],
            "data": [],
            "error": str(e),
            "summary": {"query": query, "params": params}
        }

async def get_taxonomy() -> Taxonomy:
    """
    Get the taxonomy of entities.

    Returns:
        Taxonomy
    """
    adapter = await get_falkordb_adapter()

    # Get root nodes (nodes without IS_A relationships)
    root_query = """
    MATCH (n:Entity)
    WHERE NOT (n)-[:IS_A]->(:Entity)
    RETURN n
    """

    root_result = adapter.execute_cypher(root_query)

    root_nodes = []
    if root_result and len(root_result) > 1:
        for row in root_result[1]:
            try:
                node_data = row[0]

                # Check if node_data is a dictionary
                if isinstance(node_data, dict):
                    root_node = TaxonomyNode(
                        uuid=node_data.get("uuid", ""),
                        name=node_data.get("name", ""),
                        type=node_data.get("type", ""),
                        children=[]
                    )
                elif isinstance(node_data, list):
                    # If it's a list, try to convert it to a dictionary
                    node_dict = {}
                    # Assuming the list contains key-value pairs
                    for i in range(0, len(node_data), 2):
                        if i + 1 < len(node_data):
                            # Make sure the key is hashable (convert to string if needed)
                            key = node_data[i]
                            if not isinstance(key, str):
                                key = str(key)
                            node_dict[key] = node_data[i + 1]

                    root_node = TaxonomyNode(
                        uuid=node_dict.get("uuid", ""),
                        name=node_dict.get("name", ""),
                        type=node_dict.get("type", ""),
                        children=[]
                    )
                else:
                    # Skip nodes with invalid data
                    logger.warning(f"Skipping taxonomy node with invalid data type: {type(node_data)}")
                    continue

                # Only process nodes with valid UUIDs
                if root_node.uuid:
                    # Get children recursively
                    await get_taxonomy_children(root_node)
                    root_nodes.append(root_node)
                else:
                    logger.warning("Skipping taxonomy node with missing UUID")
            except Exception as e:
                logger.error(f"Error processing taxonomy node: {e}")
                # Skip this node
                continue

    return Taxonomy(root_nodes=root_nodes)

async def get_taxonomy_children(node: TaxonomyNode) -> None:
    """
    Get children of a taxonomy node recursively.

    Args:
        node: Taxonomy node
    """
    adapter = await get_falkordb_adapter()

    # Get children
    children_query = f"""
    MATCH (n:Entity)<-[:IS_A]-(child:Entity)
    WHERE n.uuid = '{node.uuid}'
    RETURN child
    """

    children_result = adapter.execute_cypher(children_query)

    if children_result and len(children_result) > 1:
        for row in children_result[1]:
            try:
                child_data = row[0]

                # Check if child_data is a dictionary
                if isinstance(child_data, dict):
                    child_node = TaxonomyNode(
                        uuid=child_data.get("uuid", ""),
                        name=child_data.get("name", ""),
                        type=child_data.get("type", ""),
                        children=[]
                    )
                elif isinstance(child_data, list):
                    # If it's a list, try to convert it to a dictionary
                    child_dict = {}
                    # Assuming the list contains key-value pairs
                    for i in range(0, len(child_data), 2):
                        if i + 1 < len(child_data):
                            # Make sure the key is hashable (convert to string if needed)
                            key = child_data[i]
                            if not isinstance(key, str):
                                key = str(key)
                            child_dict[key] = child_data[i + 1]

                    child_node = TaxonomyNode(
                        uuid=child_dict.get("uuid", ""),
                        name=child_dict.get("name", ""),
                        type=child_dict.get("type", ""),
                        children=[]
                    )
                else:
                    # Skip nodes with invalid data
                    logger.warning(f"Skipping taxonomy child with invalid data type: {type(child_data)}")
                    continue

                # Only process nodes with valid UUIDs
                if child_node.uuid:
                    # Get children recursively
                    await get_taxonomy_children(child_node)
                    node.children.append(child_node)
                else:
                    logger.warning("Skipping taxonomy child with missing UUID")
            except Exception as e:
                logger.error(f"Error processing taxonomy child: {e}")
                # Skip this child
                continue

async def create_entity_relationship(
    source_uuid: str,
    target_uuid: str,
    relationship_type: str,
    properties: Dict[str, Any] = None
) -> bool:
    """
    Create a relationship between two entities.

    Args:
        source_uuid: Source entity UUID
        target_uuid: Target entity UUID
        relationship_type: Relationship type
        properties: Relationship properties

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create relationship
        result = await create_relationship(source_uuid, target_uuid, relationship_type, properties)
        return result

    except Exception as e:
        logger.error(f"Error creating entity relationship: {e}")
        return False
