#!/usr/bin/env python3
"""
Debug the antioxidants and cancer Q&A response.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.qa_service import get_relevant_facts

async def debug_antioxidants_cancer():
    """Debug the antioxidants and cancer question."""
    try:
        question = "antioxidants and cancer"
        
        print(f"🔍 DEBUGGING: '{question}'")
        print("=" * 50)
        
        # Get relevant facts
        facts = await get_relevant_facts(question, limit=10)
        
        print(f"📊 Found {len(facts)} relevant facts:")
        
        for i, fact in enumerate(facts):
            body = fact.get('body', '')[:200]
            doc_name = fact.get('document_name', 'Unknown')
            doc_id = fact.get('document_id', 'Unknown')
            
            print(f"\n[{i+1}] Document: {doc_name}")
            print(f"    Doc ID: {doc_id}")
            print(f"    Content: {body}...")
            
            # Check if this fact contains repetitive content
            if 'NADPH Quinone Reductase' in body:
                print(f"    ⚠️ Contains NADPH content")
            if 'oxidative stress' in body.lower():
                print(f"    ✅ Contains oxidative stress content")
            if 'cancer' in body.lower():
                print(f"    ✅ Contains cancer content")
            if 'antioxidant' in body.lower():
                print(f"    ✅ Contains antioxidant content")
        
        # Check for duplicate facts
        print(f"\n🔍 CHECKING FOR DUPLICATES:")
        unique_bodies = set()
        duplicates = 0
        
        for fact in facts:
            body_start = fact.get('body', '')[:100]
            if body_start in unique_bodies:
                duplicates += 1
                print(f"   ⚠️ Duplicate found: {body_start[:50]}...")
            else:
                unique_bodies.add(body_start)
        
        print(f"   Found {duplicates} duplicate facts out of {len(facts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(debug_antioxidants_cancer())
