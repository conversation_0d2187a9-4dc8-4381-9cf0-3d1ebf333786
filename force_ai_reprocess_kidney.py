#!/usr/bin/env python3
"""
Force reprocess the kidney document with the new AI-powered Intelligent Reference Extractor.
"""

import asyncio
import os
from pathlib import Path
from services.intelligent_reference_extractor import get_intelligent_reference_extractor
from utils.mistral_ocr import MistralOCRProcessor
from utils.logging_utils import get_logger
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

async def force_ai_reprocess_kidney():
    """Force reprocess kidney document with AI-powered extraction."""
    print("🤖 FORCE AI REPROCESSING: Kidney Document")
    print("=" * 70)
    
    # Find the kidney document
    document_path = Path("uploads/5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal <PERSON>.pdf")
    
    if not document_path.exists():
        print(f"❌ Document not found: {document_path}")
        return False
    
    print(f"📄 Found document: {document_path.name}")
    print(f"📊 File size: {document_path.stat().st_size:,} bytes")
    
    # Check API keys
    openrouter_key = os.getenv('OPEN_ROUTER_API_KEY') or os.getenv('OPENROUTER_API_KEY')
    print(f"🔑 OpenRouter API: {'✅ Available' if openrouter_key else '❌ Missing'}")
    
    try:
        # Step 1: Extract text using OCR
        print(f"\n🔍 Step 1: Extracting text from PDF with OCR...")
        ocr_processor = MistralOCRProcessor()
        
        ocr_result = await ocr_processor.process_pdf(str(document_path))
        
        if not ocr_result.get('success', False):
            print(f"❌ OCR extraction failed: {ocr_result.get('error', 'Unknown error')}")
            return False
        
        extracted_text = ocr_result.get('text', '')
        print(f"✅ Text extracted: {len(extracted_text):,} characters")
        
        if len(extracted_text) < 10000:
            print(f"⚠️ Warning: Short text extracted, may indicate OCR issues")
        
        # Step 2: Apply AI-Powered Intelligent Reference Extractor
        print(f"\n🤖 Step 2: Applying AI-Powered Intelligent Reference Extractor...")
        extractor = get_intelligent_reference_extractor()
        
        if not extractor.ai_available:
            print(f"❌ AI not available despite API keys")
            return False
        
        print(f"✅ AI extraction available with meta-llama/llama-4-maverick")
        
        # Force comprehensive extraction with AI
        result = await extractor.extract_references_comprehensive(
            extracted_text, 
            document_path.name
        )
        
        print(f"\n✅ AI-powered extraction completed!")
        print(f"📊 Total references found: {result['total_found']}")
        print(f"🎯 Confidence score: {result['confidence_score']}")
        print(f"🔧 Extraction methods: {result['extraction_methods']}")
        
        # Check AI contribution
        ai_refs = result['extraction_methods'].get('ai_powered', 0)
        pattern_refs = result['extraction_methods'].get('pattern_based', 0)
        
        print(f"\n🤖 AI Contribution Analysis:")
        print(f"   AI-powered: {ai_refs} references")
        print(f"   Pattern-based: {pattern_refs} references")
        print(f"   Section-based: {result['extraction_methods'].get('section_based', 0)} references")
        print(f"   Line analysis: {result['extraction_methods'].get('line_analysis', 0)} references")
        
        # Step 3: Save results to new CSV with AI tag
        csv_filename = f"ai_powered_references_{document_path.stem}.csv"
        csv_path = Path("references") / csv_filename
        
        print(f"\n💾 Step 3: Saving AI-powered results to CSV...")
        
        # Create DataFrame with AI enhancement info
        references_data = []
        for i, ref_data in enumerate(result['references'], 1):
            references_data.append({
                'reference_number': i,
                'text': ref_data['text'],
                'confidence': ref_data['confidence'],
                'extraction_method': 'ai_powered_intelligent_extractor',
                'ai_enhanced': True,
                'source_section': 'document_body'
            })
        
        df = pd.DataFrame(references_data)
        
        # Ensure references directory exists
        csv_path.parent.mkdir(exist_ok=True)
        
        # Save to CSV
        df.to_csv(csv_path, index=False)
        print(f"✅ AI-powered references saved to: {csv_path}")
        
        # Step 4: Display sample AI-found references
        print(f"\n📋 Sample AI-powered references:")
        for i, ref_data in enumerate(result['references'][:15], 1):
            ref_text = ref_data['text']
            print(f"{i:2d}. {ref_text[:120]}{'...' if len(ref_text) > 120 else ''}")
        
        if result['total_found'] > 15:
            print(f"... and {result['total_found'] - 15} more references")
        
        # Step 5: Performance comparison
        print(f"\n🎯 Performance Comparison:")
        print(f"   Original system: 6 references")
        print(f"   Improved system: 47 references")
        print(f"   AI-powered system: {result['total_found']} references")
        
        improvement_vs_original = result['total_found'] / 6
        improvement_vs_improved = result['total_found'] / 47
        
        print(f"\n📈 Improvements:")
        print(f"   vs Original: {improvement_vs_original:.1f}x better")
        print(f"   vs Improved: {improvement_vs_improved:.1f}x better")
        
        if ai_refs > 0:
            print(f"   AI Contribution: +{ai_refs} additional references")
        
        # Step 6: Final assessment
        if result['total_found'] >= 100:
            print(f"\n🎉 OUTSTANDING: {result['total_found']} references with AI!")
            print(f"   This is a revolutionary improvement!")
        elif result['total_found'] >= 75:
            print(f"\n🎉 EXCELLENT: {result['total_found']} references with AI!")
            print(f"   AI has significantly enhanced reference detection!")
        elif result['total_found'] >= 50:
            print(f"\n✅ VERY GOOD: {result['total_found']} references with AI!")
            print(f"   AI is providing meaningful improvements!")
        else:
            print(f"\n✅ GOOD: {result['total_found']} references with AI!")
            print(f"   AI is working but may need optimization")
        
        # Step 7: Check for expected references
        print(f"\n🔍 Checking for expected key references...")
        expected_authors = [
            "Australian Institute of Health and Welfare",
            "Liyanage T",
            "Zoccali, C.",
            "Hill, N. R.",
            "Miwa, K.",
            "Bowe B",
            "Mahalingasivam V",
            "Huang C",
            "Hsu CM",
            "Gur E"
        ]
        
        found_authors = []
        all_ref_text = ' '.join([ref['text'] for ref in result['references']])
        
        for author in expected_authors:
            if author in all_ref_text:
                found_authors.append(author)
        
        print(f"📊 Expected authors found: {len(found_authors)}/{len(expected_authors)}")
        for author in found_authors:
            print(f"  ✅ {author}")
        
        missing_authors = [author for author in expected_authors if author not in found_authors]
        if missing_authors:
            print(f"\n❌ Still missing authors:")
            for author in missing_authors:
                print(f"  ❌ {author}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI reprocessing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function."""
    print("🚀 FORCE AI REPROCESSING OF KIDNEY DOCUMENT")
    print("This will use the full AI-powered Intelligent Reference Extractor")
    print("=" * 70)
    
    success = await force_ai_reprocess_kidney()
    
    if success:
        print(f"\n🎯 AI REPROCESSING SUCCESSFUL!")
        print(f"The kidney document has been reprocessed with full AI enhancement.")
        print(f"Check the new CSV file for the complete AI-powered reference list.")
        print(f"This should now find significantly more references than before!")
    else:
        print(f"\n❌ AI REPROCESSING FAILED!")
        print(f"Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
