#!/usr/bin/env python3
"""
Fix entity extraction for the two newer documents that are missing entities.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter
from services.entity_extraction_service import extract_entities_from_text
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def fix_missing_entities():
    """Run entity extraction on documents that have facts but no entities."""
    
    try:
        adapter = GraphitiFalkorDBAdapter('graphiti')
        
        # Get documents that have facts but no entities
        query = '''
        MATCH (ep:Episode)-[:CONTAINS]->(f:Fact)
        WHERE NOT (f)-[:MENTIONS]->(:Entity)
        RETURN ep.uuid as episode_uuid, ep.name as episode_name, 
               f.uuid as fact_uuid, f.body as fact_text
        ORDER BY ep.processed_at DESC
        '''
        
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or len(result[1]) == 0:
            logger.info("No facts without entity mentions found")
            return
        
        logger.info(f"Found {len(result[1])} facts without entity mentions")
        
        # Group facts by episode
        episodes = {}
        for row in result[1]:
            episode_uuid = row[0]
            episode_name = row[1]
            fact_uuid = row[2]
            fact_text = row[3]
            
            if episode_uuid not in episodes:
                episodes[episode_uuid] = {
                    'name': episode_name,
                    'facts': []
                }
            
            episodes[episode_uuid]['facts'].append({
                'uuid': fact_uuid,
                'text': fact_text
            })
        
        total_entities_extracted = 0
        
        for episode_uuid, episode_data in episodes.items():
            logger.info(f"\n🔄 Processing document: {episode_data['name']}")
            logger.info(f"   Episode UUID: {episode_uuid}")
            logger.info(f"   Facts to process: {len(episode_data['facts'])}")
            
            episode_entities = 0
            
            for i, fact in enumerate(episode_data['facts']):
                fact_uuid = fact['uuid']
                fact_text = fact['text']
                
                logger.info(f"   Processing fact {i+1}/{len(episode_data['facts'])}: {fact_uuid}")
                logger.info(f"   Text sample: {fact_text[:100]}...")
                
                try:
                    # Extract entities with proper fact_id
                    entities = await extract_entities_from_text(
                        text=fact_text,
                        document_id=episode_uuid,
                        fact_id=fact_uuid,
                        llm_provider='openrouter'
                    )
                    
                    logger.info(f"   ✅ Extracted {len(entities)} entities from fact {fact_uuid}")
                    episode_entities += len(entities)
                    total_entities_extracted += len(entities)
                    
                    for entity in entities:
                        logger.info(f"     - {entity.get('name', 'Unknown')} ({entity.get('type', 'Unknown')})")
                
                except Exception as e:
                    logger.error(f"   ❌ Error extracting entities from fact {fact_uuid}: {e}")
                    continue
            
            logger.info(f"✅ Document '{episode_data['name']}' completed: {episode_entities} entities extracted")
        
        logger.info(f"\n🎉 TOTAL ENTITIES EXTRACTED: {total_entities_extracted}")
        
        # Verify the results
        print("\n=== VERIFICATION ===")
        
        # Check total entity count
        count_query = 'MATCH (e:Entity) RETURN count(e) as count'
        count_result = adapter.execute_cypher(count_query)
        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            entity_count = count_result[1][0][0]
            logger.info(f"✅ Total entities in database: {entity_count}")
        
        # Check mentions relationships
        mentions_query = 'MATCH ()-[r:MENTIONS]->() RETURN count(r) as count'
        mentions_result = adapter.execute_cypher(mentions_query)
        if mentions_result and len(mentions_result) > 1 and len(mentions_result[1]) > 0:
            mentions_count = mentions_result[1][0][0]
            logger.info(f"✅ Total MENTIONS relationships: {mentions_count}")
        
        # Check facts without entities (should be 0 now)
        remaining_query = '''
        MATCH (f:Fact)
        WHERE NOT (f)-[:MENTIONS]->(:Entity)
        RETURN count(f) as count
        '''
        remaining_result = adapter.execute_cypher(remaining_query)
        if remaining_result and len(remaining_result) > 1 and len(remaining_result[1]) > 0:
            remaining_count = remaining_result[1][0][0]
            logger.info(f"✅ Facts still without entities: {remaining_count}")
        
        # Show per-document entity counts
        print("\n=== PER-DOCUMENT ENTITY COUNTS ===")
        doc_query = '''
        MATCH (ep:Episode)
        OPTIONAL MATCH (ep)-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(e:Entity)
        RETURN ep.name as doc_name, count(DISTINCT e) as entity_count
        ORDER BY ep.processed_at DESC
        '''
        doc_result = adapter.execute_cypher(doc_query)
        if doc_result and len(doc_result) > 1:
            headers = doc_result[0]
            for row in doc_result[1]:
                doc_data = dict(zip(headers, row))
                logger.info(f"📄 {doc_data['doc_name']}: {doc_data['entity_count']} entities")
        
    except Exception as e:
        logger.error(f"Error in entity extraction fix: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(fix_missing_entities())
