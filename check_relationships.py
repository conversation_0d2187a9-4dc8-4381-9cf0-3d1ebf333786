#!/usr/bin/env python3
"""
Check relationships and entities in the FalkorDB knowledge graph.
"""

import falkordb

def main():
    try:
        # Connect to FalkorDB
        client = falkordb.FalkorDB(host='localhost', port=6379)
        graph = client.select_graph('graphiti_knowledge')

        # Check relationship types
        result = graph.query('MATCH ()-[r]->() RETURN type(r) AS relationship_type, count(r) AS count ORDER BY count DESC LIMIT 10')
        print('RELATIONSHIP TYPES IN KNOWLEDGE GRAPH:')
        print('=' * 50)
        for record in result.result_set:
            print(f'  {record[0]}: {record[1]} relationships')

        # Check entity types
        result = graph.query('MATCH (e:Entity) RETURN e.type AS entity_type, count(e) AS count ORDER BY count DESC LIMIT 10')
        print('\nTOP ENTITY TYPES:')
        print('=' * 30)
        for record in result.result_set:
            print(f'  {record[0]}: {record[1]} entities')

        # Sample entities
        result = graph.query('MATCH (e:Entity) RETURN e.name, e.type LIMIT 10')
        print('\nSAMPLE ENTITIES:')
        print('=' * 25)
        for record in result.result_set:
            print(f'  {record[0]} ({record[1]})')
            
        # Total counts
        result = graph.query('MATCH (n) RETURN labels(n)[0] AS node_type, count(n) AS count ORDER BY count DESC')
        print('\nNODE COUNTS BY TYPE:')
        print('=' * 30)
        for record in result.result_set:
            print(f'  {record[0]}: {record[1]} nodes')

    except Exception as e:
        print(f'Error: {e}')

if __name__ == "__main__":
    main()
