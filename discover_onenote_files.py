#!/usr/bin/env python3
"""
OneNote File Discovery Script

This script searches for OneNote (.one) files on the computer and creates
a comprehensive list for processing through the Graphiti document pipeline.
"""

import os
import sys
from pathlib import Path
import json
import csv
from datetime import datetime
from typing import List, Dict, Any

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def get_file_info(file_path: Path) -> Dict[str, Any]:
    """Get detailed information about a OneNote file."""
    try:
        stat = file_path.stat()
        return {
            'path': str(file_path),
            'name': file_path.name,
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'parent_folder': str(file_path.parent),
            'drive': str(file_path.parts[0]) if file_path.parts else '',
            'accessible': True
        }
    except (OSError, PermissionError) as e:
        return {
            'path': str(file_path),
            'name': file_path.name,
            'size': 0,
            'size_formatted': 'Unknown',
            'created': 'Unknown',
            'modified': 'Unknown',
            'parent_folder': str(file_path.parent),
            'drive': str(file_path.parts[0]) if file_path.parts else '',
            'accessible': False,
            'error': str(e)
        }

def search_onenote_files(search_paths: List[str] = None) -> List[Dict[str, Any]]:
    """Search for OneNote files in specified paths or common locations."""
    
    if search_paths is None:
        # Default search paths for Windows
        search_paths = [
            os.path.expanduser("~"),  # User home directory
            "C:\\Users",  # All user directories
        ]
        
        # Add OneDrive paths if they exist
        onedrive_paths = [
            os.path.expanduser("~/OneDrive"),
            os.path.expanduser("~/OneDrive - Personal"),
            os.path.expanduser("~/OneDrive for Business"),
            "C:\\Users\\<USER>\\Documents",
        ]
        
        for path in onedrive_paths:
            if os.path.exists(path):
                search_paths.append(path)
        
        # Add additional drives if they exist
        for drive_letter in ['D', 'E', 'F']:
            drive_path = f"{drive_letter}:\\"
            if os.path.exists(drive_path):
                search_paths.append(drive_path)
    
    onenote_files = []
    processed_paths = set()
    
    print("🔍 Searching for OneNote (.one) files...")
    print(f"Search paths: {len(search_paths)} locations")
    
    for search_path in search_paths:
        if not os.path.exists(search_path):
            print(f"⚠️  Path does not exist: {search_path}")
            continue
            
        if search_path in processed_paths:
            continue
            
        processed_paths.add(search_path)
        print(f"📂 Searching in: {search_path}")
        
        try:
            # Use Path.rglob for recursive search
            path_obj = Path(search_path)
            
            # Search for .one files recursively
            for one_file in path_obj.rglob("*.one"):
                try:
                    file_info = get_file_info(one_file)
                    onenote_files.append(file_info)
                    print(f"   ✓ Found: {one_file.name} ({file_info['size_formatted']})")
                except Exception as e:
                    print(f"   ❌ Error processing {one_file}: {e}")
                    
        except PermissionError:
            print(f"   ⚠️  Permission denied: {search_path}")
        except Exception as e:
            print(f"   ❌ Error searching {search_path}: {e}")
    
    return onenote_files

def save_results(onenote_files: List[Dict[str, Any]], output_dir: str = "."):
    """Save the results in multiple formats."""
    
    if not onenote_files:
        print("No OneNote files found.")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save as JSON
    json_file = os.path.join(output_dir, f"onenote_files_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(onenote_files, f, indent=2, ensure_ascii=False)
    print(f"📄 Saved JSON report: {json_file}")
    
    # Save as CSV
    csv_file = os.path.join(output_dir, f"onenote_files_{timestamp}.csv")
    if onenote_files:
        fieldnames = ['name', 'path', 'size', 'size_formatted', 'created', 'modified', 'parent_folder', 'drive', 'accessible']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for file_info in onenote_files:
                # Only write the specified fields
                row = {field: file_info.get(field, '') for field in fieldnames}
                writer.writerow(row)
    
    print(f"📊 Saved CSV report: {csv_file}")
    
    # Save processing list (simple text file with paths)
    processing_file = os.path.join(output_dir, f"onenote_processing_list_{timestamp}.txt")
    with open(processing_file, 'w', encoding='utf-8') as f:
        f.write("# OneNote Files for Processing\n")
        f.write("# Copy and paste the paths you want to process\n")
        f.write("# One file path per line\n\n")
        
        for file_info in sorted(onenote_files, key=lambda x: x.get('name', '').lower()):
            if file_info.get('accessible', False):
                f.write(f"{file_info['path']}\n")
    
    print(f"📋 Saved processing list: {processing_file}")

def print_summary(onenote_files: List[Dict[str, Any]]):
    """Print a summary of found OneNote files."""
    
    if not onenote_files:
        print("\n❌ No OneNote files found on this computer.")
        print("\nPossible reasons:")
        print("- OneNote files may be stored in cloud locations")
        print("- Files may be in restricted directories")
        print("- OneNote may be using a different storage format")
        return
    
    print(f"\n✅ Found {len(onenote_files)} OneNote files!")
    
    # Calculate total size
    total_size = sum(f.get('size', 0) for f in onenote_files)
    accessible_files = sum(1 for f in onenote_files if f.get('accessible', False))
    
    print(f"📊 Summary:")
    print(f"   Total files: {len(onenote_files)}")
    print(f"   Accessible files: {accessible_files}")
    print(f"   Total size: {format_file_size(total_size)}")
    
    # Group by location
    by_location = {}
    for file_info in onenote_files:
        parent = file_info.get('parent_folder', 'Unknown')
        if parent not in by_location:
            by_location[parent] = []
        by_location[parent].append(file_info)
    
    print(f"\n📁 Files by location:")
    for location, files in sorted(by_location.items()):
        print(f"   {location}: {len(files)} files")
    
    # Show largest files
    largest_files = sorted(onenote_files, key=lambda x: x.get('size', 0), reverse=True)[:5]
    print(f"\n📈 Largest files:")
    for i, file_info in enumerate(largest_files, 1):
        print(f"   {i}. {file_info['name']} ({file_info['size_formatted']})")
    
    # Show most recent files
    try:
        recent_files = sorted(
            [f for f in onenote_files if f.get('modified') != 'Unknown'], 
            key=lambda x: x.get('modified', ''), 
            reverse=True
        )[:5]
        
        if recent_files:
            print(f"\n🕒 Most recently modified:")
            for i, file_info in enumerate(recent_files, 1):
                mod_date = file_info.get('modified', 'Unknown')
                if mod_date != 'Unknown':
                    mod_date = datetime.fromisoformat(mod_date).strftime('%Y-%m-%d %H:%M')
                print(f"   {i}. {file_info['name']} ({mod_date})")
    except Exception as e:
        print(f"   Error sorting by date: {e}")

def main():
    """Main function to discover OneNote files."""
    
    print("🚀 OneNote File Discovery Tool")
    print("=" * 50)
    
    # Allow custom search paths via command line
    search_paths = None
    if len(sys.argv) > 1:
        search_paths = sys.argv[1:]
        print(f"Using custom search paths: {search_paths}")
    
    # Search for OneNote files
    onenote_files = search_onenote_files(search_paths)
    
    # Print summary
    print_summary(onenote_files)
    
    # Save results
    if onenote_files:
        save_results(onenote_files)
        
        print(f"\n📋 Next Steps:")
        print("1. Review the generated reports")
        print("2. Edit the processing list file to select which files to process")
        print("3. Use the selected file paths for batch processing in Graphiti")
        print("4. Consider organizing files by notebook/topic for better results")
    
    print(f"\n✨ Discovery complete!")

if __name__ == "__main__":
    main()
