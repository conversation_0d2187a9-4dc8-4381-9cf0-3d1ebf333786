#!/usr/bin/env python3
"""
Duplicate Detection API Routes

This module provides REST API endpoints for document duplicate detection functionality.
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import tempfile
import os
from pathlib import Path

from pydantic import BaseModel

from services.document_duplicate_detector import (
    get_document_duplicate_detector, 
    DuplicateDetectionResult,
    DuplicateMatch
)
from utils.logging_utils import get_logger
from utils.file_utils import save_uploaded_file
from utils.config import get_config

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/duplicates", tags=["duplicate-detection"])

class DuplicateCheckRequest(BaseModel):
    """Request model for duplicate checking."""
    file_path: str
    content_hash: Optional[str] = None
    extracted_text: Optional[str] = None

class DuplicateCheckResponse(BaseModel):
    """Response model for duplicate checking."""
    is_duplicate: bool
    matches: list
    highest_similarity: float
    recommendation: str
    total_matches: int
    message: str

@router.post("/check-file", response_model=DuplicateCheckResponse)
async def check_file_for_duplicates(
    file: UploadFile = File(...),
    extract_text: bool = Form(False)
):
    """
    Check if an uploaded file is a duplicate of existing documents.
    
    Args:
        file: The uploaded file to check
        extract_text: Whether to extract text for content comparison
        
    Returns:
        DuplicateCheckResponse with duplicate detection results
    """
    temp_file_path = None
    
    try:
        logger.info(f"Checking file for duplicates: {file.filename}")
        
        # Save file temporarily for analysis
        config = get_config()
        file_content = await file.read()
        temp_file_path = save_uploaded_file(
            file_content,
            file.filename,
            config['paths']['uploads_dir']
        )
        
        # Get duplicate detector
        detector = await get_document_duplicate_detector()
        
        # Extract text if requested (simplified - in real implementation, use proper text extraction)
        extracted_text = None
        if extract_text:
            try:
                # For text files, read directly
                if file.filename.lower().endswith(('.txt', '.md')):
                    extracted_text = file_content.decode('utf-8', errors='ignore')
                # For other files, you would use appropriate text extraction libraries
            except Exception as e:
                logger.warning(f"Could not extract text from {file.filename}: {e}")
        
        # Check for duplicates
        result = await detector.check_for_duplicates(
            file_path=temp_file_path,
            extracted_text=extracted_text
        )
        
        # Convert result to response format
        matches_data = []
        for match in result.matches:
            matches_data.append({
                "document_id": match.document_id,
                "filename": match.filename,
                "file_path": match.file_path,
                "similarity_score": match.similarity_score,
                "match_type": match.match_type,
                "processed_at": match.processed_at,
                "file_size": match.file_size,
                "document_hash": match.document_hash,
                "confidence": match.confidence
            })
        
        message = "No duplicates found."
        if result.is_duplicate:
            if result.recommendation == 'skip':
                message = f"Exact duplicate found! Similarity: {result.highest_similarity:.1%}"
            elif result.recommendation == 'review':
                message = f"High similarity detected ({result.highest_similarity:.1%}). Review recommended."
            else:
                message = f"Similar documents found ({result.highest_similarity:.1%}), but safe to process."
        
        response = DuplicateCheckResponse(
            is_duplicate=result.is_duplicate,
            matches=matches_data,
            highest_similarity=result.highest_similarity,
            recommendation=result.recommendation,
            total_matches=result.total_matches,
            message=message
        )
        
        logger.info(f"Duplicate check completed for {file.filename}: {len(matches_data)} matches found")
        return response
        
    except Exception as e:
        logger.error(f"Error checking file for duplicates: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error checking for duplicates: {str(e)}")
    
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except Exception as e:
                logger.warning(f"Could not remove temporary file {temp_file_path}: {e}")

@router.post("/check-path")
async def check_path_for_duplicates(request: DuplicateCheckRequest):
    """
    Check if a file at a specific path is a duplicate.
    
    Args:
        request: DuplicateCheckRequest with file path and optional content
        
    Returns:
        DuplicateCheckResponse with duplicate detection results
    """
    try:
        logger.info(f"Checking path for duplicates: {request.file_path}")
        
        # Verify file exists
        if not os.path.exists(request.file_path):
            raise HTTPException(status_code=404, detail="File not found")
        
        # Get duplicate detector
        detector = await get_document_duplicate_detector()
        
        # Check for duplicates
        result = await detector.check_for_duplicates(
            file_path=request.file_path,
            content_hash=request.content_hash,
            extracted_text=request.extracted_text
        )
        
        # Convert result to response format
        matches_data = []
        for match in result.matches:
            matches_data.append({
                "document_id": match.document_id,
                "filename": match.filename,
                "file_path": match.file_path,
                "similarity_score": match.similarity_score,
                "match_type": match.match_type,
                "processed_at": match.processed_at,
                "file_size": match.file_size,
                "document_hash": match.document_hash,
                "confidence": match.confidence
            })
        
        message = "No duplicates found."
        if result.is_duplicate:
            if result.recommendation == 'skip':
                message = f"Exact duplicate found! Similarity: {result.highest_similarity:.1%}"
            elif result.recommendation == 'review':
                message = f"High similarity detected ({result.highest_similarity:.1%}). Review recommended."
            else:
                message = f"Similar documents found ({result.highest_similarity:.1%}), but safe to process."
        
        response = DuplicateCheckResponse(
            is_duplicate=result.is_duplicate,
            matches=matches_data,
            highest_similarity=result.highest_similarity,
            recommendation=result.recommendation,
            total_matches=result.total_matches,
            message=message
        )
        
        logger.info(f"Duplicate check completed for {request.file_path}: {len(matches_data)} matches found")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking path for duplicates: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error checking for duplicates: {str(e)}")

@router.get("/document/{document_id}")
async def get_duplicate_document_details(document_id: str):
    """
    Get detailed information about a document that was identified as a potential duplicate.
    
    Args:
        document_id: UUID of the document
        
    Returns:
        Document details including processing statistics
    """
    try:
        logger.info(f"Getting document details for duplicate: {document_id}")
        
        # Get duplicate detector
        detector = await get_document_duplicate_detector()
        
        # Get document details
        document = await detector.get_document_by_id(document_id)
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return JSONResponse(content=document)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document details: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting document details: {str(e)}")

@router.get("/stats")
async def get_duplicate_detection_stats():
    """
    Get statistics about duplicate detection system.
    
    Returns:
        Statistics about processed documents and duplicate detection
    """
    try:
        logger.info("Getting duplicate detection statistics")
        
        # Get duplicate detector
        detector = await get_document_duplicate_detector()
        
        # Get all processed documents
        processed_docs = await detector._get_processed_documents()
        
        # Calculate statistics
        total_documents = len(processed_docs)
        documents_with_hash = sum(1 for doc in processed_docs if doc.get('document_hash'))
        
        # Group by file extensions
        extensions = {}
        for doc in processed_docs:
            filename = doc.get('filename', '')
            if filename:
                ext = Path(filename).suffix.lower()
                extensions[ext] = extensions.get(ext, 0) + 1
        
        stats = {
            "total_documents": total_documents,
            "documents_with_hash": documents_with_hash,
            "hash_coverage": documents_with_hash / total_documents if total_documents > 0 else 0,
            "file_extensions": extensions,
            "duplicate_detection_enabled": True
        }
        
        return JSONResponse(content=stats)
        
    except Exception as e:
        logger.error(f"Error getting duplicate detection stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting statistics: {str(e)}")

@router.post("/decision")
async def record_duplicate_decision(
    decision: str = Form(...),  # 'skip', 'process', 'cancel'
    file_path: str = Form(...),
    document_matches: str = Form(None)  # JSON string of matches
):
    """
    Record a user's decision about how to handle a duplicate document.
    
    Args:
        decision: User's decision ('skip', 'process', 'cancel')
        file_path: Path to the file in question
        document_matches: JSON string of duplicate matches (optional)
        
    Returns:
        Confirmation of decision recording
    """
    try:
        logger.info(f"Recording duplicate decision: {decision} for {file_path}")
        
        # In a more advanced implementation, you might want to:
        # 1. Store the decision in a database for analytics
        # 2. Update document metadata with duplicate information
        # 3. Track user behavior for improving duplicate detection
        
        # For now, just log the decision
        logger.info(f"User decided to '{decision}' for file: {file_path}")
        
        return JSONResponse(content={
            "success": True,
            "decision": decision,
            "file_path": file_path,
            "message": f"Decision '{decision}' recorded successfully"
        })
        
    except Exception as e:
        logger.error(f"Error recording duplicate decision: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error recording decision: {str(e)}")
