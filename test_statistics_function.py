#!/usr/bin/env python3
"""
Test the statistics function directly.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_statistics_function():
    """Test the statistics function."""
    try:
        from routes.entity_routes import get_graph_statistics
        
        print("🔄 Testing get_graph_statistics function...")
        stats = await get_graph_statistics()
        
        print("✅ Statistics function result:")
        for key, value in stats.items():
            if key == "entity_counts_by_type":
                print(f"   {key}: {type(value)} with {len(getattr(value, 'counts', []))} types")
            else:
                print(f"   {key}: {value}")
                
        return stats
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_statistics_function())
    if result:
        print(f"\n🎯 Key Statistics:")
        print(f"   Documents: {result.get('total_documents', 0)}")
        print(f"   Entities: {result.get('total_entities', 0)}")
        print(f"   Chunks: {result.get('total_chunks', 0)}")
        print(f"   Relationships: {result.get('total_relationships', 0)}")
