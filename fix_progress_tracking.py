#!/usr/bin/env python3
"""
Fix the progress tracking issues by testing the current system.
"""

import asyncio
import sys
from pathlib import Path

async def test_progress_system():
    """Test the current progress tracking system."""
    print("🔧 Testing Progress Tracking System...")
    
    try:
        # Test WebSocket manager import
        from utils.websocket_manager import get_websocket_manager
        websocket_manager = get_websocket_manager()
        print("✅ WebSocket manager imported successfully")
        
        # Test EnhancedProgressTracker import
        from utils.enhanced_progress_tracker import EnhancedProgressTracker
        print("✅ EnhancedProgressTracker imported successfully")
        
        # Test creating a tracker
        tracker = EnhancedProgressTracker(
            operation_id="test-123",
            filename="test.pdf",
            websocket_manager=websocket_manager
        )
        print("✅ EnhancedProgressTracker created successfully")
        
        # Test getting progress data
        progress_data = tracker.get_progress_data()
        print(f"✅ Progress data: {progress_data['overall_progress']}%")
        
        # Test WebSocket manager methods
        print(f"✅ Active operations: {websocket_manager.get_active_operations()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_unified_pipeline():
    """Test the unified pipeline integration."""
    print("\n🔧 Testing Unified Pipeline Integration...")
    
    try:
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print("✅ Unified pipeline imported successfully")
        
        # Check if the pipeline accepts progress_tracker parameter
        import inspect
        sig = inspect.signature(pipeline.process_document)
        params = list(sig.parameters.keys())
        
        if 'progress_tracker' in params:
            print("✅ Unified pipeline accepts progress_tracker parameter")
        else:
            print("❌ Unified pipeline missing progress_tracker parameter")
            print(f"Available parameters: {params}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_frontend_files():
    """Check if frontend files exist and are properly configured."""
    print("\n🔧 Checking Frontend Files...")
    
    frontend_files = [
        "static/js/enhanced_progress.js",
        "static/js/enhanced_progress_ui.js", 
        "static/js/websocket_upload.js"
    ]
    
    for file_path in frontend_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
    
    # Check if WebSocket URL is correct in frontend
    try:
        with open("static/js/enhanced_progress_ui.js", "r") as f:
            content = f.read()
            if "ws://" in content and "/ws/progress/" in content:
                print("✅ WebSocket URL found in frontend")
            else:
                print("❌ WebSocket URL not found in frontend")
    except Exception as e:
        print(f"❌ Error reading frontend file: {e}")

async def main():
    """Run all tests."""
    print("🚀 Fixing Progress Tracking Issues")
    print("=" * 50)
    
    # Test backend components
    backend_ok = await test_progress_system()
    pipeline_ok = await test_unified_pipeline()
    
    # Check frontend files
    await check_frontend_files()
    
    print("\n" + "=" * 50)
    
    if backend_ok and pipeline_ok:
        print("✅ Backend systems are working correctly")
        print("\n🔧 The issue is likely in the frontend WebSocket connection")
        print("   or the progress tracker not being passed to the pipeline.")
        
        print("\n📋 Recommended fixes:")
        print("1. Ensure WebSocket connections are established in frontend")
        print("2. Check browser console for WebSocket errors")
        print("3. Verify progress tracker is passed to unified pipeline")
        print("4. Check if progress updates are being sent")
        
    else:
        print("❌ Backend issues detected - need to fix these first")

if __name__ == "__main__":
    asyncio.run(main())
