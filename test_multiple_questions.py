#!/usr/bin/env python3
"""
Test multiple Q&A questions to verify the system is working.
"""

import requests
import json

def test_question(question, description):
    """Test a single question."""
    print(f"\n🔍 Testing: {description}")
    print(f"   Question: '{question}'")
    
    try:
        response = requests.post(
            'http://localhost:9753/api/qa/answer',
            json={
                'question': question,
                'max_facts': 5,
                'response_length': 'brief'
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('answer', 'No answer')
            references = data.get('references', [])
            
            # Check if it's a real answer or "not enough information"
            if "don't have enough information" in answer.lower():
                print(f"   ❌ Still getting 'not enough information'")
                return False
            else:
                print(f"   ✅ Got answer ({len(answer)} chars, {len(references)} refs)")
                print(f"   Preview: {answer[:100]}...")
                return True
        else:
            print(f"   ❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    """Test multiple questions."""
    print("🚀 TESTING MULTIPLE Q&A QUESTIONS")
    print("=" * 50)
    
    test_questions = [
        ("cocoa and human health", "Cocoa Health Benefits"),
        ("cancer and antioxidants", "Cancer & Antioxidants"),
        ("ginger health benefits", "Ginger Benefits"),
        ("vitamin C immune system", "Vitamin C & Immunity"),
        ("inflammation and disease", "Inflammation & Disease"),
        ("what are polyphenols", "Polyphenols Information")
    ]
    
    results = []
    for question, description in test_questions:
        result = test_question(question, description)
        results.append((description, result))
    
    # Summary
    print(f"\n📊 RESULTS SUMMARY:")
    print("=" * 30)
    
    working = 0
    for description, result in results:
        status = "✅" if result else "❌"
        print(f"   {status} {description}")
        if result:
            working += 1
    
    print(f"\n🎯 SUCCESS RATE: {working}/{len(results)} ({working/len(results)*100:.1f}%)")
    
    if working == len(results):
        print("\n🎉 ALL Q&A QUESTIONS WORKING!")
        print("   The Q&A system is fully functional!")
    elif working > 0:
        print(f"\n⚠️ PARTIAL SUCCESS: {working} out of {len(results)} working")
    else:
        print("\n❌ NO QUESTIONS WORKING")

if __name__ == "__main__":
    main()
