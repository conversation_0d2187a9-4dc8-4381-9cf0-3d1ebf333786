#!/usr/bin/env python3
"""
Test the exact Q&A query that would be generated.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.database_service import get_falkordb_adapter
from services.qa_service import extract_keywords

async def test_qa_query():
    """Test the exact Q&A query."""
    try:
        adapter = await get_falkordb_adapter()
        
        question = "cocoa and human health"
        keywords = extract_keywords(question)
        
        print(f"🔍 Testing Q&A Query for: '{question}'")
        print(f"📝 Keywords extracted: {keywords}")
        
        # Build the exact query that qa_service would build
        cypher_query = """
        MATCH (c:Chunk)
        WHERE
        """
        
        # Add keyword conditions (case-insensitive)
        keyword_conditions = []
        for keyword in keywords:
            # Escape single quotes in the keyword
            escaped_keyword = keyword.replace("'", "\\'")
            keyword_conditions.append(f"toLower(c.text) CONTAINS toLower('{escaped_keyword}')")

        cypher_query += " OR ".join(keyword_conditions)
        
        # Add the Document match using document_id property
        cypher_query += """
        WITH c
        OPTIONAL MATCH (d:Document)
        WHERE d.id = c.document_id
        RETURN
            c.id as uuid,
            c.text as body,
            c.chunk_index as chunk_num,
            COALESCE(d.id, c.document_id, '') as document_id,
            COALESCE(d.name, '') as document_title,
            COALESCE(d.name, 'Unknown Document') as document_name,
            '' as document_author,
            '' as document_year
        """
        
        cypher_query += "LIMIT 5"
        
        print(f"\n📊 Generated Query:")
        print(cypher_query)
        
        # Execute the query
        result = adapter.execute_cypher(cypher_query)
        
        print(f"\n📊 Query Results:")
        if result and len(result) > 1 and len(result[1]) > 0:
            print(f"   Found {len(result[1])} chunks")
            for i, row in enumerate(result[1][:3]):
                body = row[1][:150] if row[1] else "No text"
                doc_name = row[5] if len(row) > 5 else "Unknown"
                print(f"   {i+1}. {body}...")
                print(f"      From: {doc_name}")
        else:
            print("   No results found")
            print(f"   Raw result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_qa_query())
