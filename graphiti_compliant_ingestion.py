#!/usr/bin/env python3
"""
Graphiti-compliant document ingestion pipeline.
Uses actual Graphiti base classes (EntityNode, EpisodicNode, EntityEdge) for proper compliance.
"""

import asyncio
import sys
import uuid
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import json

# Add graphiti_core to path
sys.path.append(str(Path(__file__).parent / "graphiti_core"))

from graphiti_core.nodes import EntityNode, EpisodicNode, EpisodeType
from graphiti_core.edges import EntityEdge, EpisodicEdge
from graphiti_core.embedder import OpenAIEmbedder
from graphiti_core.llm_client import OpenAIClient
from graphiti_core.helpers import utc_now

# Import existing utilities
from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config
from utils.logging_utils import get_logger
from utils.redis_vector_search import store_embedding

# Import FalkorDB adapter for Neo4j-compatible operations
from database.falkordb_adapter import GraphitiFalkorDBAdapter

logger = get_logger(__name__)

class GraphitiCompliantProcessor:
    """Document processor using actual Graphiti base classes."""
    
    def __init__(self, graph_name: str = "graphiti"):
        self.graph_name = graph_name
        self.falkor_adapter = None
        self.embedder = None
        self.llm_client = None
        self.config = get_config()
        
        # Statistics
        self.stats = {
            'documents_processed': 0,
            'episodes_created': 0,
            'entities_created': 0,
            'facts_created': 0,
            'entity_edges_created': 0,
            'episodic_edges_created': 0,
            'embeddings_stored': 0
        }
    
    async def initialize(self):
        """Initialize all components."""
        try:
            # Initialize database
            self.falkor_adapter = GraphitiFalkorDBAdapter(self.graph_name)
            
            # Initialize embedder (using OpenAI for now, can switch to Ollama)
            openai_api_key = self.config.get('openai_api_key')
            if openai_api_key:
                self.embedder = OpenAIEmbedder(api_key=openai_api_key)
            
            # Initialize LLM client
            self.llm_client = OpenAIClient(api_key=openai_api_key)
            
            logger.info("✅ Graphiti components initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize: {e}")
            raise
    
    def generate_group_id(self, document_path: str) -> str:
        """Generate group_id for graph partitioning."""
        # Use document-based grouping
        doc_name = Path(document_path).stem
        return f"doc_{doc_name}_{datetime.now().strftime('%Y%m%d')}"
    
    async def create_episodic_node(self, document_path: str, content: str, group_id: str) -> EpisodicNode:
        """Create an EpisodicNode using Graphiti base class."""
        now = utc_now()
        
        episode = EpisodicNode(
            name=f"Document: {Path(document_path).name}",
            group_id=group_id,
            labels=["Document"],
            source=EpisodeType.message,  # or appropriate episode type
            source_description=f"Document processing of {document_path}",
            content=content,
            created_at=now,
            valid_at=now,
            entity_edges=[]
        )
        
        # Save using Graphiti's save method (adapted for FalkorDB)
        await self.save_episodic_node_to_falkor(episode)
        
        self.stats['episodes_created'] += 1
        logger.info(f"✅ Created EpisodicNode: {episode.uuid}")
        
        return episode
    
    async def save_episodic_node_to_falkor(self, episode: EpisodicNode):
        """Save EpisodicNode to FalkorDB using Graphiti structure."""
        try:
            # Convert to FalkorDB-compatible format
            properties = {
                'uuid': episode.uuid,
                'name': episode.name,
                'group_id': episode.group_id,
                'labels': episode.labels,
                'source': episode.source.value,
                'source_description': episode.source_description,
                'content': episode.content,
                'created_at': int(episode.created_at.timestamp() * 1000),
                'valid_at': int(episode.valid_at.timestamp() * 1000),
                'entity_edges': episode.entity_edges
            }
            
            # Create node in FalkorDB
            query = """
            CREATE (ep:Episodic:Episode {
                uuid: $uuid,
                name: $name,
                group_id: $group_id,
                source: $source,
                source_description: $source_description,
                content: $content,
                created_at: $created_at,
                valid_at: $valid_at,
                entity_edges: $entity_edges
            })
            RETURN ep.uuid
            """
            
            result = self.falkor_adapter.execute_cypher(query, properties)
            if result:
                logger.debug(f"Saved EpisodicNode to FalkorDB: {episode.uuid}")
            
        except Exception as e:
            logger.error(f"Error saving EpisodicNode: {e}")
            raise
    
    async def create_entity_node(self, name: str, entity_type: str, group_id: str, 
                               description: str = "", confidence: float = 1.0) -> EntityNode:
        """Create an EntityNode using Graphiti base class."""
        now = utc_now()
        
        entity = EntityNode(
            name=name,
            group_id=group_id,
            labels=[entity_type],
            created_at=now,
            summary=description,
            attributes={
                'type': entity_type,
                'confidence': confidence,
                'description': description
            }
        )
        
        # Generate name embedding if embedder available
        if self.embedder:
            await entity.generate_name_embedding(self.embedder)
        
        # Save to FalkorDB
        await self.save_entity_node_to_falkor(entity)
        
        self.stats['entities_created'] += 1
        logger.debug(f"✅ Created EntityNode: {entity.name} ({entity_type})")
        
        return entity
    
    async def save_entity_node_to_falkor(self, entity: EntityNode):
        """Save EntityNode to FalkorDB using Graphiti structure."""
        try:
            # Convert to FalkorDB-compatible format
            properties = {
                'uuid': entity.uuid,
                'name': entity.name,
                'group_id': entity.group_id,
                'labels': entity.labels,
                'summary': entity.summary,
                'created_at': int(entity.created_at.timestamp() * 1000),
                'name_embedding': entity.name_embedding,
                'type': entity.attributes.get('type', ''),
                'confidence': entity.attributes.get('confidence', 1.0),
                'description': entity.attributes.get('description', '')
            }
            
            # Create node in FalkorDB
            query = """
            CREATE (e:Entity {
                uuid: $uuid,
                name: $name,
                group_id: $group_id,
                summary: $summary,
                created_at: $created_at,
                type: $type,
                confidence: $confidence,
                description: $description
            })
            RETURN e.uuid
            """
            
            result = self.falkor_adapter.execute_cypher(query, properties)
            if result:
                logger.debug(f"Saved EntityNode to FalkorDB: {entity.uuid}")
            
        except Exception as e:
            logger.error(f"Error saving EntityNode: {e}")
            raise
    
    async def create_episodic_edge(self, episode: EpisodicNode, entity: EntityNode) -> EpisodicEdge:
        """Create an EpisodicEdge using Graphiti base class."""
        now = utc_now()
        
        edge = EpisodicEdge(
            group_id=episode.group_id,
            source_node_uuid=episode.uuid,
            target_node_uuid=entity.uuid,
            created_at=now
        )
        
        # Save to FalkorDB
        await self.save_episodic_edge_to_falkor(edge)
        
        self.stats['episodic_edges_created'] += 1
        logger.debug(f"✅ Created EpisodicEdge: {episode.uuid} -> {entity.uuid}")
        
        return edge
    
    async def save_episodic_edge_to_falkor(self, edge: EpisodicEdge):
        """Save EpisodicEdge to FalkorDB using Graphiti structure."""
        try:
            properties = {
                'uuid': edge.uuid,
                'group_id': edge.group_id,
                'created_at': int(edge.created_at.timestamp() * 1000)
            }
            
            query = """
            MATCH (ep:Episodic {uuid: $episode_uuid})
            MATCH (e:Entity {uuid: $entity_uuid})
            CREATE (ep)-[r:MENTIONS {
                uuid: $uuid,
                group_id: $group_id,
                created_at: $created_at
            }]->(e)
            RETURN r.uuid
            """
            
            params = {
                'episode_uuid': edge.source_node_uuid,
                'entity_uuid': edge.target_node_uuid,
                **properties
            }
            
            result = self.falkor_adapter.execute_cypher(query, params)
            if result:
                logger.debug(f"Saved EpisodicEdge to FalkorDB: {edge.uuid}")
            
        except Exception as e:
            logger.error(f"Error saving EpisodicEdge: {e}")
            raise
    
    async def extract_entities_with_llm(self, text: str) -> List[Dict[str, Any]]:
        """Extract entities using LLM with proper prompting."""
        if not self.llm_client:
            logger.warning("No LLM client available for entity extraction")
            return []
        
        try:
            prompt = f"""
            Extract entities from the following text. Return a JSON list of entities with name, type, and description.
            
            Entity types: Person, Organization, Location, Disease, Medication, Nutrient, Herb, Symptom, Treatment, Process, Concept
            
            Text: {text[:2000]}  # Limit text length
            
            Return format:
            [
                {{"name": "entity_name", "type": "entity_type", "description": "brief description"}},
                ...
            ]
            """
            
            response = await self.llm_client.generate_response(prompt)
            
            # Parse JSON response
            try:
                entities = json.loads(response)
                return entities if isinstance(entities, list) else []
            except json.JSONDecodeError:
                logger.warning("Failed to parse LLM entity extraction response")
                return []
            
        except Exception as e:
            logger.error(f"Error in LLM entity extraction: {e}")
            return []
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document using Graphiti base classes."""
        try:
            logger.info(f"🔄 Processing document: {file_path}")
            
            # Generate group_id for this document
            group_id = self.generate_group_id(file_path)
            
            # Extract text from document (using existing OCR)
            ocr_processor = MistralOCRProcessor()
            ocr_result = await ocr_processor.process_document(file_path)
            
            if not ocr_result.get('success'):
                return {'success': False, 'error': 'OCR processing failed'}
            
            text_content = ocr_result.get('text', '')
            
            # Create EpisodicNode for the document
            episode = await self.create_episodic_node(file_path, text_content, group_id)
            
            # Extract entities using LLM
            entities_data = await self.extract_entities_with_llm(text_content)
            
            # Create EntityNodes and EpisodicEdges
            entities = []
            for entity_data in entities_data:
                entity = await self.create_entity_node(
                    name=entity_data.get('name', ''),
                    entity_type=entity_data.get('type', 'Concept'),
                    group_id=group_id,
                    description=entity_data.get('description', ''),
                    confidence=entity_data.get('confidence', 1.0)
                )
                entities.append(entity)
                
                # Create EpisodicEdge linking episode to entity
                await self.create_episodic_edge(episode, entity)
            
            # Store embeddings for entities (if embedder available)
            if self.embedder:
                for entity in entities:
                    if entity.name_embedding:
                        store_embedding(
                            fact_uuid=entity.uuid,
                            episode_uuid=episode.uuid,
                            body=entity.name,
                            embedding=entity.name_embedding
                        )
                        self.stats['embeddings_stored'] += 1
            
            self.stats['documents_processed'] += 1
            
            logger.info(f"✅ Processed {file_path}: {len(entities)} entities created")
            
            return {
                'success': True,
                'episode_uuid': episode.uuid,
                'entities_created': len(entities),
                'group_id': group_id
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing document {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    def print_stats(self):
        """Print processing statistics."""
        print(f"\n📊 GRAPHITI PROCESSING STATISTICS:")
        print(f"  📄 Documents processed: {self.stats['documents_processed']}")
        print(f"  📝 Episodes created: {self.stats['episodes_created']}")
        print(f"  🏷️  Entities created: {self.stats['entities_created']}")
        print(f"  🔗 Episodic edges created: {self.stats['episodic_edges_created']}")
        print(f"  🧠 Embeddings stored: {self.stats['embeddings_stored']}")

    async def process_directory(self, directory_path: str, file_extensions: List[str] = None) -> Dict[str, Any]:
        """Process all documents in a directory."""
        if file_extensions is None:
            file_extensions = ['.pdf', '.txt', '.docx', '.one']

        directory = Path(directory_path)
        if not directory.exists():
            return {'success': False, 'error': f'Directory not found: {directory_path}'}

        # Find all matching files
        files = []
        for ext in file_extensions:
            files.extend(directory.glob(f'**/*{ext}'))

        if not files:
            return {'success': False, 'error': f'No files found with extensions {file_extensions}'}

        logger.info(f"🔄 Processing {len(files)} files from {directory_path}")

        results = []
        for file_path in files:
            try:
                result = await self.process_document(str(file_path))
                results.append({
                    'file': str(file_path),
                    'success': result['success'],
                    'entities': result.get('entities_created', 0),
                    'error': result.get('error')
                })

                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                results.append({
                    'file': str(file_path),
                    'success': False,
                    'entities': 0,
                    'error': str(e)
                })

        successful = sum(1 for r in results if r['success'])
        total_entities = sum(r['entities'] for r in results)

        return {
            'success': True,
            'files_processed': len(files),
            'successful': successful,
            'failed': len(files) - successful,
            'total_entities': total_entities,
            'results': results
        }

async def main():
    """Main processing function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python graphiti_compliant_ingestion.py <document_path>")
        print("  Directory:   python graphiti_compliant_ingestion.py --dir <directory_path>")
        return

    processor = GraphitiCompliantProcessor()
    await processor.initialize()

    if sys.argv[1] == '--dir':
        if len(sys.argv) < 3:
            print("Error: Directory path required")
            return

        directory_path = sys.argv[2]
        result = await processor.process_directory(directory_path)

        if result['success']:
            print(f"✅ Batch processing complete:")
            print(f"  📁 Files processed: {result['files_processed']}")
            print(f"  ✅ Successful: {result['successful']}")
            print(f"  ❌ Failed: {result['failed']}")
            print(f"  🏷️  Total entities: {result['total_entities']}")
            processor.print_stats()
        else:
            print(f"❌ Batch processing failed: {result.get('error')}")
    else:
        document_path = sys.argv[1]
        result = await processor.process_document(document_path)

        if result['success']:
            print(f"✅ Successfully processed: {document_path}")
            processor.print_stats()
        else:
            print(f"❌ Failed to process: {document_path}")
            print(f"Error: {result.get('error')}")

if __name__ == "__main__":
    asyncio.run(main())
