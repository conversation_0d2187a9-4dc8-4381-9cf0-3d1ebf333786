<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug References Display</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Debug References Display</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>API Response Test</h5>
                        <button onclick="testAPI()" class="btn btn-primary">Test API</button>
                        <pre id="api-response" class="mt-3"></pre>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>Display Test</h5>
                        <div class="stats-card">
                            <div class="stats-number" id="total-references">Loading...</div>
                            <div class="stats-label">
                                <i class="bi bi-bookmark"></i> References
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <label>Manual Test:</label>
                            <input type="number" id="manual-input" class="form-control" placeholder="Enter number">
                            <button onclick="setManual()" class="btn btn-secondary mt-2">Set Manually</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5>Console Log</h5>
                        <div id="console-log" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            border-radius: 10px;
            color: white;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }
    </style>

    <script>
        function log(message) {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function testAPI() {
            log('Testing API endpoint...');
            
            try {
                const response = await fetch('/api/fast/graph-stats');
                log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API Response: ${JSON.stringify(data, null, 2)}`);
                    
                    document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                    
                    // Update the display
                    const referencesElement = document.getElementById('total-references');
                    const referencesCount = data.total_references;
                    
                    log(`Setting total-references to: ${referencesCount}`);
                    referencesElement.textContent = referencesCount;
                    
                    // Check if it was set correctly
                    setTimeout(() => {
                        const actualValue = referencesElement.textContent;
                        log(`Actual displayed value: "${actualValue}"`);
                        log(`Element innerHTML: "${referencesElement.innerHTML}"`);
                        log(`Element style: ${referencesElement.style.cssText}`);
                        log(`Element computed style: ${window.getComputedStyle(referencesElement).cssText}`);
                    }, 100);
                    
                } else {
                    log(`API Error: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`Error: ${error.message}`);
                console.error('API Test Error:', error);
            }
        }
        
        function setManual() {
            const input = document.getElementById('manual-input');
            const value = input.value;
            const element = document.getElementById('total-references');
            
            log(`Setting manual value: ${value}`);
            element.textContent = value;
            
            setTimeout(() => {
                const actualValue = element.textContent;
                log(`Manual set result: "${actualValue}"`);
            }, 100);
        }
        
        // Auto-test on load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, starting auto-test...');
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
