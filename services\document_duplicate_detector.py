#!/usr/bin/env python3
"""
Document Duplicate Detection Service

This service provides comprehensive duplicate detection for documents before processing.
It uses multiple similarity metrics including:
- Content hash (SHA-256) for exact duplicates
- Filename similarity using fuzzy matching
- Content similarity using text embeddings
- Metadata similarity (file size, creation date, etc.)
"""

import hashlib
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import asyncio
import logging

from pydantic import BaseModel
from difflib import SequenceMatcher
from fuzzywuzzy import fuzz

from database.falkordb_adapter import get_falkordb_adapter
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class DuplicateMatch(BaseModel):
    """Model for a potential duplicate match."""
    document_id: str
    filename: str
    file_path: str
    similarity_score: float
    match_type: str  # 'exact_hash', 'filename', 'content', 'metadata'
    processed_at: Optional[str] = None
    file_size: Optional[int] = None
    document_hash: Optional[str] = None
    confidence: float = 0.0

class DuplicateDetectionResult(BaseModel):
    """Result of duplicate detection."""
    is_duplicate: bool
    matches: List[DuplicateMatch]
    highest_similarity: float
    recommendation: str  # 'skip', 'process', 'review'
    total_matches: int

class DocumentDuplicateDetector:
    """Service for detecting duplicate documents."""
    
    def __init__(self):
        self.similarity_thresholds = {
            'exact_hash': 1.0,  # Exact match
            'filename': 0.85,   # High filename similarity
            'content': 0.90,    # High content similarity
            'metadata': 0.80    # Metadata similarity
        }
        
    async def check_for_duplicates(
        self, 
        file_path: str, 
        content_hash: Optional[str] = None,
        extracted_text: Optional[str] = None
    ) -> DuplicateDetectionResult:
        """
        Check if a document is a duplicate of existing processed documents.
        
        Args:
            file_path: Path to the document file
            content_hash: Pre-computed content hash (optional)
            extracted_text: Extracted text content (optional)
            
        Returns:
            DuplicateDetectionResult with potential matches
        """
        try:
            logger.info(f"Checking for duplicates: {file_path}")
            
            # Get file metadata
            file_stats = await self._get_file_metadata(file_path)
            
            # Generate content hash if not provided
            if not content_hash:
                content_hash = await self._generate_content_hash(file_path)
            
            # Get all processed documents from database
            processed_docs = await self._get_processed_documents()
            
            matches = []
            
            # Check for exact hash matches (highest priority)
            exact_matches = await self._find_exact_hash_matches(content_hash, processed_docs)
            matches.extend(exact_matches)
            
            # Check for filename similarity
            filename_matches = await self._find_filename_matches(file_path, processed_docs)
            matches.extend(filename_matches)
            
            # Check for content similarity (if text provided)
            if extracted_text:
                content_matches = await self._find_content_matches(extracted_text, processed_docs)
                matches.extend(content_matches)
            
            # Check for metadata similarity
            metadata_matches = await self._find_metadata_matches(file_stats, processed_docs)
            matches.extend(metadata_matches)
            
            # Remove duplicates and sort by similarity
            unique_matches = self._deduplicate_matches(matches)
            unique_matches.sort(key=lambda x: x.similarity_score, reverse=True)
            
            # Determine if this is a duplicate and recommendation
            is_duplicate = len(unique_matches) > 0
            highest_similarity = unique_matches[0].similarity_score if unique_matches else 0.0
            recommendation = self._get_recommendation(unique_matches)
            
            result = DuplicateDetectionResult(
                is_duplicate=is_duplicate,
                matches=unique_matches[:5],  # Return top 5 matches
                highest_similarity=highest_similarity,
                recommendation=recommendation,
                total_matches=len(unique_matches)
            )
            
            logger.info(f"Duplicate check complete: {len(unique_matches)} matches found, highest similarity: {highest_similarity:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Error checking for duplicates: {e}", exc_info=True)
            # Return empty result on error
            return DuplicateDetectionResult(
                is_duplicate=False,
                matches=[],
                highest_similarity=0.0,
                recommendation='process',
                total_matches=0
            )
    
    async def _get_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get file metadata for comparison."""
        try:
            path = Path(file_path)
            stat = path.stat()
            
            return {
                'filename': path.name,
                'file_size': stat.st_size,
                'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extension': path.suffix.lower()
            }
        except Exception as e:
            logger.warning(f"Could not get file metadata: {e}")
            return {}
    
    async def _generate_content_hash(self, file_path: str) -> str:
        """Generate SHA-256 hash of file content."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.warning(f"Could not generate content hash: {e}")
            return ""
    
    async def _get_processed_documents(self) -> List[Dict[str, Any]]:
        """Get all processed documents from the database."""
        try:
            adapter = await get_falkordb_adapter()
            
            # Query to get all episodes (documents) with their metadata
            query = """
            MATCH (e:Episode)
            RETURN e.uuid as document_id, 
                   e.name as filename, 
                   e.file_path as file_path,
                   e.processed_at as processed_at,
                   e.metadata as metadata
            """
            
            result = adapter.execute_cypher(query)

            documents = []
            # FalkorDB returns [headers, rows] format
            if result and len(result) > 1:
                for row in result[1]:
                    # Row format: [document_id, filename, file_path, processed_at, metadata]
                    doc_data = {
                        'document_id': row[0] if len(row) > 0 else '',
                        'filename': row[1] if len(row) > 1 else '',
                        'file_path': row[2] if len(row) > 2 else '',
                        'processed_at': row[3] if len(row) > 3 else '',
                        'metadata': row[4] if len(row) > 4 else {}
                    }
                
                # Extract additional metadata if available
                metadata = doc_data.get('metadata', {})
                if isinstance(metadata, dict):
                    doc_data['document_hash'] = metadata.get('document_hash', '')
                    doc_data['file_size'] = metadata.get('file_size', 0)
                    doc_data['word_count'] = metadata.get('word_count', 0)
                
                documents.append(doc_data)
            
            logger.info(f"Retrieved {len(documents)} processed documents for comparison")
            return documents
            
        except Exception as e:
            logger.error(f"Error retrieving processed documents: {e}", exc_info=True)
            return []
    
    async def _find_exact_hash_matches(self, content_hash: str, processed_docs: List[Dict]) -> List[DuplicateMatch]:
        """Find documents with exact content hash matches."""
        matches = []
        
        if not content_hash:
            return matches
        
        for doc in processed_docs:
            doc_hash = doc.get('document_hash', '')
            if doc_hash and doc_hash == content_hash:
                match = DuplicateMatch(
                    document_id=doc['document_id'],
                    filename=doc['filename'],
                    file_path=doc['file_path'],
                    similarity_score=1.0,
                    match_type='exact_hash',
                    processed_at=doc.get('processed_at'),
                    file_size=doc.get('file_size'),
                    document_hash=doc_hash,
                    confidence=1.0
                )
                matches.append(match)
        
        return matches
    
    async def _find_filename_matches(self, file_path: str, processed_docs: List[Dict]) -> List[DuplicateMatch]:
        """Find documents with similar filenames."""
        matches = []
        current_filename = Path(file_path).name
        
        for doc in processed_docs:
            doc_filename = doc.get('filename', '')
            if not doc_filename:
                continue
            
            # Calculate filename similarity using multiple methods
            ratio = fuzz.ratio(current_filename.lower(), doc_filename.lower())
            token_sort_ratio = fuzz.token_sort_ratio(current_filename.lower(), doc_filename.lower())
            partial_ratio = fuzz.partial_ratio(current_filename.lower(), doc_filename.lower())
            
            # Use the highest similarity score
            similarity = max(ratio, token_sort_ratio, partial_ratio) / 100.0
            
            if similarity >= self.similarity_thresholds['filename']:
                match = DuplicateMatch(
                    document_id=doc['document_id'],
                    filename=doc_filename,
                    file_path=doc['file_path'],
                    similarity_score=similarity,
                    match_type='filename',
                    processed_at=doc.get('processed_at'),
                    file_size=doc.get('file_size'),
                    document_hash=doc.get('document_hash'),
                    confidence=similarity
                )
                matches.append(match)
        
        return matches

    async def _find_content_matches(self, extracted_text: str, processed_docs: List[Dict]) -> List[DuplicateMatch]:
        """Find documents with similar content using text similarity."""
        matches = []

        # For now, we'll use a simple approach based on word count and text length
        # In a more advanced implementation, we could use embeddings for semantic similarity

        current_word_count = len(extracted_text.split())
        current_char_count = len(extracted_text)

        for doc in processed_docs:
            doc_word_count = doc.get('word_count', 0)

            if doc_word_count == 0:
                continue

            # Calculate similarity based on word count difference
            word_count_diff = abs(current_word_count - doc_word_count)
            max_word_count = max(current_word_count, doc_word_count)

            if max_word_count > 0:
                word_similarity = 1.0 - (word_count_diff / max_word_count)

                if word_similarity >= self.similarity_thresholds['content']:
                    match = DuplicateMatch(
                        document_id=doc['document_id'],
                        filename=doc['filename'],
                        file_path=doc['file_path'],
                        similarity_score=word_similarity,
                        match_type='content',
                        processed_at=doc.get('processed_at'),
                        file_size=doc.get('file_size'),
                        document_hash=doc.get('document_hash'),
                        confidence=word_similarity * 0.8  # Lower confidence for content matching
                    )
                    matches.append(match)

        return matches

    async def _find_metadata_matches(self, file_stats: Dict[str, Any], processed_docs: List[Dict]) -> List[DuplicateMatch]:
        """Find documents with similar metadata (file size, etc.)."""
        matches = []
        current_size = file_stats.get('file_size', 0)

        if current_size == 0:
            return matches

        for doc in processed_docs:
            doc_size = doc.get('file_size', 0)

            if doc_size == 0:
                continue

            # Calculate file size similarity
            size_diff = abs(current_size - doc_size)
            max_size = max(current_size, doc_size)

            if max_size > 0:
                size_similarity = 1.0 - (size_diff / max_size)

                # Only consider as potential duplicate if file sizes are very similar
                if size_similarity >= 0.95:  # 95% size similarity
                    match = DuplicateMatch(
                        document_id=doc['document_id'],
                        filename=doc['filename'],
                        file_path=doc['file_path'],
                        similarity_score=size_similarity,
                        match_type='metadata',
                        processed_at=doc.get('processed_at'),
                        file_size=doc_size,
                        document_hash=doc.get('document_hash'),
                        confidence=size_similarity * 0.6  # Lower confidence for metadata matching
                    )
                    matches.append(match)

        return matches

    def _deduplicate_matches(self, matches: List[DuplicateMatch]) -> List[DuplicateMatch]:
        """Remove duplicate matches (same document matched by different methods)."""
        seen_docs = set()
        unique_matches = []

        # Sort by similarity score (highest first) to keep the best match for each document
        matches.sort(key=lambda x: x.similarity_score, reverse=True)

        for match in matches:
            if match.document_id not in seen_docs:
                unique_matches.append(match)
                seen_docs.add(match.document_id)

        return unique_matches

    def _get_recommendation(self, matches: List[DuplicateMatch]) -> str:
        """Get processing recommendation based on matches."""
        if not matches:
            return 'process'

        highest_similarity = matches[0].similarity_score

        # Exact hash match - definitely skip
        if any(match.match_type == 'exact_hash' for match in matches):
            return 'skip'

        # Very high similarity - recommend review
        if highest_similarity >= 0.95:
            return 'review'

        # High similarity - recommend review
        if highest_similarity >= 0.85:
            return 'review'

        # Moderate similarity - can process but show warning
        return 'process'

    async def get_document_by_id(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific document."""
        try:
            adapter = await get_falkordb_adapter()

            query = f"""
            MATCH (e:Episode {{uuid: '{document_id}'}})
            OPTIONAL MATCH (e)-[:CONTAINS]->(f:Fact)
            OPTIONAL MATCH (f)-[:MENTIONS]->(entity:Entity)
            OPTIONAL MATCH (e)-[:HAS_REFERENCE]->(ref:Reference)
            RETURN e.uuid as document_id,
                   e.name as filename,
                   e.file_path as file_path,
                   e.processed_at as processed_at,
                   e.metadata as metadata,
                   count(DISTINCT f) as chunks,
                   count(DISTINCT entity) as entities,
                   count(DISTINCT ref) as references
            """

            result = adapter.execute_cypher(query)

            # FalkorDB returns [headers, rows] format
            if result and len(result) > 1 and len(result[1]) > 0:
                row = result[1][0]  # First row
                return {
                    'document_id': row[0] if len(row) > 0 else '',
                    'filename': row[1] if len(row) > 1 else '',
                    'file_path': row[2] if len(row) > 2 else '',
                    'processed_at': row[3] if len(row) > 3 else '',
                    'metadata': row[4] if len(row) > 4 else {},
                    'chunks': row[5] if len(row) > 5 else 0,
                    'entities': row[6] if len(row) > 6 else 0,
                    'references': row[7] if len(row) > 7 else 0
                }

            return None

        except Exception as e:
            logger.error(f"Error getting document by ID: {e}", exc_info=True)
            return None

# Global instance
_duplicate_detector = None

async def get_document_duplicate_detector() -> DocumentDuplicateDetector:
    """Get the global document duplicate detector instance."""
    global _duplicate_detector
    if _duplicate_detector is None:
        _duplicate_detector = DocumentDuplicateDetector()
    return _duplicate_detector
