#!/usr/bin/env python3
"""
Demonstration script for document duplicate detection system.
This script shows how the duplicate detection works by:
1. Processing a test document
2. Attempting to upload the same document again
3. Showing the duplicate detection results
"""

import asyncio
import os
import shutil
from pathlib import Path

async def demo_duplicate_detection():
    """Demonstrate the duplicate detection system."""
    print("🚀 Document Duplicate Detection Demonstration")
    print("=" * 50)
    
    try:
        # Import required modules
        from services.document_duplicate_detector import get_document_duplicate_detector
        from services.document_processing.metadata_processor import MetadataProcessor
        from utils.config import get_config
        
        # Get detector and config
        detector = await get_document_duplicate_detector()
        config = get_config()
        
        # Test document path
        test_doc_path = "test_document.txt"
        
        if not os.path.exists(test_doc_path):
            print("❌ Test document not found. Please ensure test_document.txt exists.")
            return
        
        print(f"📄 Using test document: {test_doc_path}")
        
        # Step 1: Check for duplicates (should find none initially)
        print("\n🔍 Step 1: Initial duplicate check...")
        
        # Read the test document content
        with open(test_doc_path, 'r', encoding='utf-8') as f:
            test_content = f.read()
        
        result1 = await detector.check_for_duplicates(
            file_path=test_doc_path,
            extracted_text=test_content
        )
        
        print(f"   Initial check results:")
        print(f"   - Is duplicate: {result1.is_duplicate}")
        print(f"   - Total matches: {result1.total_matches}")
        print(f"   - Recommendation: {result1.recommendation}")
        
        # Step 2: Simulate document processing by copying to uploads directory
        print("\n📤 Step 2: Simulating document upload and processing...")
        
        uploads_dir = Path(config['paths']['uploads_dir'])
        uploads_dir.mkdir(exist_ok=True)
        
        # Copy test document to uploads directory with a unique name
        processed_doc_path = uploads_dir / "processed_test_document.txt"
        shutil.copy2(test_doc_path, processed_doc_path)
        
        print(f"   Document copied to: {processed_doc_path}")
        
        # Generate metadata for the processed document
        metadata_processor = MetadataProcessor()
        metadata = await metadata_processor.extract_metadata(str(processed_doc_path), test_content)
        
        print(f"   Generated metadata:")
        print(f"   - Document hash: {metadata.get('document_hash', 'N/A')[:16]}...")
        print(f"   - File size: {metadata.get('file_size', 'N/A')} bytes")
        print(f"   - Word count: {metadata.get('word_count', 'N/A')}")
        
        # Step 3: Simulate storing the document in the knowledge graph
        print("\n💾 Step 3: Simulating document storage in knowledge graph...")
        
        # For demonstration, we'll manually add a document record
        # In real usage, this would be done by the document processing pipeline
        from database.database_service import create_episode_node
        
        episode_data = {
            'uuid': 'demo-test-document-123',
            'name': 'processed_test_document.txt',
            'file_path': str(processed_doc_path),
            'processed_at': '2025-07-18T11:50:00',
            'chunks_count': 5,
            'metadata': metadata
        }
        
        episode_id = await create_episode_node('processed_test_document.txt', episode_data)
        print(f"   Created episode with ID: {episode_id}")
        
        # Step 4: Now check for duplicates again (should find the processed document)
        print("\n🔍 Step 4: Duplicate check after processing...")
        
        # Create a copy of the test document with a slightly different name
        duplicate_test_path = "test_document_copy.txt"
        shutil.copy2(test_doc_path, duplicate_test_path)
        
        result2 = await detector.check_for_duplicates(
            file_path=duplicate_test_path,
            extracted_text=test_content
        )
        
        print(f"   Duplicate check results:")
        print(f"   - Is duplicate: {result2.is_duplicate}")
        print(f"   - Total matches: {result2.total_matches}")
        print(f"   - Highest similarity: {result2.highest_similarity:.2%}")
        print(f"   - Recommendation: {result2.recommendation}")
        
        if result2.matches:
            print(f"   - Matches found:")
            for i, match in enumerate(result2.matches):
                print(f"     {i+1}. {match.filename}")
                print(f"        Similarity: {match.similarity_score:.2%}")
                print(f"        Match type: {match.match_type}")
                print(f"        Confidence: {match.confidence:.2%}")
        
        # Step 5: Test different similarity scenarios
        print("\n🧪 Step 5: Testing different similarity scenarios...")
        
        # Create a document with similar filename but different content
        similar_filename_path = "test_document_similar.txt"
        with open(similar_filename_path, 'w', encoding='utf-8') as f:
            f.write("This is a completely different document with similar filename.")
        
        result3 = await detector.check_for_duplicates(
            file_path=similar_filename_path,
            extracted_text="This is a completely different document with similar filename."
        )
        
        print(f"   Similar filename test:")
        print(f"   - Is duplicate: {result3.is_duplicate}")
        print(f"   - Total matches: {result3.total_matches}")
        print(f"   - Highest similarity: {result3.highest_similarity:.2%}")
        print(f"   - Recommendation: {result3.recommendation}")
        
        # Step 6: Show statistics
        print("\n📊 Step 6: Duplicate detection statistics...")
        
        processed_docs = await detector._get_processed_documents()
        print(f"   - Total processed documents: {len(processed_docs)}")
        
        docs_with_hash = sum(1 for doc in processed_docs if doc.get('document_hash'))
        print(f"   - Documents with hash: {docs_with_hash}")
        
        if processed_docs:
            print(f"   - Sample document:")
            sample_doc = processed_docs[0]
            print(f"     Filename: {sample_doc.get('filename', 'N/A')}")
            print(f"     Hash: {sample_doc.get('document_hash', 'N/A')[:16]}...")
            print(f"     File size: {sample_doc.get('file_size', 'N/A')} bytes")
        
        # Cleanup
        print("\n🧹 Cleanup...")
        cleanup_files = [duplicate_test_path, similar_filename_path, processed_doc_path]
        for file_path in cleanup_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   Removed: {file_path}")
        
        print("\n✅ Duplicate detection demonstration completed successfully!")
        print("\nKey takeaways:")
        print("- The system successfully detects exact duplicates (hash matching)")
        print("- Filename similarity is detected using fuzzy matching")
        print("- Content similarity is analyzed based on word count and text analysis")
        print("- The system provides appropriate recommendations based on similarity levels")
        print("- Integration with the knowledge graph enables persistent duplicate tracking")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demo_duplicate_detection())
