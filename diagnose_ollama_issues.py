#!/usr/bin/env python3
"""
Diagnose Ollama/MedGemma issues and provide solutions.
"""

import subprocess
import requests
import time

def check_docker_container():
    """Check Docker container status and logs."""
    print("🐳 DOCKER CONTAINER DIAGNOSIS")
    print("=" * 40)
    
    try:
        # Find Ollama container
        result = subprocess.run(['docker', 'ps', '-a', '--filter', 'name=ollama'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                print("📊 Ollama Container Status:")
                for line in lines:
                    print(f"   {line}")
                
                # Get container name/ID
                container_line = lines[1] if len(lines) > 1 else ""
                container_id = container_line.split()[0] if container_line else None
                
                if container_id:
                    print(f"\n📋 Container Logs (last 20 lines):")
                    log_result = subprocess.run(['docker', 'logs', '--tail', '20', container_id], 
                                              capture_output=True, text=True, timeout=10)
                    if log_result.returncode == 0:
                        print(log_result.stdout)
                        if log_result.stderr:
                            print("STDERR:")
                            print(log_result.stderr)
                    else:
                        print("❌ Could not get container logs")
                
                # Check container stats
                print(f"\n📈 Container Resource Usage:")
                stats_result = subprocess.run(['docker', 'stats', '--no-stream', container_id], 
                                            capture_output=True, text=True, timeout=10)
                if stats_result.returncode == 0:
                    print(stats_result.stdout)
                
                return container_id
            else:
                print("❌ No Ollama container found")
                return None
        else:
            print("❌ Could not check Docker containers")
            return None
            
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return None

def test_ollama_health():
    """Test Ollama health and model loading."""
    print(f"\n🏥 OLLAMA HEALTH CHECK")
    print("=" * 30)
    
    try:
        # Test basic connectivity
        print("🔄 Testing basic connectivity...")
        response = requests.get('http://localhost:11434/api/tags', timeout=10)
        
        if response.status_code == 200:
            print("✅ Ollama API is responding")
            
            models = response.json().get('models', [])
            medgemma_models = [m for m in models if 'medgemma' in m.get('name', '').lower()]
            
            if medgemma_models:
                print(f"✅ MedGemma models found: {len(medgemma_models)}")
                for model in medgemma_models:
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0) / (1024**3)
                    print(f"   📦 {name} ({size:.1f} GB)")
            else:
                print("❌ No MedGemma models found")
                return False
        else:
            print(f"❌ Ollama API error: {response.status_code}")
            return False
        
        # Test model loading with a very simple request
        print(f"\n🔄 Testing model loading...")
        simple_response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                "model": "alibayram/medgemma:4b",
                "prompt": "Hello",
                "stream": False,
                "options": {"num_predict": 5}
            },
            timeout=60
        )
        
        if simple_response.status_code == 200:
            result = simple_response.json()
            response_text = result.get('response', '')
            print(f"✅ Model loading successful")
            print(f"   Response: {response_text[:50]}...")
            return True
        else:
            print(f"❌ Model loading failed: {simple_response.status_code}")
            print(f"   Error: {simple_response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout - model may be loading slowly")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def provide_solutions():
    """Provide solutions for common Ollama issues."""
    print(f"\n🔧 SOLUTIONS FOR OLLAMA ISSUES")
    print("=" * 40)
    
    print("💡 Common Solutions:")
    print()
    
    print("1. 🔄 RESTART OLLAMA CONTAINER:")
    print("   docker restart <ollama-container-name>")
    print("   # Wait 30-60 seconds for models to load")
    print()
    
    print("2. 💾 INCREASE DOCKER MEMORY:")
    print("   # In Docker Desktop: Settings > Resources > Memory")
    print("   # Increase to at least 8GB for MedGemma")
    print()
    
    print("3. 🧹 CLEAN UP DOCKER RESOURCES:")
    print("   docker system prune")
    print("   docker volume prune")
    print()
    
    print("4. 🔄 RELOAD MEDGEMMA MODEL:")
    print("   docker exec <ollama-container> ollama pull alibayram/medgemma:4b")
    print()
    
    print("5. 📊 CHECK SYSTEM RESOURCES:")
    print("   # Close other applications to free RAM")
    print("   # Ensure at least 4GB free RAM")
    print()
    
    print("6. 🔄 RESTART DOCKER ENTIRELY:")
    print("   # In Docker Desktop: Restart Docker")
    print("   # Or: sudo systemctl restart docker (Linux)")
    print()
    
    print("7. 🎯 ALTERNATIVE: USE SMALLER MODEL:")
    print("   # Try a smaller model if resources are limited")
    print("   docker exec <ollama-container> ollama pull llama3.1:8b")

def recommend_next_steps():
    """Recommend next steps based on diagnosis."""
    print(f"\n🎯 RECOMMENDED NEXT STEPS")
    print("=" * 35)
    
    print("Based on the diagnosis, here's what to do:")
    print()
    
    print("🔄 IMMEDIATE ACTIONS:")
    print("1. Restart the Ollama Docker container")
    print("2. Wait 60 seconds for models to load")
    print("3. Test with a simple query")
    print()
    
    print("⚠️ IF STILL FAILING:")
    print("1. Check Docker memory allocation (needs 8GB+)")
    print("2. Close other applications to free RAM")
    print("3. Consider using OpenRouter instead")
    print()
    
    print("✅ FALLBACK OPTION:")
    print("Switch back to OpenRouter Llama Maverick:")
    print("   ENTITY_EXTRACTION_PROVIDER=openrouter")
    print("   ENTITY_EXTRACTION_MODEL=meta-llama/llama-4-maverick")
    print()
    
    print("💡 COST-BENEFIT ANALYSIS:")
    print("   OpenRouter: Reliable, fast, costs ~$0.01 per document")
    print("   MedGemma: Free, local, but requires stable Docker setup")

def main():
    """Main diagnosis function."""
    print("🚀 OLLAMA/MEDGEMMA DIAGNOSIS")
    print("=" * 50)
    
    # Check Docker container
    container_id = check_docker_container()
    
    # Test Ollama health
    health_ok = test_ollama_health()
    
    # Provide solutions
    provide_solutions()
    
    # Recommend next steps
    recommend_next_steps()
    
    # Final recommendation
    print(f"\n🎯 FINAL RECOMMENDATION:")
    if health_ok:
        print("   ✅ MedGemma appears to be working - try the entity extraction test again")
    else:
        print("   ⚠️ MedGemma has issues - consider using OpenRouter for reliability")
        print("   💰 OpenRouter cost is minimal (~$0.01 per document) for the reliability gained")

if __name__ == "__main__":
    main()
