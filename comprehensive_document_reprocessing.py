#!/usr/bin/env python3
"""
Comprehensive Document Reprocessing with AI-Powered Intelligent Reference Extractor
- Reprocesses all documents using the unified ingestion pipeline
- Ensures proper document metadata linking
- Updates master CSV as per normal ingestion workflow
- Maintains database consistency and relationships
"""

import asyncio
import os
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
from unified_ingestion_pipeline import get_unified_pipeline
from utils.logging_utils import get_logger
from dotenv import load_dotenv
from datetime import datetime
import json

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

class ComprehensiveReprocessor:
    """Comprehensive document reprocessor using unified pipeline."""
    
    def __init__(self):
        self.pipeline = None
        self.processed_documents = []
        self.master_references = []
        self.processing_stats = {
            'total_documents': 0,
            'successful_documents': 0,
            'failed_documents': 0,
            'total_references': 0,
            'total_entities': 0,
            'total_chunks': 0,
            'total_embeddings': 0
        }
        
    async def initialize_pipeline(self):
        """Initialize the unified ingestion pipeline."""
        print("🔧 Initializing unified ingestion pipeline...")
        try:
            self.pipeline = await get_unified_pipeline()
            print("✅ Unified pipeline initialized successfully")
            
            # Verify AI-powered reference extraction is available
            if hasattr(self.pipeline, 'intelligent_extractor') and self.pipeline.intelligent_extractor:
                if self.pipeline.intelligent_extractor.ai_available:
                    print("✅ AI-powered reference extraction: Available")
                else:
                    print("⚠️ AI-powered reference extraction: Not available (will use pattern-based)")
            else:
                print("⚠️ Intelligent extractor not found in pipeline")
                
            return True
        except Exception as e:
            print(f"❌ Failed to initialize pipeline: {e}")
            return False
    
    async def find_all_documents(self) -> List[Path]:
        """Find all documents to reprocess."""
        print("\n🔍 Scanning for documents to reprocess...")
        
        # Search locations
        search_paths = [
            Path("uploads"),
            Path("documents"), 
            Path("processed_documents"),
            Path("temp_uploads"),
            Path(".")
        ]
        
        # Supported file types (focus on what the pipeline can handle)
        extensions = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.txt']
        
        documents = []
        for search_path in search_paths:
            if search_path.exists():
                print(f"📂 Searching in: {search_path}")
                for ext in extensions:
                    pattern = f"*{ext}"
                    found_files = list(search_path.glob(pattern))
                    if found_files:
                        print(f"   Found {len(found_files)} {ext} files")
                    documents.extend(found_files)
        
        # Remove duplicates and filter
        unique_documents = []
        seen_names = set()
        
        for doc in documents:
            # Skip temporary files, system files, and very small files
            if any(skip in doc.name.lower() for skip in ['temp', 'tmp', '~', '.git', '__pycache__']):
                continue
                
            if doc.stat().st_size < 5000:  # Less than 5KB (likely not real documents)
                continue
                
            # Use filename as key to avoid duplicates
            key = doc.name.lower()
            if key not in seen_names:
                seen_names.add(key)
                unique_documents.append(doc)
        
        # Sort by file size (process smaller files first for faster feedback)
        unique_documents.sort(key=lambda x: x.stat().st_size)
        
        print(f"\n📄 Found {len(unique_documents)} documents to process:")
        for i, doc in enumerate(unique_documents, 1):
            size_mb = doc.stat().st_size / (1024 * 1024)
            print(f"   {i:2d}. {doc.name} ({size_mb:.1f} MB)")
        
        return unique_documents
    
    async def process_single_document(self, document_path: Path, doc_index: int, total_docs: int) -> Dict[str, Any]:
        """Process a single document using the unified pipeline."""
        print(f"\n{'='*70}")
        print(f"🚀 Processing Document {doc_index}/{total_docs}")
        print(f"📄 File: {document_path.name}")
        print(f"📊 Size: {document_path.stat().st_size:,} bytes ({document_path.stat().st_size/(1024*1024):.1f} MB)")
        print(f"{'='*70}")
        
        start_time = datetime.now()
        
        try:
            # Use the unified pipeline for complete processing
            result = await self.pipeline.process_document(
                file_path=document_path,
                chunk_size=1200,
                overlap=0,
                extract_entities=True,      # Extract entities
                extract_references=True,    # AI-powered reference extraction
                extract_metadata=True,      # Extract metadata
                generate_embeddings=True    # Generate embeddings
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if result.get('success', False):
                # Extract statistics
                references_found = result.get('references', 0)
                entities_found = result.get('entities', 0)
                chunks_created = result.get('chunks', 0)
                embeddings_generated = result.get('embeddings', 0)
                text_length = result.get('text_length', 0)
                
                print(f"\n✅ Processing SUCCESSFUL!")
                print(f"   📄 Text extracted: {text_length:,} characters")
                print(f"   🧩 Chunks created: {chunks_created}")
                print(f"   🏷️ Entities extracted: {entities_found}")
                print(f"   📚 References found: {references_found}")
                print(f"   🔗 Embeddings generated: {embeddings_generated}")
                print(f"   ⏱️ Processing time: {processing_time:.1f}s")
                
                # Update statistics
                self.processing_stats['successful_documents'] += 1
                self.processing_stats['total_references'] += references_found
                self.processing_stats['total_entities'] += entities_found
                self.processing_stats['total_chunks'] += chunks_created
                self.processing_stats['total_embeddings'] += embeddings_generated
                
                # Store document result
                document_result = {
                    'document_name': document_path.name,
                    'document_path': str(document_path),
                    'success': True,
                    'text_length': text_length,
                    'references_found': references_found,
                    'entities_found': entities_found,
                    'chunks_created': chunks_created,
                    'embeddings_generated': embeddings_generated,
                    'reference_csv_path': result.get('reference_csv_path'),
                    'processing_time': processing_time,
                    'processed_at': datetime.now().isoformat(),
                    'pipeline_version': result.get('pipeline_version', 'unified_v1.0_ai_powered')
                }
                
                self.processed_documents.append(document_result)
                
                # Load references from CSV if available
                await self.load_references_from_csv(result.get('reference_csv_path'), document_path.name)
                
                return document_result
                
            else:
                error = result.get('error', 'Unknown error')
                print(f"\n❌ Processing FAILED: {error}")
                
                self.processing_stats['failed_documents'] += 1
                
                return {
                    'document_name': document_path.name,
                    'document_path': str(document_path),
                    'success': False,
                    'error': error,
                    'processing_time': processing_time,
                    'processed_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            print(f"\n❌ Processing ERROR: {e}")
            
            self.processing_stats['failed_documents'] += 1
            
            return {
                'document_name': document_path.name,
                'document_path': str(document_path),
                'success': False,
                'error': str(e),
                'processing_time': processing_time,
                'processed_at': datetime.now().isoformat()
            }
    
    async def load_references_from_csv(self, csv_path: str, document_name: str):
        """Load references from the generated CSV file."""
        if not csv_path or not Path(csv_path).exists():
            return
        
        try:
            df = pd.read_csv(csv_path)
            print(f"📄 Loading {len(df)} references from CSV: {Path(csv_path).name}")
            
            for _, row in df.iterrows():
                reference_data = {
                    'reference_text': row.get('reference_text', ''),
                    'source_document': document_name,
                    'confidence': row.get('confidence', 0.0),
                    'extraction_method': 'ai_powered_intelligent',
                    'csv_source': csv_path,
                    'processed_at': datetime.now().isoformat()
                }
                self.master_references.append(reference_data)
                
        except Exception as e:
            print(f"⚠️ Failed to load references from CSV: {e}")
    
    async def create_master_csv(self) -> str:
        """Create a comprehensive master CSV file with all references."""
        print(f"\n📊 Creating master CSV file...")
        
        if not self.master_references:
            print("⚠️ No references to save to master CSV")
            return ""
        
        # Create DataFrame
        df = pd.DataFrame(self.master_references)
        
        # Add quality scoring and deduplication
        df = self.enhance_master_csv(df)
        
        # Sort by quality and confidence
        df = df.sort_values(['quality_score', 'confidence'], ascending=[False, False])
        
        # Save master CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"master_references_comprehensive_{timestamp}.csv"
        csv_path = Path("references") / csv_filename
        
        # Ensure directory exists
        csv_path.parent.mkdir(exist_ok=True)
        
        # Save with proper encoding
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        print(f"✅ Master CSV created: {csv_path}")
        print(f"📊 Total references: {len(df):,}")
        print(f"🎯 Average quality score: {df['quality_score'].mean():.3f}")
        print(f"🏆 High quality (>0.7): {len(df[df['quality_score'] > 0.7]):,}")
        print(f"📚 Unique documents: {df['source_document'].nunique()}")
        
        return str(csv_path)
    
    def enhance_master_csv(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enhance master CSV with quality scores and deduplication."""
        print("🔍 Enhancing master CSV with quality scoring...")
        
        # Add quality scores
        df['quality_score'] = df['reference_text'].apply(self.calculate_quality_score)
        
        # Add reference length
        df['reference_length'] = df['reference_text'].str.len()
        
        # Add normalized text for deduplication
        df['normalized_text'] = df['reference_text'].apply(self.normalize_reference)
        
        # Remove duplicates based on normalized text
        initial_count = len(df)
        df = df.drop_duplicates(subset=['normalized_text'], keep='first')
        final_count = len(df)
        
        print(f"🔄 Deduplication: {initial_count:,} → {final_count:,} references ({initial_count-final_count:,} duplicates removed)")
        
        # Add reference ID
        df['reference_id'] = range(1, len(df) + 1)
        
        return df
    
    def calculate_quality_score(self, ref_text: str) -> float:
        """Calculate quality score for a reference."""
        if not ref_text or len(ref_text) < 20:
            return 0.0
        
        score = 0.0
        
        # Length scoring (optimal 50-300 chars)
        length = len(ref_text)
        if 50 <= length <= 300:
            score += 0.3
        elif 30 <= length <= 500:
            score += 0.2
        
        # Has year
        import re
        if re.search(r'\b(19|20)\d{2}\b', ref_text):
            score += 0.2
        
        # Has DOI
        if re.search(r'doi:', ref_text, re.IGNORECASE):
            score += 0.2
        
        # Has journal patterns
        if re.search(r'\b(journal|nature|science|cell|lancet|nejm|bmj)\b', ref_text, re.IGNORECASE):
            score += 0.2
        
        # Has author patterns
        if re.search(r'[A-Z][a-z]+,?\s+[A-Z]\.?', ref_text):
            score += 0.1
        
        return min(score, 1.0)
    
    def normalize_reference(self, ref_text: str) -> str:
        """Normalize reference for deduplication."""
        import re
        normalized = ref_text.lower()
        normalized = re.sub(r'\s+', ' ', normalized)
        normalized = re.sub(r'[^\w\s]', '', normalized)
        return normalized.strip()
    
    async def generate_comprehensive_report(self) -> str:
        """Generate a comprehensive processing report."""
        print(f"\n📋 Generating comprehensive report...")
        
        total_docs = self.processing_stats['total_documents']
        successful_docs = self.processing_stats['successful_documents']
        failed_docs = self.processing_stats['failed_documents']
        
        # Calculate averages
        if successful_docs > 0:
            avg_refs = self.processing_stats['total_references'] / successful_docs
            avg_entities = self.processing_stats['total_entities'] / successful_docs
            avg_chunks = self.processing_stats['total_chunks'] / successful_docs
            avg_embeddings = self.processing_stats['total_embeddings'] / successful_docs
            avg_time = sum(d.get('processing_time', 0) for d in self.processed_documents if d['success']) / successful_docs
        else:
            avg_refs = avg_entities = avg_chunks = avg_embeddings = avg_time = 0
        
        report = f"""# COMPREHENSIVE DOCUMENT REPROCESSING REPORT
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Pipeline: Unified Ingestion with AI-Powered Intelligent Reference Extractor

## 📊 PROCESSING SUMMARY
- **Total Documents:** {total_docs}
- **Successfully Processed:** {successful_docs}
- **Failed Processing:** {failed_docs}
- **Success Rate:** {(successful_docs/total_docs*100):.1f}%

## 📚 EXTRACTION RESULTS
- **Total References:** {self.processing_stats['total_references']:,}
- **Total Entities:** {self.processing_stats['total_entities']:,}
- **Total Chunks:** {self.processing_stats['total_chunks']:,}
- **Total Embeddings:** {self.processing_stats['total_embeddings']:,}

## 📈 AVERAGES PER DOCUMENT
- **References:** {avg_refs:.1f}
- **Entities:** {avg_entities:.1f}
- **Chunks:** {avg_chunks:.1f}
- **Embeddings:** {avg_embeddings:.1f}
- **Processing Time:** {avg_time:.1f} seconds

## 📄 DOCUMENT DETAILS
"""
        
        for doc in self.processed_documents:
            if doc['success']:
                report += f"✅ **{doc['document_name']}**\n"
                report += f"   - References: {doc['references_found']}\n"
                report += f"   - Entities: {doc['entities_found']}\n"
                report += f"   - Chunks: {doc['chunks_created']}\n"
                report += f"   - Embeddings: {doc['embeddings_generated']}\n"
                report += f"   - Time: {doc['processing_time']:.1f}s\n\n"
            else:
                report += f"❌ **{doc['document_name']}**: {doc.get('error', 'Unknown error')}\n\n"
        
        report += f"""## 🚀 SYSTEM IMPROVEMENTS
This comprehensive reprocessing using the AI-powered system shows:
- **Revolutionary reference extraction** with AI enhancement
- **Complete metadata linking** and database integration
- **Quality filtering** and deduplication
- **Unified pipeline** ensuring consistency

## 🎯 NEXT STEPS
1. Review the master CSV file for reference quality
2. Verify database entries and relationships
3. Test search and retrieval functionality
4. Monitor system performance with new data

---
*Report generated by Comprehensive Document Reprocessor v1.0*
"""
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"comprehensive_reprocessing_report_{timestamp}.md"
        report_path = Path("reports") / report_filename
        
        # Ensure directory exists
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Comprehensive report saved: {report_path}")
        return str(report_path)

async def main():
    """Main comprehensive reprocessing function."""
    print("🚀 COMPREHENSIVE DOCUMENT REPROCESSING")
    print("=" * 70)
    print("Using Unified Ingestion Pipeline with AI-Powered Reference Extraction")
    print("Ensures proper metadata linking and master CSV updates")
    print("=" * 70)
    
    # Check prerequisites
    openrouter_key = os.getenv('OPEN_ROUTER_API_KEY') or os.getenv('OPENROUTER_API_KEY')
    if not openrouter_key:
        print("❌ OpenRouter API key not found! Please check your .env file.")
        return
    
    print(f"🔑 OpenRouter API: ✅ Available")
    
    # Initialize reprocessor
    reprocessor = ComprehensiveReprocessor()
    
    # Initialize pipeline
    if not await reprocessor.initialize_pipeline():
        print("❌ Failed to initialize pipeline. Exiting.")
        return
    
    try:
        # Find all documents
        documents = await reprocessor.find_all_documents()
        
        if not documents:
            print("❌ No documents found to process!")
            return
        
        reprocessor.processing_stats['total_documents'] = len(documents)
        
        # Process each document
        print(f"\n🔄 Starting comprehensive processing of {len(documents)} documents...")
        
        for i, document_path in enumerate(documents, 1):
            await reprocessor.process_single_document(document_path, i, len(documents))
        
        # Create master CSV
        master_csv_path = await reprocessor.create_master_csv()
        
        # Generate comprehensive report
        report_path = await reprocessor.generate_comprehensive_report()
        
        # Final summary
        print(f"\n🎉 COMPREHENSIVE REPROCESSING COMPLETE!")
        print(f"=" * 70)
        print(f"📊 Documents processed: {reprocessor.processing_stats['successful_documents']}/{reprocessor.processing_stats['total_documents']}")
        print(f"📚 Total references: {reprocessor.processing_stats['total_references']:,}")
        print(f"🏷️ Total entities: {reprocessor.processing_stats['total_entities']:,}")
        print(f"🧩 Total chunks: {reprocessor.processing_stats['total_chunks']:,}")
        print(f"🔗 Total embeddings: {reprocessor.processing_stats['total_embeddings']:,}")
        print(f"📄 Master CSV: {master_csv_path}")
        print(f"📋 Report: {report_path}")
        print(f"=" * 70)
        print(f"🚀 All documents reprocessed with AI-powered extraction!")
        print(f"   Database entries updated with proper metadata linking")
        print(f"   Master CSV created with quality filtering and deduplication")
        
    except Exception as e:
        print(f"❌ Comprehensive reprocessing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
