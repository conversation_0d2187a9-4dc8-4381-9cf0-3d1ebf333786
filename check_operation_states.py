#!/usr/bin/env python3
"""
Check the operation states database for stuck operations.
"""

import sqlite3
from pathlib import Path
from utils.logging_utils import get_logger

logger = get_logger(__name__)

def check_operation_states():
    """Check the operation states database for stuck operations."""
    
    db_path = Path("data/operation_states.db")
    
    if not db_path.exists():
        logger.info("No operation states database found")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            # Get all operations
            cursor = conn.execute("""
                SELECT operation_id, filename, status, progress_percentage, 
                       current_step_name, start_time, last_update_time
                FROM operation_states 
                ORDER BY last_update_time DESC
            """)
            
            operations = cursor.fetchall()
            
            if not operations:
                logger.info("No operations found in database")
                return
            
            logger.info(f"Found {len(operations)} operations in database:")
            
            stuck_operations = []
            
            for op in operations:
                operation_id, filename, status, progress, step_name, start_time, last_update = op
                
                logger.info(f"  {operation_id[:8]}... | {filename} | {status} | {progress}% | {step_name}")
                
                # Check for stuck operations (processing status with Biopractia files)
                if status == 'processing' and 'Biopractia' in filename:
                    stuck_operations.append(operation_id)
                    logger.warning(f"    ⚠️  STUCK: {filename}")
            
            if stuck_operations:
                logger.warning(f"Found {len(stuck_operations)} stuck operations with Biopractia files")
                
                # Ask if we should clean them up
                response = input(f"\nClean up {len(stuck_operations)} stuck operations? (y/n): ")
                if response.lower() == 'y':
                    for operation_id in stuck_operations:
                        conn.execute("""
                            UPDATE operation_states 
                            SET status = 'failed', 
                                error_message = 'File not found - cleaned up stuck operation',
                                completion_time = datetime('now'),
                                updated_at = datetime('now')
                            WHERE operation_id = ?
                        """, (operation_id,))
                    
                    conn.commit()
                    logger.info(f"✅ Cleaned up {len(stuck_operations)} stuck operations")
                else:
                    logger.info("Skipped cleanup")
            else:
                logger.info("✅ No stuck operations found")
                
    except Exception as e:
        logger.error(f"Error checking operation states: {e}")

if __name__ == "__main__":
    check_operation_states()
