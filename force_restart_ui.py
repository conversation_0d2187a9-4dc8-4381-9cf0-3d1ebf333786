#!/usr/bin/env python3
"""
Force restart the UI with correct configuration.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def main():
    print("🔄 Force restarting UI with correct configuration...")
    
    # Kill any existing Python processes that might be running the app
    try:
        subprocess.run(["taskkill", "/f", "/im", "python.exe"], capture_output=True)
        time.sleep(2)
    except:
        pass
    
    # Set environment variables
    os.environ["FALKORDB_GRAPH"] = "graphiti_knowledge"
    os.environ["PYTHONDONTWRITEBYTECODE"] = "1"  # Prevent .pyc files
    
    print(f"✅ Set FALKORDB_GRAPH to: {os.environ['FALKORDB_GRAPH']}")
    
    # Clear Python cache
    import sys
    modules_to_clear = [
        'utils.config',
        'database.falkordb_adapter', 
        'database.database_service',
        'routes.entity_routes',
        'services.entity_service',
        'services.entity_query_service'
    ]
    
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
            print(f"✅ Cleared module: {module}")
    
    # Now start the app
    print("\n🚀 Starting application...")
    print("📊 Graph: graphiti_knowledge")
    print("📍 URL: http://localhost:9753")
    print("=" * 50)
    
    # Import and run
    try:
        import uvicorn
        from app import app
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=9753,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ Error starting app: {e}")

if __name__ == "__main__":
    main()
