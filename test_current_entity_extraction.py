#!/usr/bin/env python3
"""
Test what entity extraction system is currently being used in production.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.database_service import get_falkordb_adapter
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def check_current_entity_extraction():
    """Check what entity extraction system is currently being used."""
    try:
        print("🔍 CHECKING CURRENT ENTITY EXTRACTION SYSTEM")
        print("=" * 50)
        
        # Check environment variables
        import os
        provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER', 'Not set')
        model = os.environ.get('ENTITY_EXTRACTION_MODEL', 'Not set')
        
        print(f"📊 Current Configuration:")
        print(f"   Provider: {provider}")
        print(f"   Model: {model}")
        
        # Check if OpenRouter API key is available
        openrouter_key = os.environ.get('OPEN_ROUTER_API_KEY') or os.environ.get('OPENROUTER_API_KEY')
        if openrouter_key:
            print(f"   OpenRouter API Key: Available (ends with ...{openrouter_key[-4:]})")
        else:
            print(f"   OpenRouter API Key: Not found")
        
        # Check if Ollama is running
        try:
            import requests
            ollama_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')
            response = requests.get(f"{ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [m.get('name', 'Unknown') for m in models]
                print(f"   Ollama Status: Running with {len(models)} models")
                print(f"   Available Models: {', '.join(model_names[:5])}")
                
                # Check if qwen3-4b is available
                if any('qwen' in name.lower() for name in model_names):
                    print(f"   ✅ Qwen models available")
                else:
                    print(f"   ⚠️ No Qwen models found")
            else:
                print(f"   Ollama Status: Not responding (status {response.status_code})")
        except Exception as e:
            print(f"   Ollama Status: Not available ({e})")
        
        # Check recent entity extractions in the database
        adapter = await get_falkordb_adapter()
        
        # Get sample entities with their creation info
        query = """
        MATCH (e:Entity)
        RETURN e.name, e.type, e.created_at
        ORDER BY e.created_at DESC
        LIMIT 10
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1 and len(result[1]) > 0:
            print(f"\n📊 Recent Entity Extractions:")
            for i, row in enumerate(result[1][:5]):
                name = row[0] if len(row) > 0 else 'Unknown'
                entity_type = row[1] if len(row) > 1 else 'Unknown'
                created_at = row[2] if len(row) > 2 else 'Unknown'
                print(f"   {i+1}. {name} ({entity_type}) - {created_at}")
        else:
            print(f"\n⚠️ No entities found in database")
        
        # Check entity extraction statistics
        stats_query = """
        MATCH (e:Entity)
        WITH e.type as entity_type, count(e) as count
        RETURN entity_type, count
        ORDER BY count DESC
        LIMIT 10
        """
        
        stats_result = adapter.execute_cypher(stats_query)
        
        if stats_result and len(stats_result) > 1 and len(stats_result[1]) > 0:
            print(f"\n📈 Entity Type Distribution:")
            total_entities = 0
            for row in stats_result[1]:
                entity_type = row[0] if len(row) > 0 else 'Unknown'
                count = row[1] if len(row) > 1 else 0
                total_entities += count
                print(f"   {entity_type}: {count}")
            
            print(f"\n🎯 Total Entities: {total_entities}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking entity extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_extraction():
    """Test a simple entity extraction to see what's working."""
    try:
        print(f"\n🧪 TESTING SIMPLE ENTITY EXTRACTION")
        print("=" * 40)
        
        # Try to import and use the entity extractor
        from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
        
        # Simple test text
        test_text = "Vitamin C is an antioxidant that helps boost the immune system."
        
        print(f"📝 Test Text: {test_text}")
        
        extractor = LLMEntityExtractor()
        print(f"✅ Entity extractor created successfully")
        
        # Check what provider it's using
        print(f"   Provider: {extractor.llm_provider}")
        
        # Try extraction (but catch any errors)
        try:
            entities = await extractor.extract_entities(test_text)
            print(f"✅ Extraction successful: {len(entities)} entities found")
            
            for i, entity in enumerate(entities[:3]):
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                print(f"   {i+1}. {name} ({entity_type})")
                
        except Exception as e:
            print(f"❌ Extraction failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in simple extraction test: {e}")
        return False

async def main():
    """Main function."""
    print("🚀 CURRENT ENTITY EXTRACTION ANALYSIS")
    print("=" * 50)
    
    # Check current configuration
    config_ok = await check_current_entity_extraction()
    
    # Test simple extraction
    if config_ok:
        test_ok = await test_simple_extraction()
        
        if test_ok:
            print(f"\n💡 SUMMARY:")
            print("   Your current entity extraction system is configured and working")
            print("   You can proceed with PDF uploads to test extraction quality")
        else:
            print(f"\n⚠️ SUMMARY:")
            print("   Configuration looks good but extraction test failed")
            print("   May need to check API keys or model availability")
    else:
        print(f"\n❌ SUMMARY:")
        print("   Issues found with current entity extraction configuration")

if __name__ == "__main__":
    asyncio.run(main())
