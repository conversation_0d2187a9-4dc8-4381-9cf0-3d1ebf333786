#!/usr/bin/env python3
"""
Manually fix the token file with the access token from the error message.
"""

from pathlib import Path

# Extract the access token from the error message
access_token = "EwB4BMl6BAAUBKgm8k1UswUNwklmy2v7U/S+1fEAAVB9GPT6/jk3oBkHgPqXmwNDY4+zMrhpEA7O6p/pASsKIDGevVeaFclXujzeXPdeCXJIFgLTHC4p9BMIuLPIr08kzre5KKxYucxKYtsbm/FXMAkgkzVEIYAqdhZR80V4P1px6rhjeibVnGb2urwLbpU0WtXGaB1GgDj6ypuaQEhrN0IRLPBEjUG6L5hv6I871fhPBRQW+SP1Ey0z1gvp+FZorz8nfyyp/2RVtBd2P8pyrN7j+gJAU25q2RJO0zdZjG4K3OqsRqDFsYVt4WqHGOc572HJOkXyJ235NdGDlidAWT8JhPHCMc9gghdLz/czfchYp8xxsMFuCo9Tq1mANhsQZgAAELre8GZLzmxOlDPCWDGXnaRAA3+GjKyhHZxuNTk7wBwzBkQt+5LonlDYFGYYxmqMfI0qwJ55HVqrCFJzLIxt8/n3H9954LqdRYUfvtIhBgzzdftgrq9FD9sFtuxXnyJ+Yu+nHzBVCbDVPDr2f3jkPAeZ8aSRFAAnbdjXq6pexUzLdrxoc++AK1oUziyX5+1oKZmtl2Jh+WrjFKrnSyGTlRIjKOAtkC8WwfXf4C8NU54K0cyQyrOpXCJs7Ub80ZqJEr1HAoIx7JshnmsJpYov3QwVDMearwKO1rwmucencHOfP5d0w1wpxavuhRo+YHoCJGr3t0wjVz70Lpfdj2KufW1rOPgdyj1U4P+p5cPmURxY2IMXV+XWuraDFILRIYTspawBy5NVU6xhz/sG21dpvXJJNgrA1iINanmR7rUveRdBBrQHnJr7uX4E27fujzQhUN2e/GKp5nKmJHNWbaNQyq5N+yH4Q4uQTNqAewhH1eL6jb0CHBOam4C/jZMASYte6ACHdF38+pW/bhn5jDSt2mKyaCtFety0BWyTOhsaDgh+qIPiu5I6Ex7hL/4G7wYy5YCxoB79nOzt+w2G2otOMyLva+w7a/a2ciDfZ97JYQ3dEh9zEWXb3xGmpp+xiDddJMNaNQqoi32amqHk4IWzVQMRHafDwBqnc57QlSXghxQWXVD50t96XUN1S2p1wrJ9BHqH/LTNSeIZX01v4/2Gpw7iH/AjFpoorftOUGfyXqXOPM7gzzduT9gFJl1PxNf8k11htJ3M4yS8IBBKoeWXvOlb08dZfhgkLsNk3lKxHL+wbFDqjN6GjZxpBTO+ay26ulSci8lSIxEjoYSkpUSFJ1vmi6BgeyU0Z7cGJUxsJwwBzOjiXo1vyP3aAUMXiJBYaYDOOC2FI+XQ6xSqEiOMFUHeoljie0YjPYJaGXiUsbp9pg2E2Cd5Dsj8IbTAQgWq3bA9XbhaYFtE6GRB8imOyrTHMhBTXbKt15kQfSCykCs/HcrHLwVy2Bi2wCgUBSrdwVw4/GdE46FhYX30YHPTzkfn2qrjsNrvp2M3aIx7qV6f3Ok6IaYNBtdU7MPYuw5aVwixno0whIm0lbJoSTeoNMe5mOMoYKwLjikaIx5+MWFmaltlAw=="

def fix_token_files():
    """Fix both token files with the correct access token."""
    
    home = Path.home()
    credentials_dir = home / ".credentials"
    
    # Ensure credentials directory exists
    credentials_dir.mkdir(exist_ok=True)
    
    # Token file paths
    token_file = credentials_dir / "onenote_graph_token.txt"
    langchain_token_file = credentials_dir / "langchain_onenote_token.txt"
    
    print(f"🔧 Fixing token files...")
    print(f"📁 Token file: {token_file}")
    print(f"📁 LangChain file: {langchain_token_file}")
    
    # Write the access token to both files
    try:
        with open(token_file, 'w', encoding='utf-8') as f:
            f.write(access_token)
        
        with open(langchain_token_file, 'w', encoding='utf-8') as f:
            f.write(access_token)
        
        print(f"✅ Token files updated successfully!")
        print(f"📊 Token length: {len(access_token)} characters")
        print(f"📋 Token starts with: {access_token[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error writing token files: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Manual Token File Fix")
    print("=" * 25)
    
    success = fix_token_files()
    
    if success:
        print("\n✅ Token files fixed!")
        print("📋 Ready to run OneNote processing")
    else:
        print("\n❌ Failed to fix token files")
