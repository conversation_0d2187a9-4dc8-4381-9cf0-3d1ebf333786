#!/usr/bin/env python3
"""
Test script to verify AI-powered references are loading correctly.
"""

import asyncio
from services.reference_service import get_all_references
from models.reference import ReferenceFilter

async def test_reference_loading():
    """Test loading references from the new AI-powered master CSV."""
    
    print("🧪 Testing AI-Powered Reference Loading")
    print("=" * 50)
    
    try:
        # Load all references
        print("📄 Loading references...")
        reference_list = await get_all_references()
        
        if not reference_list or not reference_list.references:
            print("❌ No references loaded!")
            return
        
        references = reference_list.references
        total_refs = len(references)
        
        print(f"✅ Loaded {total_refs:,} references")
        
        # Calculate statistics
        ai_enhanced = len([r for r in references if getattr(r, 'ai_enhanced', False)])
        high_quality = len([r for r in references if getattr(r, 'quality_score', 0) > 0.7])
        unique_docs = len(set(r.source_document for r in references if r.source_document))
        
        # Quality distribution
        quality_scores = [getattr(r, 'quality_score', 0) for r in references]
        high_q = len([q for q in quality_scores if q > 0.7])
        medium_q = len([q for q in quality_scores if 0.4 <= q <= 0.7])
        low_q = len([q for q in quality_scores if 0 < q < 0.4])
        unscored = len([q for q in quality_scores if q == 0])
        
        print(f"\n📊 STATISTICS:")
        print(f"   Total References: {total_refs:,}")
        print(f"   AI-Enhanced: {ai_enhanced:,}")
        print(f"   High Quality (>0.7): {high_quality:,}")
        print(f"   Unique Documents: {unique_docs}")
        
        print(f"\n🎯 QUALITY DISTRIBUTION:")
        print(f"   High (0.7-1.0): {high_q:,} ({high_q/total_refs*100:.1f}%)")
        print(f"   Medium (0.4-0.7): {medium_q:,} ({medium_q/total_refs*100:.1f}%)")
        print(f"   Low (0.1-0.4): {low_q:,} ({low_q/total_refs*100:.1f}%)")
        print(f"   Unscored (0.0): {unscored:,} ({unscored/total_refs*100:.1f}%)")
        
        # Sample references
        print(f"\n📚 SAMPLE REFERENCES:")
        for i, ref in enumerate(references[:3], 1):
            print(f"\n   {i}. Reference ID: {getattr(ref, 'reference_id', 'N/A')}")
            print(f"      Text: {ref.reference_text[:100] if ref.reference_text else 'N/A'}...")
            print(f"      Source: {ref.source_document}")
            print(f"      Quality Score: {getattr(ref, 'quality_score', 'N/A')}")
            print(f"      AI Enhanced: {getattr(ref, 'ai_enhanced', 'N/A')}")
            print(f"      Confidence: {ref.confidence}")
            print(f"      Extraction Method: {ref.extraction_method}")
        
        # Check for AI-powered references specifically
        ai_refs = [r for r in references if getattr(r, 'ai_enhanced', False)]
        if ai_refs:
            print(f"\n🤖 AI-POWERED SAMPLE:")
            sample_ai = ai_refs[0]
            print(f"   Reference ID: {getattr(sample_ai, 'reference_id', 'N/A')}")
            print(f"   Text: {sample_ai.reference_text[:150] if sample_ai.reference_text else 'N/A'}...")
            print(f"   Quality Score: {getattr(sample_ai, 'quality_score', 'N/A')}")
            print(f"   Has DOI: {getattr(sample_ai, 'has_doi', 'N/A')}")
            print(f"   Has Year: {getattr(sample_ai, 'has_year', 'N/A')}")
            print(f"   Has Journal: {getattr(sample_ai, 'has_journal', 'N/A')}")
        
        print(f"\n✅ Reference loading test completed successfully!")
        
        # Test the API response format
        print(f"\n🔧 Testing API Response Format...")
        
        # Convert to dict format (like the API would)
        api_response = {
            "references": [
                {
                    "reference_id": getattr(ref, 'reference_id', ''),
                    "reference_text": ref.reference_text or '',
                    "source_document": ref.source_document or '',
                    "quality_score": getattr(ref, 'quality_score', 0.0),
                    "confidence": ref.confidence or 0.0,
                    "ai_enhanced": getattr(ref, 'ai_enhanced', False),
                    "extraction_method": ref.extraction_method.value if hasattr(ref.extraction_method, 'value') else str(ref.extraction_method),
                    "has_doi": getattr(ref, 'has_doi', False),
                    "has_year": getattr(ref, 'has_year', False),
                    "has_journal": getattr(ref, 'has_journal', False),
                    "uuid": getattr(ref, 'uuid', getattr(ref, 'reference_id', ''))
                }
                for ref in references[:5]  # First 5 for testing
            ],
            "total_count": total_refs,
            "ai_enhanced_count": ai_enhanced,
            "high_quality_count": high_quality,
            "unique_documents": unique_docs
        }
        
        print(f"✅ API response format prepared with {len(api_response['references'])} sample references")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reference loading: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_reference_loading())
    if success:
        print(f"\n🎉 All tests passed! References are loading correctly.")
    else:
        print(f"\n💥 Tests failed! Check the error messages above.")
