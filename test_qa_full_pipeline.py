#!/usr/bin/env python3
"""
Test the full Q&A pipeline step by step.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.qa_service import get_relevant_facts, generate_answer

async def test_full_qa_pipeline():
    """Test the full Q&A pipeline."""
    try:
        question = "cocoa and human health"
        
        print(f"🔍 TESTING FULL Q&A PIPELINE")
        print(f"Question: '{question}'")
        print("=" * 50)
        
        # Step 1: Get relevant facts
        print("📊 Step 1: Getting relevant facts...")
        facts = await get_relevant_facts(question, limit=5)
        
        print(f"   Found {len(facts)} facts")
        if facts:
            for i, fact in enumerate(facts[:2]):
                body = fact.get('body', '')[:100]
                doc_name = fact.get('document_name', 'Unknown')
                print(f"   {i+1}. {body}...")
                print(f"      From: {doc_name}")
        else:
            print("   ❌ No facts found - this is the problem!")
            return False
        
        # Step 2: Generate answer
        print(f"\n📊 Step 2: Generating answer...")
        try:
            answer_result = await generate_answer(
                question=question,
                facts=facts,
                response_length="brief"
            )
            
            answer = answer_result.get('answer', 'No answer')
            sources = answer_result.get('sources', [])
            
            print(f"   Answer: {answer[:200]}...")
            print(f"   Sources: {len(sources)} documents")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error generating answer: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Error in Q&A pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_full_qa_pipeline())
