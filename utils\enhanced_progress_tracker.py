"""
Enhanced Progress Tracking System for Document Processing

This module provides a comprehensive progress tracking system that captures
detailed processing metrics, step timings, and real-time statistics for
document processing operations.
"""

import time
import asyncio
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ProcessingStep(Enum):
    """Enumeration of processing steps."""
    INITIALIZING = "initializing"
    TEXT_EXTRACTION = "text_extraction"
    REFERENCE_EXTRACTION = "reference_extraction"
    ENTITY_EXTRACTION = "entity_extraction"
    CHUNK_PROCESSING = "chunk_processing"
    EMBEDDING_GENERATION = "embedding_generation"
    METADATA_EXTRACTION = "metadata_extraction"
    FINALIZATION = "finalization"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class StepMetrics:
    """Metrics for a processing step."""
    step: ProcessingStep
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    status: str = "pending"
    message: str = ""
    details: Dict[str, Any] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}

@dataclass
class ProcessingStatistics:
    """Overall processing statistics."""
    total_characters: int = 0
    total_chunks: int = 0
    total_entities: int = 0
    total_references: int = 0
    total_embeddings: int = 0
    entities_by_chunk: List[int] = None
    chunk_sizes: List[int] = None
    processing_errors: List[str] = None
    
    def __post_init__(self):
        if self.entities_by_chunk is None:
            self.entities_by_chunk = []
        if self.chunk_sizes is None:
            self.chunk_sizes = []
        if self.processing_errors is None:
            self.processing_errors = []

class EnhancedProgressTracker:
    """
    Enhanced progress tracking for document processing with detailed metrics.
    
    This tracker provides comprehensive monitoring of document processing
    including step-by-step progress, timing information, and detailed statistics.
    """
    
    def __init__(self, 
                 operation_id: str,
                 filename: str,
                 websocket_manager=None,
                 total_steps: int = 8):
        """
        Initialize the enhanced progress tracker.
        
        Args:
            operation_id: Unique identifier for the operation
            filename: Name of the file being processed
            websocket_manager: WebSocket manager for real-time updates
            total_steps: Total number of processing steps
        """
        self.operation_id = operation_id
        self.filename = filename
        self.websocket_manager = websocket_manager
        self.total_steps = total_steps
        
        # Timing information
        self.start_time = datetime.now(timezone.utc)
        self.end_time: Optional[datetime] = None
        
        # Step tracking
        self.current_step = 0
        self.current_step_name = ProcessingStep.INITIALIZING
        self.steps: Dict[ProcessingStep, StepMetrics] = {}
        
        # Statistics
        self.statistics = ProcessingStatistics()
        
        # Status tracking
        self.status = "initializing"
        self.overall_progress = 0
        self.current_message = "Starting processing..."
        self.is_completed = False
        self.is_failed = False

        # WebSocket optimization for real-time updates
        self.last_update_time = 0
        self.min_update_interval = 0.05  # Minimum 50ms between updates for better real-time feel
        self.force_update_steps = {'step_start', 'step_complete', 'final_complete', 'error'}  # Always send these immediately
        self.pending_update = False

        # Initialize all steps
        self._initialize_steps()
        
        logger.info(f"🚀 Enhanced progress tracker initialized for {filename} (ID: {operation_id})")
    
    def _initialize_steps(self):
        """Initialize all processing steps."""
        self.step_order = [
            ProcessingStep.INITIALIZING,
            ProcessingStep.TEXT_EXTRACTION,
            ProcessingStep.REFERENCE_EXTRACTION,
            ProcessingStep.CHUNK_PROCESSING,
            ProcessingStep.ENTITY_EXTRACTION,
            ProcessingStep.EMBEDDING_GENERATION,
            ProcessingStep.METADATA_EXTRACTION,
            ProcessingStep.FINALIZATION
        ]

        for step in self.step_order:
            self.steps[step] = StepMetrics(step=step)
    
    async def start_step(self, step: ProcessingStep, message: str = "", details: Dict[str, Any] = None):
        """
        Start a processing step.
        
        Args:
            step: The processing step to start
            message: Description of what's happening
            details: Additional details about the step
        """
        if details is None:
            details = {}
            
        # Update current step using step order
        if step in self.step_order:
            self.current_step = self.step_order.index(step) + 1
        else:
            # Fallback for unknown steps
            self.current_step = min(self.current_step + 1, self.total_steps)

        self.current_step_name = step
        self.current_message = message

        # Update step metrics
        step_metrics = self.steps[step]
        step_metrics.start_time = datetime.now(timezone.utc)
        step_metrics.status = "in_progress"
        step_metrics.message = message
        step_metrics.details.update(details)

        # Calculate progress (ensure it doesn't exceed 100%)
        self.overall_progress = min(100, int((self.current_step / self.total_steps) * 100))
        
        logger.info(f"📊 {self.filename}: Step {self.current_step}/{self.total_steps} - {step.value} - {message}")

        # Send immediate WebSocket update for step start
        await self._force_websocket_update("step_start")
    
    async def complete_step(self, step: ProcessingStep, message: str = "", details: Dict[str, Any] = None):
        """
        Complete a processing step.
        
        Args:
            step: The processing step to complete
            message: Completion message
            details: Additional details about the completion
        """
        if details is None:
            details = {}
            
        # Update step metrics
        step_metrics = self.steps[step]
        step_metrics.end_time = datetime.now(timezone.utc)
        step_metrics.status = "completed"
        step_metrics.message = message
        step_metrics.details.update(details)
        
        # Calculate duration
        if step_metrics.start_time:
            step_metrics.duration = (step_metrics.end_time - step_metrics.start_time).total_seconds()
        
        logger.info(f"✅ {self.filename}: Completed {step.value} in {step_metrics.duration:.2f}s - {message}")

        # Send immediate WebSocket update for step completion
        await self._force_websocket_update("step_complete")

    async def fail_step(self, step: ProcessingStep, error: str, details: Dict[str, Any] = None):
        """
        Mark a processing step as failed.

        Args:
            step: The processing step that failed
            error: Error message
            details: Additional details about the failure
        """
        if details is None:
            details = {}

        # Update step metrics
        step_metrics = self.steps[step]
        step_metrics.end_time = datetime.now(timezone.utc)
        step_metrics.status = "failed"
        step_metrics.error = error
        step_metrics.details.update(details)

        # Calculate duration
        if step_metrics.start_time:
            step_metrics.duration = (step_metrics.end_time - step_metrics.start_time).total_seconds()

        # Mark overall operation as failed
        self.is_failed = True
        self.status = "failed"
        self.current_message = f"Failed at {step.value}: {error}"

        # Add to processing errors
        self.statistics.processing_errors.append(f"{step.value}: {error}")

        logger.error(f"❌ {self.filename}: Failed at {step.value} - {error}")

        # Send WebSocket update
        await self._send_websocket_update()

    def update_statistics(self, **kwargs):
        """
        Update processing statistics.

        Args:
            **kwargs: Statistics to update (e.g., total_characters=1000)
        """
        for key, value in kwargs.items():
            if hasattr(self.statistics, key):
                setattr(self.statistics, key, value)
                logger.debug(f"📈 {self.filename}: Updated {key} = {value}")

    def add_chunk_entity_count(self, chunk_index: int, entity_count: int):
        """
        Add entity count for a specific chunk.

        Args:
            chunk_index: Index of the chunk
            entity_count: Number of entities extracted from the chunk
        """
        # Ensure the list is large enough
        while len(self.statistics.entities_by_chunk) <= chunk_index:
            self.statistics.entities_by_chunk.append(0)

        self.statistics.entities_by_chunk[chunk_index] = entity_count
        logger.debug(f"📊 {self.filename}: Chunk {chunk_index + 1} extracted {entity_count} entities")

    async def complete_processing(self, final_message: str = "Processing completed successfully"):
        """
        Mark the entire processing operation as completed.

        Args:
            final_message: Final completion message
        """
        self.end_time = datetime.now(timezone.utc)
        self.is_completed = True
        self.status = "completed"
        self.current_message = final_message
        self.overall_progress = 100
        self.current_step_name = ProcessingStep.COMPLETED

        # Calculate total duration
        total_duration = (self.end_time - self.start_time).total_seconds()

        logger.info(f"🎉 {self.filename}: Processing completed in {total_duration:.2f}s")
        logger.info(f"📊 Final statistics: {self.statistics.total_characters} chars, "
                   f"{self.statistics.total_chunks} chunks, {self.statistics.total_entities} entities, "
                   f"{self.statistics.total_references} references, {self.statistics.total_embeddings} embeddings")

        # Send immediate final WebSocket update
        await self._force_websocket_update(message_type="final_complete")

    async def fail_processing(self, error: str, details: Dict[str, Any] = None):
        """
        Mark the entire processing operation as failed.

        Args:
            error: Error message
            details: Additional details about the failure
        """
        if details is None:
            details = {}

        self.end_time = datetime.now(timezone.utc)
        self.is_failed = True
        self.status = "failed"
        self.current_message = f"Processing failed: {error}"
        self.current_step_name = ProcessingStep.FAILED

        # Add to processing errors
        self.statistics.processing_errors.append(f"FATAL: {error}")

        logger.error(f"💥 {self.filename}: Processing failed - {error}")

        # Send failure WebSocket update
        await self._send_websocket_update(message_type="operation_error", error=error, details=details)

    def get_progress_data(self) -> Dict[str, Any]:
        """
        Get comprehensive progress data for UI updates.

        Returns:
            Dictionary containing all progress information
        """
        # Calculate total duration
        if self.end_time:
            total_duration = (self.end_time - self.start_time).total_seconds()
        else:
            total_duration = (datetime.now(timezone.utc) - self.start_time).total_seconds()

        # Calculate ETA
        eta_seconds = None
        if self.overall_progress > 0 and not self.is_completed and not self.is_failed:
            estimated_total_time = total_duration * (100 / self.overall_progress)
            eta_seconds = estimated_total_time - total_duration

        # Prepare step data
        steps_data = {}
        for step, metrics in self.steps.items():
            step_data = {
                "status": metrics.status,
                "message": metrics.message,
                "start_time": metrics.start_time.isoformat() if metrics.start_time else None,
                "end_time": metrics.end_time.isoformat() if metrics.end_time else None,
                "duration": metrics.duration,
                "details": metrics.details,
                "error": metrics.error
            }
            steps_data[step.value] = step_data

        return {
            "operation_id": self.operation_id,
            "filename": self.filename,
            "status": self.status,
            "overall_progress": self.overall_progress,
            "current_step": self.current_step,
            "total_steps": self.total_steps,
            "current_step_name": self.current_step_name.value if hasattr(self.current_step_name, 'value') else str(self.current_step_name),
            "current_message": self.current_message,
            "is_completed": self.is_completed,
            "is_failed": self.is_failed,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "total_duration": total_duration,
            "eta_seconds": eta_seconds,
            "steps": steps_data,
            "statistics": asdict(self.statistics) if self.statistics else {}
        }

    async def _send_websocket_update(self, message_type: str = "progress_update", force: bool = False, **kwargs):
        """
        Send WebSocket update with current progress data and real-time optimization.

        Args:
            message_type: Type of WebSocket message
            force: Force immediate sending (bypass throttling)
            **kwargs: Additional data to include in the message
        """
        if not self.websocket_manager:
            logger.warning(f"⚠️ {self.filename}: No WebSocket manager available for updates")
            return

        try:
            current_time = time.time()

            # Real-time optimization: throttle frequent updates unless forced
            if not force and message_type not in self.force_update_steps:
                if current_time - self.last_update_time < self.min_update_interval:
                    self.pending_update = True
                    return

            progress_data = self.get_progress_data()
            progress_data.update(kwargs)

            message = {
                "type": message_type,
                "operation_id": self.operation_id,
                "data": progress_data,
                "timestamp": current_time,
                "real_time": True  # Flag for immediate processing
            }

            # Send immediately for better real-time performance
            await self.websocket_manager.send_to_operation(self.operation_id, message)

            self.last_update_time = current_time
            self.pending_update = False

            # Use debug for frequent updates, info for important ones
            log_level = logger.debug if message_type == "progress_update" else logger.info
            log_level(f"📡 {self.filename}: WebSocket update sent - {message_type} - Progress: {progress_data.get('overall_progress', 0)}%")

        except Exception as e:
            logger.warning(f"⚠️ {self.filename}: Failed to send WebSocket update: {e}")
            # Don't let WebSocket errors break the processing

    async def _force_websocket_update(self, message_type: str = "progress_update", **kwargs):
        """Force immediate WebSocket update bypassing throttling."""
        await self._send_websocket_update(message_type, force=True, **kwargs)

    async def _flush_pending_updates(self):
        """Send any pending updates that were throttled."""
        if self.pending_update:
            await self._send_websocket_update("progress_update", force=True)

    def get_step_summary(self) -> str:
        """
        Get a human-readable summary of all processing steps.

        Returns:
            Formatted string with step summary
        """
        summary_lines = [f"Processing Summary for {self.filename}:"]
        summary_lines.append("=" * 50)

        for step, metrics in self.steps.items():
            if metrics.status == "pending":
                continue

            status_icon = {
                "in_progress": "🔄",
                "completed": "✅",
                "failed": "❌"
            }.get(metrics.status, "⚪")

            duration_str = f" ({metrics.duration:.2f}s)" if metrics.duration else ""
            summary_lines.append(f"{status_icon} {step.value.replace('_', ' ').title()}{duration_str}")

            if metrics.message:
                summary_lines.append(f"   {metrics.message}")

            if metrics.error:
                summary_lines.append(f"   ❌ Error: {metrics.error}")

        # Add statistics
        summary_lines.append("")
        summary_lines.append("📊 Final Statistics:")
        summary_lines.append(f"   📄 Characters: {self.statistics.total_characters:,}")
        summary_lines.append(f"   🧩 Chunks: {self.statistics.total_chunks}")
        summary_lines.append(f"   🏷️ Entities: {self.statistics.total_entities}")
        summary_lines.append(f"   📚 References: {self.statistics.total_references}")
        summary_lines.append(f"   🔗 Embeddings: {self.statistics.total_embeddings}")

        if self.statistics.entities_by_chunk:
            summary_lines.append(f"   📊 Entities per chunk: {self.statistics.entities_by_chunk}")

        if self.statistics.processing_errors:
            summary_lines.append("")
            summary_lines.append("⚠️ Errors encountered:")
            for error in self.statistics.processing_errors:
                summary_lines.append(f"   • {error}")

        return "\n".join(summary_lines)
