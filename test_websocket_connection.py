#!/usr/bin/env python3
"""
Test WebSocket connection and progress tracking functionality.
"""

import asyncio
import json
import uuid
from pathlib import Path
from utils.websocket_manager import get_websocket_manager
from utils.enhanced_progress_tracker import EnhancedProgressTracker, ProcessingStep

async def test_websocket_progress():
    """Test WebSocket progress tracking functionality."""
    print("🧪 Testing WebSocket Progress Tracking...")
    
    # Get WebSocket manager
    websocket_manager = get_websocket_manager()
    
    # Create a test operation
    operation_id = str(uuid.uuid4())
    filename = "test_document.pdf"
    
    print(f"📋 Operation ID: {operation_id}")
    print(f"📄 Filename: {filename}")
    
    # Create enhanced progress tracker
    tracker = EnhancedProgressTracker(
        operation_id=operation_id,
        filename=filename,
        websocket_manager=websocket_manager
    )
    
    # Tracker is automatically initialized in constructor
    print("✅ Tracker created")
    
    # Simulate processing steps
    steps = [
        (ProcessingStep.TEXT_EXTRACTION, "Extracting text from document..."),
        (ProcessingStep.REFERENCE_EXTRACTION, "Extracting references..."),
        (ProcessingStep.CHUNK_PROCESSING, "Processing text chunks..."),
        (ProcessingStep.ENTITY_EXTRACTION, "Extracting entities..."),
        (ProcessingStep.EMBEDDING_GENERATION, "Generating embeddings..."),
        (ProcessingStep.METADATA_EXTRACTION, "Extracting metadata..."),
        (ProcessingStep.FINALIZATION, "Finalizing processing...")
    ]
    
    for i, (step, message) in enumerate(steps):
        print(f"🔄 Step {i+1}: {message}")
        
        # Start step
        await tracker.start_step(step, message)
        
        # Simulate some work
        await asyncio.sleep(2)
        
        # Update statistics
        if step == ProcessingStep.TEXT_EXTRACTION:
            tracker.update_statistics(total_characters=7792)
        elif step == ProcessingStep.CHUNK_PROCESSING:
            tracker.update_statistics(total_chunks=7)
        elif step == ProcessingStep.ENTITY_EXTRACTION:
            tracker.update_statistics(total_entities=92)
        elif step == ProcessingStep.EMBEDDING_GENERATION:
            tracker.update_statistics(total_embeddings=7)
        
        # Complete step
        await tracker.complete_step(step, f"Completed {message.lower()}")
        
        print(f"✅ Step {i+1} completed")
    
    # Complete processing
    result = {
        'success': True,
        'characters_extracted': 7792,
        'chunks_created': 7,
        'entities_extracted': 92,
        'embeddings_generated': 7
    }
    
    await tracker.complete_processing(result)
    print("🎉 Processing completed!")
    
    # Check final state
    progress_data = tracker.get_progress_data()
    print(f"📊 Final Progress: {progress_data['overall_progress']}%")
    print(f"📈 Statistics: {progress_data['statistics']}")

async def test_websocket_manager():
    """Test WebSocket manager functionality."""
    print("\n🧪 Testing WebSocket Manager...")
    
    websocket_manager = get_websocket_manager()
    
    # Test operation metadata
    operation_id = str(uuid.uuid4())
    metadata = {
        'filename': 'test.pdf',
        'start_time': '2025-07-23T16:00:00Z',
        'status': 'processing'
    }
    
    websocket_manager.store_operation_metadata(operation_id, metadata)
    retrieved_metadata = websocket_manager.get_operation_metadata(operation_id)

    # The stored metadata includes a created_at timestamp, so check the core fields
    if (retrieved_metadata and
        retrieved_metadata.get('filename') == metadata['filename'] and
        retrieved_metadata.get('status') == metadata['status']):
        print("✅ Metadata storage/retrieval working")
    else:
        print("❌ Metadata storage/retrieval failed")
    
    # Test active operations
    active_ops = websocket_manager.get_active_operations()
    print(f"📋 Active operations: {len(active_ops)}")
    
    print("✅ WebSocket manager tests completed")

async def main():
    """Run all tests."""
    print("🚀 Starting WebSocket and Progress Tracking Tests")
    print("=" * 50)
    
    try:
        await test_websocket_manager()
        await test_websocket_progress()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
