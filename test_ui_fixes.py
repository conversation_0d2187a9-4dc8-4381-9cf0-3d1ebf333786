#!/usr/bin/env python3
"""
Test the UI fixes for dashboard and document names.
"""

import requests

def test_dashboard_statistics():
    """Test the dashboard statistics API."""
    print("🔄 Testing Dashboard Statistics API...")
    try:
        response = requests.get('http://localhost:9753/api/fast/graph-stats')
        if response.status_code == 200:
            stats = response.json()
            print(f'✅ Dashboard Statistics Working:')
            print(f'   Total Documents: {stats.get("total_documents", 0)}')
            print(f'   Total Entities: {stats.get("total_entities", 0)}')
            print(f'   Total Relationships: {stats.get("total_relationships", 0)}')
            print(f'   Total References: {stats.get("total_references", 0)}')
            return True
        else:
            print(f'❌ Dashboard API Error: {response.status_code}')
            print(f'   Response: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Dashboard API Error: {e}')
        return False

def test_clean_document_names():
    """Test that document names are now clean (no UUID prefixes)."""
    print("\n🔄 Testing Clean Document Names...")
    try:
        response = requests.get('http://localhost:9753/api/documents?page=1&page_size=5')
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Documents API Working:')
            print(f'   Total: {data.get("total", 0)}')
            
            # Check document names
            for i, doc in enumerate(data.get('documents', [])[:5]):
                filename = doc.get('filename', 'Unknown')
                chunks = doc.get('chunks', 0)
                entities = doc.get('entities', 0)
                
                print(f'   {i+1}. "{filename}"')
                print(f'      Stats: {chunks} chunks, {entities} entities')
                
                # Check if name is clean (no UUID prefix)
                if '_' in filename and len(filename.split('_')[0]) >= 32:
                    print(f'      ⚠️ Still has UUID prefix')
                else:
                    print(f'      ✅ Clean name')
                    
            return True
        else:
            print(f'❌ Documents API Error: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Documents API Error: {e}')
        return False

def main():
    """Main test function."""
    print("🚀 TESTING UI FIXES")
    print("=" * 50)
    
    # Test dashboard statistics
    dashboard_ok = test_dashboard_statistics()
    
    # Test clean document names
    docs_ok = test_clean_document_names()
    
    # Summary
    print("\n📊 UI FIXES TEST SUMMARY:")
    print("=" * 30)
    print(f"   Dashboard Statistics: {'✅' if dashboard_ok else '❌'}")
    print(f"   Clean Document Names: {'✅' if docs_ok else '❌'}")
    
    if all([dashboard_ok, docs_ok]):
        print("\n🎉 ALL UI FIXES WORKING!")
        print("   Dashboard should now show correct document count")
        print("   Documents should show clean names without UUID prefixes")
        print("   References card should be properly sized")
    else:
        print("\n❌ SOME FIXES NEED MORE WORK")

if __name__ == "__main__":
    main()
