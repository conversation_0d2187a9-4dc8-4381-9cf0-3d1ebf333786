#!/usr/bin/env python3
"""
Hugging Face Transformers Client for Local Model Inference

This module provides a client for running Hugging Face Transformers models locally,
specifically optimized for medical models like II-Medical-8B-1706.
"""

import os
import logging
import torch
from typing import Dict, Any, List, Optional
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class TransformersClient:
    """Client for Hugging Face Transformers models."""
    
    def __init__(self, model_name: str = "Intelligent-Internet/II-Medical-8B-1706", device: Optional[str] = None):
        """
        Initialize the Transformers client.
        
        Args:
            model_name: Hugging Face model name
            device: Device to run on ('cuda', 'cpu', or None for auto-detect)
        """
        self.model_name = model_name
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.tokenizer = None
        self.loaded = False
        
        logger.info(f"Initialized TransformersClient with model: {self.model_name}")
        logger.info(f"Using device: {self.device}")
    
    def load_model(self):
        """Load the model and tokenizer."""
        if self.loaded:
            return
        
        try:
            logger.info(f"Loading model {self.model_name}...")
            
            # Import here to avoid dependency issues if transformers not installed
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            # Load tokenizer
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model with appropriate settings
            logger.info("Loading model...")
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == 'cuda' else torch.float32,
            }
            
            # Add device mapping for CUDA
            if self.device == 'cuda':
                model_kwargs["device_map"] = "auto"
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                **model_kwargs
            )
            
            # Move to device if not using device_map
            if self.device == 'cpu':
                self.model = self.model.to(self.device)
            
            self.model.eval()  # Set to evaluation mode
            self.loaded = True
            
            logger.info(f"✅ Successfully loaded {self.model_name}")
            logger.info(f"Model device: {next(self.model.parameters()).device}")
            
        except ImportError as e:
            logger.error(f"Transformers library not installed: {e}")
            raise ImportError("Please install transformers: pip install transformers torch")
        except Exception as e:
            logger.error(f"Error loading model {self.model_name}: {e}")
            raise
    
    def generate_completion(self, system_prompt: str, user_prompt: str, 
                          temperature: float = 0.3, max_tokens: int = 4000) -> str:
        """
        Generate a completion using the loaded model.
        
        Args:
            system_prompt: The system prompt
            user_prompt: The user prompt  
            temperature: Temperature for generation
            max_tokens: Maximum number of tokens to generate
            
        Returns:
            The generated text
        """
        if not self.loaded:
            self.load_model()
        
        try:
            # Combine prompts in a format suitable for the model
            combined_prompt = f"System: {system_prompt}\n\nUser: {user_prompt}\n\nAssistant:"
            
            # Tokenize input
            inputs = self.tokenizer(
                combined_prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048,  # Leave room for generation
                padding=True
            )
            
            # Move inputs to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    do_sample=True if temperature > 0 else False,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1,
                    top_p=0.9,
                    top_k=50
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],  # Only new tokens
                skip_special_tokens=True
            )
            
            return generated_text.strip()
            
        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            return f"Error: {e}"
    
    def generate_completion_with_messages(self, messages: List[Dict[str, str]], 
                                        temperature: float = 0.3, max_tokens: int = 4000) -> Dict[str, Any]:
        """
        Generate a completion using messages format (compatible with OpenAI-style APIs).
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            
        Returns:
            Response in OpenAI-compatible format
        """
        try:
            # Convert messages to system and user prompts
            system_prompt = ""
            user_prompt = ""
            
            for message in messages:
                if message["role"] == "system":
                    system_prompt = message["content"]
                elif message["role"] == "user":
                    user_prompt = message["content"]
            
            # Generate completion
            content = self.generate_completion(system_prompt, user_prompt, temperature, max_tokens)
            
            # Return in OpenAI-compatible format
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": content
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": len(system_prompt.split()) + len(user_prompt.split()),
                    "completion_tokens": len(content.split()),
                    "total_tokens": len(system_prompt.split()) + len(user_prompt.split()) + len(content.split())
                }
            }
            
        except Exception as e:
            logger.error(f"Error in generate_completion_with_messages: {e}")
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant", 
                            "content": f"Error: {e}"
                        },
                        "finish_reason": "error"
                    }
                ],
                "error": str(e)
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not self.loaded:
            return {"loaded": False}
        
        try:
            param_count = sum(p.numel() for p in self.model.parameters())
            return {
                "loaded": True,
                "model_name": self.model_name,
                "device": str(next(self.model.parameters()).device),
                "parameter_count": param_count,
                "parameter_count_b": f"{param_count / 1e9:.1f}B",
                "dtype": str(next(self.model.parameters()).dtype)
            }
        except Exception as e:
            return {"loaded": True, "error": str(e)}

def test_transformers_client():
    """Test the Transformers client."""
    try:
        logger.info("🧪 Testing II-Medical-8B-1706 model...")
        
        client = TransformersClient()
        
        # Test basic completion
        response = client.generate_completion(
            system_prompt="You are a helpful medical AI assistant specialized in entity extraction.",
            user_prompt="What is intestinal dysbiosis?",
            temperature=0.3,
            max_tokens=200
        )
        
        print(f"✅ II-Medical-8B Test Response: {response[:100]}...")
        
        # Test model info
        info = client.get_model_info()
        print(f"✅ Model Info: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ II-Medical-8B Test Failed: {e}")
        return False

if __name__ == "__main__":
    test_transformers_client()
