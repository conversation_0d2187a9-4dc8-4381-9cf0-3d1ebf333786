#!/usr/bin/env python3
"""
Debug document data to see what we're getting.
"""

import asyncio
import sys
from pathlib import Path
sys.path.insert(0, str(Path('.').absolute()))

async def debug_documents():
    """Debug document data."""
    try:
        from database.database_service import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        # Test the exact query from document service
        query = """
        MATCH (e:Document)
        RETURN e.id as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at
        ORDER BY e.processed_at DESC
        LIMIT 3
        """
        
        result = adapter.execute_cypher(query)
        print(f'Raw query result: {result}')
        
        if result and len(result) > 1:
            headers = result[0]
            print(f'Headers: {headers}')
            
            for i, row in enumerate(result[1][:3]):
                print(f'\nDocument {i+1}:')
                for j, header in enumerate(headers):
                    value = row[j] if j < len(row) else None
                    print(f'   {header}: "{value}" (type: {type(value)})')
        else:
            print('No documents found')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_documents())
