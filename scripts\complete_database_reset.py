#!/usr/bin/env python3
"""
Complete database reset script for Graphiti application.
This script will clean all data from:
- FalkorDB (all entities, facts, relationships)
- Redis Vector Search (all embeddings, chunks, metadata)
- Reference CSV files and master CSV
- Processed documents and uploads
- Operation states
"""

import os
import sys
import asyncio
import shutil
import logging
from pathlib import Path
from typing import Optional

# Add the parent directory to the path so we can import modules
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
import redis

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.config import (
    UPLOADS_DIR, PROCESSED_DIR, DOCUMENTS_DIR, REFERENCES_DIR,
    FALKORDB_GRAPH
)
from utils.redis_vector_search import get_redis_vector_search_client, REDIS_VECTOR_SEARCH_INDEX_NAME
from utils.operation_state_manager import OperationStateManager
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = get_logger(__name__)

class DatabaseResetManager:
    """Manager for complete database reset operations."""
    
    def __init__(self):
        self.falkordb_adapter: Optional[GraphitiFalkorDBAdapter] = None
        self.redis_client: Optional[redis.Redis] = None
        self.operation_manager: Optional[OperationStateManager] = None
    
    async def initialize_connections(self):
        """Initialize database connections."""
        try:
            # Initialize FalkorDB adapter
            self.falkordb_adapter = GraphitiFalkorDBAdapter(FALKORDB_GRAPH)
            logger.info("✅ FalkorDB adapter initialized")
            
            # Initialize Redis Vector Search client
            self.redis_client = get_redis_vector_search_client()
            logger.info("✅ Redis Vector Search client initialized")
            
            # Initialize operation state manager
            self.operation_manager = OperationStateManager()
            logger.info("✅ Operation state manager initialized")
            
        except Exception as e:
            logger.error(f"❌ Error initializing connections: {e}")
            raise
    
    def clear_falkordb(self):
        """Clear all data from FalkorDB."""
        logger.info("🧹 Clearing FalkorDB...")

        try:
            if not self.falkordb_adapter:
                raise ValueError("FalkorDB adapter not initialized")

            # Delete all nodes and relationships
            query = "MATCH (n) DETACH DELETE n"
            result = self.falkordb_adapter.execute_cypher(query)

            logger.info("✅ All FalkorDB nodes and relationships deleted")

            # Get count to verify
            count_query = "MATCH (n) RETURN count(n) as count"
            count_result = self.falkordb_adapter.execute_cypher(count_query)

            if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
                node_count = count_result[1][0][0] if count_result[1][0] else 0
                logger.info(f"✅ FalkorDB verification: {node_count} nodes remaining")
            else:
                logger.info("✅ FalkorDB verification: 0 nodes remaining")

        except Exception as e:
            logger.error(f"❌ Error clearing FalkorDB: {e}")
            raise
    
    def clear_redis_vector_search(self):
        """Clear all vector embeddings and metadata from Redis."""
        logger.info("🧹 Clearing Redis Vector Search...")
        
        try:
            if not self.redis_client:
                raise ValueError("Redis client not initialized")
            
            # Drop the vector index if it exists
            try:
                self.redis_client.ft(REDIS_VECTOR_SEARCH_INDEX_NAME).dropindex(delete_documents=True)
                logger.info(f"✅ Dropped vector index: {REDIS_VECTOR_SEARCH_INDEX_NAME}")
            except Exception as e:
                logger.warning(f"Vector index may not exist: {e}")
            
            # Clear all fact-related keys
            fact_keys = self.redis_client.keys("fact:*")
            if fact_keys:
                self.redis_client.delete(*fact_keys)
                logger.info(f"✅ Deleted {len(fact_keys)} fact keys from Redis")
            
            # Clear all embedding-related keys
            embedding_keys = self.redis_client.keys("embedding:*")
            if embedding_keys:
                self.redis_client.delete(*embedding_keys)
                logger.info(f"✅ Deleted {len(embedding_keys)} embedding keys from Redis")
            
            # Clear any chunk-related keys
            chunk_keys = self.redis_client.keys("chunk:*")
            if chunk_keys:
                self.redis_client.delete(*chunk_keys)
                logger.info(f"✅ Deleted {len(chunk_keys)} chunk keys from Redis")
            
            # Clear any metadata keys
            metadata_keys = self.redis_client.keys("metadata:*")
            if metadata_keys:
                self.redis_client.delete(*metadata_keys)
                logger.info(f"✅ Deleted {len(metadata_keys)} metadata keys from Redis")
            
            logger.info("✅ Redis Vector Search cleared")
            
        except Exception as e:
            logger.error(f"❌ Error clearing Redis Vector Search: {e}")
            raise
    
    def clear_file_directories(self):
        """Clear all file directories."""
        logger.info("🧹 Clearing file directories...")
        
        directories_to_clear = [
            (UPLOADS_DIR, "uploads"),
            (PROCESSED_DIR, "processed documents"),
            (DOCUMENTS_DIR, "documents"),
            (REFERENCES_DIR, "references")
        ]
        
        for directory, name in directories_to_clear:
            try:
                if directory.exists():
                    # Remove all contents but keep the directory
                    for item in directory.iterdir():
                        if item.is_file():
                            item.unlink()
                            logger.debug(f"Deleted file: {item}")
                        elif item.is_dir():
                            shutil.rmtree(item)
                            logger.debug(f"Deleted directory: {item}")
                    
                    logger.info(f"✅ Cleared {name} directory: {directory}")
                else:
                    logger.info(f"ℹ️ {name} directory does not exist: {directory}")
                    
            except Exception as e:
                logger.error(f"❌ Error clearing {name} directory: {e}")
                raise
    
    def clear_operation_states(self):
        """Clear operation state database."""
        logger.info("🧹 Clearing operation states...")
        
        try:
            if not self.operation_manager:
                raise ValueError("Operation state manager not initialized")
            
            # Clean up all operations (regardless of age)
            deleted_count = self.operation_manager.cleanup_old_operations(days_old=0)
            logger.info(f"✅ Cleared {deleted_count} operation states")
            
        except Exception as e:
            logger.error(f"❌ Error clearing operation states: {e}")
            raise
    
    def verify_cleanup(self):
        """Verify that all data has been cleared."""
        logger.info("🔍 Verifying cleanup...")

        verification_results = {
            "falkordb_nodes": 0,
            "redis_fact_keys": 0,
            "redis_embedding_keys": 0,
            "uploads_files": 0,
            "processed_files": 0,
            "documents_files": 0,
            "references_files": 0
        }

        try:
            # Check FalkorDB
            if self.falkordb_adapter:
                count_query = "MATCH (n) RETURN count(n) as count"
                count_result = self.falkordb_adapter.execute_cypher(count_query)
                if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
                    verification_results["falkordb_nodes"] = count_result[1][0][0] if count_result[1][0] else 0
            
            # Check Redis
            if self.redis_client:
                verification_results["redis_fact_keys"] = len(self.redis_client.keys("fact:*"))
                verification_results["redis_embedding_keys"] = len(self.redis_client.keys("embedding:*"))
            
            # Check file directories
            for directory, key in [
                (UPLOADS_DIR, "uploads_files"),
                (PROCESSED_DIR, "processed_files"),
                (DOCUMENTS_DIR, "documents_files"),
                (REFERENCES_DIR, "references_files")
            ]:
                if directory.exists():
                    verification_results[key] = len(list(directory.iterdir()))
            
            # Report results
            logger.info("📊 Cleanup verification results:")
            for key, value in verification_results.items():
                status = "✅" if value == 0 else "⚠️"
                logger.info(f"  {status} {key}: {value}")
            
            # Overall status
            all_clear = all(value == 0 for value in verification_results.values())
            if all_clear:
                logger.info("🎉 Complete cleanup verified - all databases and files cleared!")
            else:
                logger.warning("⚠️ Some data may remain - check the results above")
            
            return verification_results
            
        except Exception as e:
            logger.error(f"❌ Error during verification: {e}")
            raise

async def main():
    """Main function to perform complete database reset."""
    logger.info("🚀 Starting complete database reset...")
    
    # Confirmation prompt
    print("\n" + "="*60)
    print("⚠️  COMPLETE DATABASE RESET")
    print("="*60)
    print("This will permanently delete ALL data from:")
    print("• FalkorDB (all entities, facts, relationships)")
    print("• Redis Vector Search (all embeddings, chunks, metadata)")
    print("• All reference CSV files and master CSV")
    print("• All processed documents and uploads")
    print("• All operation states")
    print("="*60)
    
    response = input("\nAre you absolutely sure? Type 'RESET' to continue: ")
    if response != 'RESET':
        print("❌ Database reset cancelled.")
        return
    
    # Load environment variables
    load_dotenv()
    
    reset_manager = DatabaseResetManager()
    
    try:
        # Initialize connections
        await reset_manager.initialize_connections()
        
        # Perform cleanup operations
        reset_manager.clear_falkordb()
        reset_manager.clear_redis_vector_search()
        reset_manager.clear_file_directories()
        reset_manager.clear_operation_states()

        # Verify cleanup
        reset_manager.verify_cleanup()
        
        logger.info("🎉 Complete database reset finished successfully!")
        
    except Exception as e:
        logger.error(f"❌ Database reset failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
