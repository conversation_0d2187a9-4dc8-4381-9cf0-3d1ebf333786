#!/usr/bin/env python3
"""
Test database status to see what's happening with the latest document.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def test_database_status():
    """Test the database status for the latest document."""
    
    print("🔍 Testing database status...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # 1. Check latest document
        print("\n1. Checking latest document...")
        query = '''
        MATCH (ep:Episode)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN ep.uuid as uuid, ep.name as name, ep.processed_at as processed_at
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or not result[1]:
            print("❌ No documents found")
            return
            
        episode_id = result[1][0][0]
        episode_name = result[1][0][1]
        processed_at = result[1][0][2]
        
        print(f"✅ Latest episode: {episode_id}")
        print(f"   Name: {episode_name}")
        print(f"   Processed: {processed_at}")
        
        # 2. Check facts
        print("\n2. Checking facts...")
        fact_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN count(f) as fact_count
        '''
        fact_result = adapter.execute_cypher(fact_query)
        fact_count = fact_result[1][0][0] if fact_result and len(fact_result) > 1 else 0
        print(f"✅ Facts: {fact_count}")
        
        # 3. Check entities linked to facts
        print("\n3. Checking entities...")
        entity_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(e:Entity)
        RETURN count(DISTINCT e) as entity_count
        '''
        entity_result = adapter.execute_cypher(entity_query)
        entity_count = entity_result[1][0][0] if entity_result and len(entity_result) > 1 else 0
        print(f"✅ Entities linked to facts: {entity_count}")
        
        # 4. Check embeddings in Redis
        print("\n4. Checking embeddings in Redis...")
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            
            # Check if Redis is connected
            r.ping()
            print("✅ Redis connection successful")
            
            # Check for embeddings related to this episode
            keys = r.keys(f"*{episode_id}*")
            print(f"✅ Redis keys for episode: {len(keys)}")
            
            # Check for fact embeddings
            fact_keys = r.keys("fact:*")
            print(f"✅ Total fact embeddings in Redis: {len(fact_keys)}")
            
        except Exception as e:
            print(f"❌ Redis check failed: {e}")
        
        # 5. Summary
        print("\n📊 Summary:")
        print(f"   Episode: {episode_id}")
        print(f"   Facts: {fact_count}")
        print(f"   Entities: {entity_count}")
        print(f"   Redis keys: {len(keys) if 'keys' in locals() else 'N/A'}")
        
        if entity_count > 0:
            print("✅ Entity extraction working correctly!")
        else:
            print("❌ Entity extraction not working")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database_status())
