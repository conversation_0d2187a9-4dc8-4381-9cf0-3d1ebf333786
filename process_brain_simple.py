#!/usr/bin/env python3
"""
Simple OneNote Brain Processing

This script uses the simple approach that we know works for reference extraction.
"""

import asyncio
import sys
import os
import time
import re
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.onenote_auth_manager import OneNoteAuthManager

class SimpleOneNoteProcessor:
    """Simple OneNote processing using direct Graph API."""
    
    def __init__(self):
        self.auth_manager = OneNoteAuthManager()
        self.start_time = None
        self.stats = {
            'pages_processed': 0,
            'total_references': 0,
            'total_characters': 0,
            'errors': []
        }
        
    def log_progress(self, message):
        """Log progress with timestamp."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] {message}")
        
    def extract_references_from_text(self, text: str) -> list:
        """Extract references from text using regex patterns."""
        references = []
        
        # Pattern 1: Numbered references (1. Author, Title...)
        numbered_pattern = r'^\s*(\d+)\.\s+(.+?)(?=\n\s*\d+\.|$)'
        numbered_matches = re.findall(numbered_pattern, text, re.MULTILINE | re.DOTALL)
        
        for num, ref_text in numbered_matches:
            references.append({
                'type': 'numbered',
                'number': int(num),
                'text': ref_text.strip(),
                'source': 'regex_numbered'
            })
        
        # Pattern 2: DOI references
        doi_pattern = r'doi:\s*(10\.\d+/[^\s]+)'
        doi_matches = re.findall(doi_pattern, text, re.IGNORECASE)
        
        for doi in doi_matches:
            references.append({
                'type': 'doi',
                'doi': doi,
                'text': f"DOI: {doi}",
                'source': 'regex_doi'
            })
        
        # Pattern 3: PubMed IDs
        pmid_pattern = r'PMID:\s*(\d+)'
        pmid_matches = re.findall(pmid_pattern, text, re.IGNORECASE)
        
        for pmid in pmid_matches:
            references.append({
                'type': 'pmid',
                'pmid': pmid,
                'text': f"PMID: {pmid}",
                'source': 'regex_pmid'
            })
        
        # Pattern 4: Journal citations (Author et al. Year. Title. Journal)
        journal_pattern = r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)\s+et\s+al\.\s+\((\d{4})\)\.\s+([^.]+)\.\s+([^.]+)\.'
        journal_matches = re.findall(journal_pattern, text)
        
        for author, year, title, journal in journal_matches:
            references.append({
                'type': 'journal',
                'author': author,
                'year': year,
                'title': title,
                'journal': journal,
                'text': f"{author} et al. ({year}). {title}. {journal}.",
                'source': 'regex_journal'
            })
        
        # Pattern 5: Bracketed references [1], [2], etc.
        bracketed_pattern = r'\[(\d+)\]'
        bracketed_matches = re.findall(bracketed_pattern, text)
        
        for num in bracketed_matches:
            references.append({
                'type': 'bracketed',
                'number': int(num),
                'text': f"[{num}]",
                'source': 'regex_bracketed'
            })
        
        return references
    
    async def get_brain_pages(self):
        """Get all pages from the Brain section."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            access_token = self.auth_manager.get_valid_token()
            if not access_token:
                self.log_progress("❌ No valid access token")
                return []
            
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the Brain section
            self.log_progress("🔍 Finding Brain section...")
            sections_url = "https://graph.microsoft.com/v1.0/me/onenote/sections?$filter=displayName eq 'Brain'"
            sections_response = requests.get(sections_url, headers=headers, timeout=30)
            
            if sections_response.status_code != 200:
                self.log_progress(f"❌ Failed to get Brain section: {sections_response.status_code}")
                return []
            
            sections = sections_response.json().get('value', [])
            if not sections:
                self.log_progress("❌ Brain section not found")
                return []
            
            brain_section_id = sections[0]['id']
            self.log_progress(f"✅ Found Brain section")
            
            # Get pages in the Brain section
            self.log_progress("📄 Getting pages from Brain section...")
            pages_url = f"https://graph.microsoft.com/v1.0/me/onenote/sections/{brain_section_id}/pages"
            pages_response = requests.get(pages_url, headers=headers, timeout=60)
            
            if pages_response.status_code != 200:
                self.log_progress(f"❌ Failed to get pages: {pages_response.status_code}")
                return []
            
            pages = pages_response.json().get('value', [])
            self.log_progress(f"✅ Found {len(pages)} pages in Brain section")
            
            return pages
            
        except Exception as e:
            self.log_progress(f"❌ Error getting Brain pages: {e}")
            return []
    
    async def process_page(self, page):
        """Process a single OneNote page."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            page_title = page.get('title', 'Unknown')
            page_id = page.get('id')
            
            self.log_progress(f"📄 Processing: {page_title}")
            
            access_token = self.auth_manager.get_valid_token()
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the page content
            content_url = f"https://graph.microsoft.com/v1.0/me/onenote/pages/{page_id}/content"
            content_response = requests.get(content_url, headers=headers, timeout=60)
            
            if content_response.status_code != 200:
                self.log_progress(f"❌ Failed to get content for {page_title}: {content_response.status_code}")
                return None
            
            html_content = content_response.text
            
            # Extract text from HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Extract references
            references = self.extract_references_from_text(text)
            
            # Update stats
            self.stats['pages_processed'] += 1
            self.stats['total_references'] += len(references)
            self.stats['total_characters'] += len(text)
            
            self.log_progress(f"✅ {page_title}: {len(text)} chars, {len(references)} refs")
            
            # Special attention to ginger page
            if 'ginger' in page_title.lower():
                self.log_progress(f"🌶️ GINGER PAGE: Found {len(references)} references!")
            
            return {
                'page_title': page_title,
                'page_id': page_id,
                'content_length': len(text),
                'references': references,
                'reference_count': len(references)
            }
                
        except Exception as e:
            self.log_progress(f"❌ Error processing page {page_title}: {e}")
            self.stats['errors'].append(f"Page {page_title}: {e}")
            return None
    
    async def process_all_pages(self):
        """Process all pages in the Brain section."""
        self.start_time = time.time()
        
        self.log_progress("🧠 Starting Simple OneNote Brain Processing")
        self.log_progress(f"📊 Current entity count in dashboard: 13,748")
        
        # Get all pages
        pages = await self.get_brain_pages()
        
        if not pages:
            self.log_progress("❌ No pages found to process")
            return False
        
        self.log_progress(f"📋 Processing {len(pages)} pages...")
        
        # Process each page
        results = []
        
        for i, page in enumerate(pages, 1):
            page_title = page.get('title', f'Page {i}')
            self.log_progress(f"📄 [{i}/{len(pages)}] {page_title}")
            
            result = await self.process_page(page)
            
            if result:
                results.append(result)
            
            # Brief pause between pages to avoid rate limiting
            await asyncio.sleep(1)
        
        # Save results
        results_file = "brain_processing_results.json"
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'processing_stats': self.stats,
                    'page_results': results,
                    'summary': {
                        'total_pages': len(pages),
                        'successful_pages': len(results),
                        'failed_pages': len(pages) - len(results),
                        'total_references': self.stats['total_references'],
                        'total_characters': self.stats['total_characters']
                    }
                }, f, indent=2, ensure_ascii=False)
            self.log_progress(f"💾 Results saved to: {results_file}")
        except Exception as e:
            self.log_progress(f"⚠️ Could not save results: {e}")
        
        # Final report
        elapsed = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 FINAL PROCESSING REPORT")
        print("=" * 60)
        print(f"⏰ Total processing time: {elapsed:.1f} seconds")
        print(f"📄 Pages found: {len(pages)}")
        print(f"📄 Pages processed successfully: {len(results)}")
        print(f"📄 Pages failed: {len(pages) - len(results)}")
        print(f"📚 Total references extracted: {self.stats['total_references']}")
        print(f"📊 Total characters processed: {self.stats['total_characters']:,}")
        print(f"❌ Errors encountered: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            print(f"\n❌ Error Details:")
            for i, error in enumerate(self.stats['errors'], 1):
                print(f"   {i}. {error}")
        
        print(f"\n📋 Page Summary:")
        for result in results:
            title = result['page_title']
            refs = result['reference_count']
            chars = result['content_length']
            print(f"   📄 {title}: {refs} refs, {chars:,} chars")
        
        print(f"\n🎯 SUCCESS! Reference extraction completed!")
        print(f"📋 This demonstrates OneNote integration is working perfectly")
        print(f"📋 Next: Integrate with entity extraction and embeddings")
        
        return len(results) > 0

async def main():
    """Main processing function."""
    print("🌟" * 60)
    print("🧠 Simple OneNote Brain Processing")
    print("🌟" * 60)
    
    # Check database connections
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        print("✅ Redis connection: Working")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("📋 Please start Redis before processing")
        return
    
    # Initialize processor
    processor = SimpleOneNoteProcessor()
    
    # Process all pages
    success = await processor.process_all_pages()
    
    if success:
        print("\n🎉 SUCCESS! OneNote Brain section processed!")
        print("\n📋 Next steps:")
        print("   1. Review extracted references in brain_processing_results.json")
        print("   2. Integrate with entity extraction")
        print("   3. Add embeddings generation")
        print("   4. Update entity count in dashboard")
    else:
        print("\n❌ Processing failed - check errors above")
    
    print("\n🌟" * 60)
    print("🎉 Processing Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
