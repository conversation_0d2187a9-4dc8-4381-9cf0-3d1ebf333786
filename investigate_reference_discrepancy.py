#!/usr/bin/env python3
"""
Investigate the reference count discrepancy between batch processing and UI
"""

import os
import csv
import json
from pathlib import Path
import requests

def investigate_discrepancy():
    """Investigate why UI shows 396 but batch processing found 1,043 references"""
    
    print("🔍 INVESTIGATING REFERENCE COUNT DISCREPANCY")
    print("=" * 60)
    
    # 1. Check what the API currently returns
    print("1️⃣ CHECKING API RESPONSE:")
    try:
        response = requests.get("http://localhost:9753/api/fast/graph-stats", timeout=10)
        if response.status_code == 200:
            api_data = response.json()
            api_refs = api_data.get('total_references', 0)
            print(f"   📱 API returns: {api_refs} references")
        else:
            print(f"   ❌ API error: {response.status_code}")
            api_refs = 0
    except Exception as e:
        print(f"   ❌ API error: {e}")
        api_refs = 0
    
    # 2. Check CSV files in references directory
    print(f"\n2️⃣ CHECKING CSV FILES:")
    ref_dir = Path("references")
    
    if not ref_dir.exists():
        print("   ❌ References directory doesn't exist")
        return
    
    csv_files = list(ref_dir.glob("*.csv"))
    print(f"   📁 Found {len(csv_files)} CSV files")
    
    total_csv_refs = 0
    aggressive_refs = 0
    traditional_refs = 0
    file_details = []
    
    for csv_file in csv_files:
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
                ref_count = len(rows) - 1  # Subtract header
                
                if ref_count > 0:
                    total_csv_refs += ref_count
                    
                    file_info = {
                        'name': csv_file.name,
                        'count': ref_count,
                        'type': 'aggressive' if 'aggressive' in csv_file.name else 'traditional'
                    }
                    file_details.append(file_info)
                    
                    if 'aggressive' in csv_file.name:
                        aggressive_refs += ref_count
                    else:
                        traditional_refs += ref_count
                        
        except Exception as e:
            print(f"   ❌ Error reading {csv_file.name}: {e}")
    
    # Sort by count for display
    file_details.sort(key=lambda x: x['count'], reverse=True)
    
    print(f"\n   📊 CSV FILE BREAKDOWN:")
    for file_info in file_details[:10]:  # Show top 10
        file_type = "🚀" if file_info['type'] == 'aggressive' else "📄"
        print(f"      {file_type} {file_info['name'][:50]}...: {file_info['count']} refs")
    
    if len(file_details) > 10:
        print(f"      ... and {len(file_details) - 10} more files")
    
    print(f"\n   📈 CSV TOTALS:")
    print(f"      🚀 Aggressive files: {aggressive_refs} references")
    print(f"      📄 Traditional files: {traditional_refs} references")
    print(f"      📚 Total CSV references: {total_csv_refs} references")
    
    # 3. Check batch processing results
    print(f"\n3️⃣ CHECKING BATCH PROCESSING RESULTS:")
    batch_file = "graphiti_reprocessing_final_20250721_085426.json"
    
    if os.path.exists(batch_file):
        with open(batch_file, 'r') as f:
            batch_data = json.load(f)
        
        batch_refs = batch_data.get('total_references_found', 0)
        batch_docs = batch_data.get('successful_count', 0)
        print(f"   🔄 Batch processing found: {batch_refs} references from {batch_docs} documents")
    else:
        print(f"   ❌ Batch results file not found")
        batch_refs = 0
    
    # 4. Analysis
    print(f"\n4️⃣ DISCREPANCY ANALYSIS:")
    print(f"   📱 API/UI shows: {api_refs} references")
    print(f"   📁 CSV files contain: {total_csv_refs} references")
    print(f"   🔄 Batch processing found: {batch_refs} references")
    
    print(f"\n🔍 POSSIBLE EXPLANATIONS:")
    
    if total_csv_refs == api_refs:
        print("   ✅ CSV files match API - this is expected")
        if batch_refs > total_csv_refs:
            print("   ❓ Batch processing found more references than saved to CSV")
            print("   💡 Possible: Some batch results weren't saved to CSV files")
    
    elif total_csv_refs > api_refs:
        print("   ❓ CSV files have more references than API reports")
        print("   💡 Possible: API is not reading all CSV files")
    
    elif api_refs > total_csv_refs:
        print("   ❓ API reports more references than in CSV files")
        print("   💡 Possible: API is counting from another source")
    
    if batch_refs > total_csv_refs:
        missing = batch_refs - total_csv_refs
        print(f"\n⚠️  MISSING REFERENCES: {missing} references found in batch but not in CSV")
        print("   💡 This suggests the batch processing results weren't fully saved")
    
    # 5. Recommendations
    print(f"\n5️⃣ RECOMMENDATIONS:")
    
    if batch_refs > total_csv_refs:
        print("   🔧 Re-run aggressive reference extraction to ensure all results are saved")
        print("   📁 Check if CSV files are being created in the correct location")
        print("   🔄 Verify the batch processing save mechanism")
    
    if total_csv_refs != api_refs:
        print("   🔧 Check the API reference counting logic")
        print("   📂 Ensure API is reading from the correct directory")
    
    print(f"\n✅ Investigation complete!")

if __name__ == "__main__":
    investigate_discrepancy()
