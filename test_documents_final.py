#!/usr/bin/env python3
"""
Final test of the documents API to confirm it's working.
"""

import requests

def test_documents_api():
    """Test the documents API with correct parameters."""
    try:
        response = requests.get('http://localhost:9753/api/documents?page=1&page_size=10')
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Documents API Working:')
            print(f'   Total: {data.get("total", 0)}')
            print(f'   Documents on page: {len(data.get("documents", []))}')
            print(f'   Pagination: {data.get("pagination", {})}')
            
            # Show first few document names
            for i, doc in enumerate(data.get('documents', [])[:5]):
                name = doc.get('filename', 'Unknown')
                # Clean up the name by removing UUID prefix
                if '_' in name:
                    clean_name = name.split('_', 1)[1] if len(name.split('_', 1)) > 1 else name
                else:
                    clean_name = name
                print(f'   {i+1}. {clean_name}')
                
            return True
        else:
            print(f'❌ API Error: {response.status_code} - {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    test_documents_api()
