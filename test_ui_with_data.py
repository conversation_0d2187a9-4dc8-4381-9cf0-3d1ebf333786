#!/usr/bin/env python3
"""
Test UI with actual FalkorDB data from the graphiti_knowledge graph.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger
import falkordb

logger = get_logger(__name__)

async def test_with_correct_graph():
    """Test with the correct graphiti_knowledge graph."""
    try:
        logger.info("🔄 Testing with graphiti_knowledge graph...")
        
        # Connect directly to FalkorDB
        client = falkordb.FalkorDB(host='localhost', port=6379)
        graph = client.select_graph('graphiti_knowledge')
        
        # Test basic queries
        result = graph.query("MATCH (n) RETURN count(n) as total_nodes LIMIT 1")
        total_nodes = result.result_set[0][0] if result.result_set else 0
        
        logger.info(f"✅ Total nodes in graphiti_knowledge: {total_nodes}")
        
        # Test entity query
        entity_result = graph.query("MATCH (e:Entity) RETURN count(e) as entity_count LIMIT 1")
        entity_count = entity_result.result_set[0][0] if entity_result.result_set else 0
        
        logger.info(f"✅ Total entities: {entity_count}")
        
        # Test document query
        doc_result = graph.query("MATCH (d:Document) RETURN count(d) as doc_count LIMIT 1")
        doc_count = doc_result.result_set[0][0] if doc_result.result_set else 0
        
        logger.info(f"✅ Total documents: {doc_count}")
        
        # Test chunk query
        chunk_result = graph.query("MATCH (c:Chunk) RETURN count(c) as chunk_count LIMIT 1")
        chunk_count = chunk_result.result_set[0][0] if chunk_result.result_set else 0
        
        logger.info(f"✅ Total chunks: {chunk_count}")
        
        # Test reference query
        ref_result = graph.query("MATCH (r:Reference) RETURN count(r) as ref_count LIMIT 1")
        ref_count = ref_result.result_set[0][0] if ref_result.result_set else 0
        
        logger.info(f"✅ Total references: {ref_count}")
        
        # Test relationship query
        rel_result = graph.query("MATCH ()-[r]->() RETURN count(r) as rel_count LIMIT 1")
        rel_count = rel_result.result_set[0][0] if rel_result.result_set else 0
        
        logger.info(f"✅ Total relationships: {rel_count}")
        
        # Sample entities
        if entity_count > 0:
            sample_result = graph.query("MATCH (e:Entity) RETURN e.name, e.type LIMIT 5")
            logger.info(f"✅ Sample entities:")
            for row in sample_result.result_set:
                logger.info(f"   - {row[0]} ({row[1]})")
        
        # Sample documents
        if doc_count > 0:
            doc_sample_result = graph.query("MATCH (d:Document) RETURN d.name LIMIT 3")
            logger.info(f"✅ Sample documents:")
            for row in doc_sample_result.result_set:
                logger.info(f"   - {row[0]}")
        
        return {
            'total_nodes': total_nodes,
            'entities': entity_count,
            'documents': doc_count,
            'chunks': chunk_count,
            'references': ref_count,
            'relationships': rel_count
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing graphiti_knowledge graph: {e}")
        return None

def update_adapter_to_use_correct_graph():
    """Update the adapter to use the correct graph name."""
    try:
        logger.info("🔄 Updating adapter configuration...")
        
        # Clear any cached adapter
        import database.falkordb_adapter
        database.falkordb_adapter._falkordb_adapter = None
        
        # Force the correct graph name
        from database.falkordb_adapter import FalkorDBAdapter
        
        # Create new adapter with correct graph name
        adapter = FalkorDBAdapter(graph_name="graphiti_knowledge")
        database.falkordb_adapter._falkordb_adapter = adapter
        
        logger.info("✅ Adapter updated to use graphiti_knowledge graph")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating adapter: {e}")
        return False

async def test_ui_endpoints_with_data():
    """Test UI endpoints with the correct graph data."""
    try:
        logger.info("🔄 Testing UI endpoints with correct graph...")
        
        # Update adapter first
        update_adapter_to_use_correct_graph()
        
        # Import and test entity routes
        from routes.entity_routes import get_graph_statistics, get_all_entities
        
        # Test graph statistics
        stats = await get_graph_statistics()
        logger.info(f"✅ Graph statistics:")
        logger.info(f"   Documents: {stats.get('total_documents', 0)}")
        logger.info(f"   Entities: {stats.get('total_entities', 0)}")
        logger.info(f"   Relationships: {stats.get('total_relationships', 0)}")
        logger.info(f"   References: {stats.get('total_references', 0)}")
        
        # Test entity listing
        entities_result = await get_all_entities(limit=10, offset=0)
        logger.info(f"✅ Entity listing:")
        logger.info(f"   Retrieved {len(entities_result.get('entities', []))} entities")
        logger.info(f"   Total count: {entities_result.get('count', 0)}")
        
        # Show sample entities
        for entity in entities_result.get('entities', [])[:3]:
            logger.info(f"   - {entity.get('name', 'Unknown')} ({entity.get('type', 'Unknown')})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing UI endpoints: {e}")
        return False

async def main():
    """Main function."""
    logger.info("🚀 TESTING UI WITH ACTUAL FALKORDB DATA")
    logger.info("=" * 50)
    
    # Test direct graph connection
    graph_stats = await test_with_correct_graph()
    
    if graph_stats and graph_stats['total_nodes'] > 0:
        logger.info(f"\n✅ Found data in graphiti_knowledge graph!")
        
        # Test UI endpoints
        ui_ok = await test_ui_endpoints_with_data()
        
        if ui_ok:
            logger.info(f"\n🎉 UI IS READY WITH REAL DATA!")
            logger.info(f"   The UI should now show:")
            logger.info(f"   - {graph_stats['documents']} documents")
            logger.info(f"   - {graph_stats['entities']} entities") 
            logger.info(f"   - {graph_stats['relationships']} relationships")
            logger.info(f"   - {graph_stats['references']} references")
        else:
            logger.error(f"\n❌ UI endpoints need fixing")
    else:
        logger.warning(f"\n⚠️ No data found in graphiti_knowledge graph")
        logger.warning(f"   Run the batch processing first to populate the graph")

if __name__ == "__main__":
    asyncio.run(main())
