# 🌟 Graphiti Knowledge Graph Platform

> **🎉 LATEST UPDATE (January 2025):** UI Consistency Complete! All UI pages now have standardized data labels, consistent loading patterns, and enhanced user experience. AI-powered reference system displaying 4,430 references with quality scoring. System running with 31,982+ entities, 82,079+ relationships, and comprehensive multi-format document support.

## 🎯 Overview

Graphiti is a comprehensive knowledge graph platform that processes scientific documents into a structured, queryable graph of entities and relationships. Built on FalkorDB with Redis vector search, it specializes in health, nutrition, and medical research with advanced OneNote processing capabilities.

## ✨ Key Features

### 📄 **Document Processing**
- **PDF Processing**: Complete text extraction with chunking and metadata
- **OneNote Support**: ✅ **FULLY INTEGRATED** - Page-by-page processing through main pipeline
- **Word Documents**: Full .docx processing with Mistral OCR
- **PowerPoint**: Presentation processing with reference extraction
- **Text Files**: Markdown and plain text support
- **Unified Pipeline**: All formats process through the same robust workflow

### 🧠 **Knowledge Graph**
- **FalkorDB Backend**: High-performance graph database
- **31,982+ Entities**: Comprehensive entity extraction and classification
- **82,079+ Relationships**: Sophisticated relationship mapping
- **Entity Types**: Application, Disease, Food, Herb, Location, Medication, Nutrient, Organization, Person, Process, Research, Symptom, Treatment

### 📚 **Reference Management**
- **4,430+ AI-Powered References**: Intelligent extraction with quality scoring
- **Academic Citations**: DOI, PubMed, journal information
- **Quality Metrics**: AI-enhanced quality scoring and confidence ratings
- **Reference Enhancement**: Bibliographic enrichment and deduplication
- **CSV Export**: Complete reference database export

### 🔍 **Search & Q&A**
- **Vector Search**: Redis-powered semantic search with Ollama embeddings
- **Natural Language Q&A**: OpenRouter with meta-llama/llama-4-maverick
- **Entity Filtering**: Advanced filtering by type, confidence, relationships
- **Citation Support**: Proper academic citations in responses

### 🎨 **Modern Interface**
- **Bootstrap 5 UI**: Professional, responsive design with consistent styling
- **Real-time Updates**: Live progress tracking and status updates
- **Consistent UX**: Standardized data labels, loading patterns, and error handling across all pages
- **Interactive Visualizations**: Knowledge graph visualization
- **9 Core Pages**: Dashboard, Upload, Documents, Entities, References, Search, Q&A, Knowledge Graph, Settings

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Docker (for FalkorDB and Redis)
- Ollama (for embeddings)
- OpenRouter API key (for LLM)

### Installation

1. **Clone Repository**
```bash
git clone <repository-url>
cd Graphiti
```

2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

3. **Start Services**
```bash
# Start FalkorDB and Redis
docker-compose -f docker-compose.unified.yml up -d

# Start Ollama (if not running)
ollama serve
```

4. **Configure Environment**
```bash
# Copy and edit configuration
cp .env.example .env
# Add your OpenRouter API key and other settings
```

5. **Run Application**
```bash
python app.py
```

6. **Access Interface**
- Web UI: http://localhost:5000
- API Docs: http://localhost:5000/docs

## 📊 System Architecture

### Core Components
- **FastAPI Backend**: High-performance API with async support
- **FalkorDB**: Graph database for entities and relationships
- **Redis**: Vector search and caching
- **Ollama**: Local embeddings with snowflake-arctic-embed2
- **OpenRouter**: LLM services with meta-llama/llama-4-maverick

### Processing Pipeline
1. **Document Upload**: Multi-format support (PDF, OneNote, Word, PowerPoint, Text)
2. **Content Extraction**: OCR, text extraction, chunking (1200 chars, 0 overlap)
3. **Entity Extraction**: LLM-powered entity identification (meta-llama/llama-4-maverick)
4. **Relationship Mapping**: Sophisticated relationship extraction
5. **Reference Processing**: Academic reference extraction with Mistral OCR
6. **Embedding Generation**: Vector embeddings with snowflake-arctic-embed2
7. **Graph Storage**: Structured storage in FalkorDB with Redis vector search

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
REDIS_HOST=localhost
REDIS_PORT=6380

# LLM Configuration
OPENROUTER_API_KEY=your_key_here
OPENROUTER_MODEL=meta-llama/llama-4-maverick

# Ollama Configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_EMBEDDING_MODEL=snowflake-arctic-embed2

# Processing Configuration
DEFAULT_CHUNK_SIZE=1200
DEFAULT_OVERLAP=0
MAX_ENTITIES_PER_CHUNK=50
```

### Model Configuration
- **Entity Extraction**: meta-llama/llama-4-maverick via OpenRouter
- **Embeddings**: snowflake-arctic-embed2 via Ollama
- **Q&A**: meta-llama/llama-4-maverick via OpenRouter
- **OCR**: Mistral AI OCR for complex documents

## 📁 Project Structure

```
Graphiti/
├── app.py                 # Main FastAPI application
├── routes/               # API route handlers
├── processors/           # Document processors
├── services/            # Core business logic
├── database/            # Database adapters
├── utils/               # Utility functions
├── templates/           # Web UI templates
├── static/              # CSS, JS, assets
├── mcp_server/          # MCP server implementation
├── uploads/             # Document uploads
├── references/          # Reference database
└── docs/                # Documentation
```

## 🔌 MCP Server

The system includes a Model Context Protocol (MCP) server for integration with AI assistants:

```bash
# Start MCP server
cd mcp_server
python graphiti_mcp_server.py --transport stdio
```

### MCP Tools
- `process_document`: Process documents through the pipeline
- `get_graph_stats`: Retrieve knowledge graph statistics

## 📈 Performance

- **Entity Processing**: ~50 entities per document
- **Reference Extraction**: 4,430+ AI-powered references with quality scoring
- **Search Performance**: Sub-second semantic search
- **API Response**: 0.01-0.1s average response time
- **Concurrent Processing**: Multi-threaded document processing
- **UI Consistency**: Standardized loading patterns and error handling

## 🛠️ Development

### Running Tests
```bash
# Core functionality tests
python -m pytest tests/

# API tests
python tests/test_api.py
```

### Adding New Processors
1. Create processor in `processors/`
2. Implement base processor interface
3. Add to document processing service
4. Update route handlers

## 📚 Documentation

- **API Documentation**: Available at `/docs` when running
- **Architecture Guide**: `docs/ARCHITECTURE.md`
- **OneNote Support**: `docs/ONENOTE_SUPPORT.md`
- **Deployment Guide**: `DEPLOYMENT_GUIDE.md`

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
- Check existing documentation
- Review API documentation at `/docs`
- Create GitHub issue with detailed description

---

**Built with ❤️ for knowledge discovery and research acceleration**
