{"pages_processed": 0, "entities_added": 0, "references_added": 0, "chunks_processed": 0, "total_characters": 0, "errors": ["Authentication setup failed: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 177, in <module>\n    asyncio.run(main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 195, in run\n    return runner.run(main)\n           ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 691, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 131, in main\n    print(\"\\U0001f31f\" * 60)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode characters in position 0-59: character maps to <undefined>\n", "Authentication setup failed: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 177, in <module>\n    asyncio.run(main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 195, in run\n    return runner.run(main)\n           ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 691, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 131, in main\n    print(\"\\U0001f31f\" * 60)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode characters in position 0-59: character maps to <undefined>\n", "Authentication setup failed: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 177, in <module>\n    asyncio.run(main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 195, in run\n    return runner.run(main)\n           ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 691, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Graphiti\\graphiti\\setup_onenote_token.py\", line 131, in main\n    print(\"\\U0001f31f\" * 60)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode characters in position 0-59: character maps to <undefined>\n", "Failed to get Brain content after all retries", "No content found to process"]}