#!/usr/bin/env python3
"""
Test all APIs to check document statistics and dashboard data.
"""

import requests
import json

def test_documents_with_stats():
    """Test documents API with chunk and entity statistics."""
    print("🔄 Testing Documents API with Statistics...")
    try:
        response = requests.get('http://localhost:9753/api/documents?page=1&page_size=5')
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Documents API Working:')
            print(f'   Total: {data.get("total", 0)}')
            print(f'   Documents on page: {len(data.get("documents", []))}')
            
            # Show document statistics
            for i, doc in enumerate(data.get('documents', [])[:3]):
                name = doc.get('filename', 'Unknown')
                chunks = doc.get('chunks', 0)
                entities = doc.get('entities', 0)
                references = doc.get('references', 0)
                
                # Clean up the name
                if '_' in name:
                    clean_name = name.split('_', 1)[1] if len(name.split('_', 1)) > 1 else name
                else:
                    clean_name = name
                    
                print(f'   {i+1}. {clean_name}')
                print(f'      Chunks: {chunks}, Entities: {entities}, References: {references}')
                
            return True
        else:
            print(f'❌ Documents API Error: {response.status_code} - {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Documents API Error: {e}')
        return False

def test_dashboard_statistics():
    """Test dashboard statistics API."""
    print("\n🔄 Testing Dashboard Statistics API...")
    try:
        response = requests.get('http://localhost:9753/api/entities/statistics')
        if response.status_code == 200:
            stats = response.json()
            print(f'✅ Statistics API Working:')
            print(f'   Total Documents: {stats.get("total_documents", 0)}')
            print(f'   Total Entities: {stats.get("total_entities", 0)}')
            print(f'   Total Relationships: {stats.get("total_relationships", 0)}')
            print(f'   Total References: {stats.get("total_references", 0)}')
            print(f'   Total Chunks: {stats.get("total_chunks", 0)}')
            
            # Check entity counts by type
            entity_counts = stats.get("entity_counts_by_type", {})
            if hasattr(entity_counts, 'counts'):
                print(f'   Entity Types: {len(entity_counts.counts)}')
                for count in entity_counts.counts[:5]:
                    print(f'     - {count.type}: {count.count}')
            
            return True
        else:
            print(f'❌ Statistics API Error: {response.status_code}')
            print(f'   Response: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Statistics API Error: {e}')
        return False

def test_entity_api():
    """Test entity API to verify it's working."""
    print("\n🔄 Testing Entity API...")
    try:
        response = requests.get('http://localhost:9753/api/entities?limit=5')
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Entity API Working:')
            print(f'   Total Entities: {data.get("count", 0)}')
            print(f'   Sample Entities:')
            
            for entity in data.get('entities', [])[:3]:
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                mentions = entity.get('mention_count', 0)
                print(f'     - {name} ({entity_type}) - {mentions} mentions')
                
            return True
        else:
            print(f'❌ Entity API Error: {response.status_code} - {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Entity API Error: {e}')
        return False

def main():
    """Main test function."""
    print("🚀 TESTING ALL APIS")
    print("=" * 50)
    
    # Test documents with statistics
    docs_ok = test_documents_with_stats()
    
    # Test dashboard statistics
    stats_ok = test_dashboard_statistics()
    
    # Test entity API
    entities_ok = test_entity_api()
    
    # Summary
    print("\n📊 API TEST SUMMARY:")
    print("=" * 30)
    print(f"   Documents API: {'✅' if docs_ok else '❌'}")
    print(f"   Statistics API: {'✅' if stats_ok else '❌'}")
    print(f"   Entity API: {'✅' if entities_ok else '❌'}")
    
    if all([docs_ok, stats_ok, entities_ok]):
        print("\n🎉 ALL APIS WORKING!")
    else:
        print("\n❌ SOME APIS NEED FIXING")

if __name__ == "__main__":
    main()
