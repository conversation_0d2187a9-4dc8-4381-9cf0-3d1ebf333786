#!/usr/bin/env python3
"""
Test the entity extraction fix in the unified pipeline
"""

import asyncio
from pathlib import Path

async def test_entity_extraction_fix():
    """Test the entity extraction fix."""
    
    print("🧪 TESTING ENTITY EXTRACTION FIX")
    print("=" * 50)
    
    try:
        # Test 1: Import the unified pipeline
        print("1️⃣ Testing unified pipeline import...")
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print(f"   ✅ Unified pipeline imported successfully")
        
        # Test 2: Test entity extraction service directly
        print("\n2️⃣ Testing entity extraction service...")
        from services.entity_extraction_service import extract_entities_from_text
        
        test_text = """
        This document discusses Lactate and Pyruvate levels in patients.
        L-Carnitine and Coenzyme Q10 are important nutrients for mitochondrial function.
        Vitamin B6 deficiency can affect Tryptophan metabolism and Serotonin production.
        Glutathione is a crucial antioxidant for cellular protection.
        """
        
        entities = await extract_entities_from_text(
            text=test_text,
            document_id="test_doc",
            fact_id=None,
            llm_provider='openrouter'
        )
        
        print(f"   ✅ Entity extraction successful")
        print(f"   🏷️ Extracted {len(entities)} entities")
        
        if entities:
            print(f"   📋 Sample entities:")
            for i, entity in enumerate(entities[:3], 1):
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                confidence = entity.get('confidence', 0.0)
                print(f"      {i}. {name} ({entity_type}) - confidence: {confidence}")
        
        # Test 3: Test the unified pipeline entity extraction method
        print("\n3️⃣ Testing unified pipeline entity extraction...")

        # Initialize the pipeline components first
        await pipeline._initialize_components()

        result = await pipeline._extract_entities(test_text, "test_document.pdf")
        
        print(f"   ✅ Pipeline entity extraction successful")
        print(f"   🏷️ Result: {result.get('count', 0)} entities")
        print(f"   🎯 Success: {result.get('success', False)}")
        
        if result.get('entities'):
            entities_from_pipeline = result.get('entities', [])
            print(f"   📋 Pipeline extracted entities:")
            for i, entity in enumerate(entities_from_pipeline[:3], 1):
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                print(f"      {i}. {name} ({entity_type})")
        
        # Test 4: Test database storage (if available)
        print("\n4️⃣ Testing entity storage...")
        
        if pipeline.database_adapter and result.get('entities'):
            try:
                storage_result = await pipeline._store_entities_in_graph(
                    result.get('entities', []), 
                    "test_document.pdf"
                )
                print(f"   ✅ Entity storage test: {storage_result}")
            except Exception as e:
                print(f"   ⚠️ Entity storage test failed: {e}")
        else:
            print(f"   ℹ️ Skipping storage test (no database or entities)")
        
        print(f"\n🎉 ENTITY EXTRACTION FIX TEST COMPLETE!")
        print(f"✅ The entity extraction integration has been fixed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_entity_extraction_fix())
