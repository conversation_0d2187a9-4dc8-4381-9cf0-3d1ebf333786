#!/usr/bin/env python3
"""
Reset databases for proper Graphiti integration.
This script will clean FalkorDB and Redis to start fresh with Graphiti base classes.
"""

import asyncio
import redis
from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.redis_vector_search import get_redis_vector_search_client
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class DatabaseResetter:
    """Resets databases for fresh Graphiti integration."""
    
    def __init__(self):
        self.falkor_adapter = None
        self.redis_client = None
        
    async def initialize(self):
        """Initialize database connections."""
        try:
            # Connect to main graphiti database
            self.falkor_adapter = GraphitiFalkorDBAdapter('graphiti')
            self.redis_client = get_redis_vector_search_client()
            logger.info("✅ Database connections initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize databases: {e}")
            raise
    
    def backup_current_data(self):
        """Create backup information about current data."""
        print("\n📊 CURRENT DATA SUMMARY (for backup reference):")
        
        try:
            # Get current counts
            result = self.falkor_adapter.execute_cypher("MATCH (e:Entity) RETURN count(e)")
            entity_count = result[1][0] if result and len(result) > 1 else 0
            
            result = self.falkor_adapter.execute_cypher("MATCH (f:Fact) RETURN count(f)")
            fact_count = result[1][0] if result and len(result) > 1 else 0
            
            result = self.falkor_adapter.execute_cypher("MATCH (ep:Episode) RETURN count(ep)")
            episode_count = result[1][0] if result and len(result) > 1 else 0
            
            result = self.falkor_adapter.execute_cypher("MATCH ()-[r]->() RETURN count(r)")
            rel_count = result[1][0] if result and len(result) > 1 else 0
            
            # Redis embeddings
            redis_keys = self.redis_client.keys('embedding:*')
            embedding_count = len(redis_keys)
            
            print(f"  📈 Entities: {entity_count:,}")
            print(f"  📈 Facts: {fact_count:,}")
            print(f"  📈 Episodes: {episode_count:,}")
            print(f"  📈 Relationships: {rel_count:,}")
            print(f"  📈 Embeddings: {embedding_count:,}")
            
            # Sample entity types
            result = self.falkor_adapter.execute_cypher("""
                MATCH (e:Entity) 
                RETURN e.type, count(e) 
                ORDER BY count(e) DESC 
                LIMIT 10
            """)
            
            if result and len(result) > 1:
                print(f"\n  📋 Top Entity Types:")
                for row in result[1:]:
                    if len(row) >= 2:
                        entity_type = row[0]
                        count = row[1]
                        print(f"    • {entity_type}: {count}")
                        
        except Exception as e:
            print(f"  ❌ Error getting backup info: {e}")
    
    def clear_falkordb(self):
        """Clear all data from FalkorDB graphiti database."""
        print("\n🗑️  CLEARING FALKORDB...")
        
        try:
            # Delete all nodes and relationships
            result = self.falkor_adapter.execute_cypher("MATCH (n) DETACH DELETE n")
            print("  ✅ Deleted all nodes and relationships")
            
            # Verify deletion
            result = self.falkor_adapter.execute_cypher("MATCH (n) RETURN count(n)")
            remaining = result[1][0] if result and len(result) > 1 else 0
            
            if remaining == 0:
                print("  ✅ FalkorDB is now empty")
            else:
                print(f"  ⚠️  Warning: {remaining} nodes still remain")
                
        except Exception as e:
            print(f"  ❌ Error clearing FalkorDB: {e}")
    
    def clear_redis_embeddings(self):
        """Clear all embeddings from Redis."""
        print("\n🗑️  CLEARING REDIS EMBEDDINGS...")
        
        try:
            # Get all embedding keys
            keys = self.redis_client.keys('embedding:*')
            
            if keys:
                # Delete all embedding keys
                deleted = self.redis_client.delete(*keys)
                print(f"  ✅ Deleted {deleted:,} embedding records")
            else:
                print("  ✅ No embeddings found to delete")
                
            # Clear reference keys too
            ref_keys = self.redis_client.keys('reference:*')
            if ref_keys:
                deleted_refs = self.redis_client.delete(*ref_keys)
                print(f"  ✅ Deleted {deleted_refs:,} reference records")
                
            # Verify deletion
            remaining_keys = self.redis_client.keys('embedding:*')
            if len(remaining_keys) == 0:
                print("  ✅ Redis embeddings cleared successfully")
            else:
                print(f"  ⚠️  Warning: {len(remaining_keys)} embedding keys still remain")
                
        except Exception as e:
            print(f"  ❌ Error clearing Redis: {e}")
    
    def setup_graphiti_schema(self):
        """Set up any required schema or indexes for Graphiti."""
        print("\n🔧 SETTING UP GRAPHITI SCHEMA...")
        
        try:
            # Create indexes for better performance
            indexes = [
                "CREATE INDEX FOR (n:Entity) ON (n.uuid)",
                "CREATE INDEX FOR (n:Entity) ON (n.name)",
                "CREATE INDEX FOR (n:Entity) ON (n.group_id)",
                "CREATE INDEX FOR (n:Episode) ON (n.uuid)",
                "CREATE INDEX FOR (n:Episode) ON (n.group_id)",
                "CREATE INDEX FOR (n:Fact) ON (n.uuid)",
                "CREATE INDEX FOR (n:Fact) ON (n.group_id)",
                "CREATE INDEX FOR (n:Community) ON (n.uuid)",
                "CREATE INDEX FOR (n:Community) ON (n.group_id)"
            ]
            
            for index_query in indexes:
                try:
                    self.falkor_adapter.execute_cypher(index_query)
                    print(f"  ✅ Created index: {index_query.split('(')[1].split(')')[0]}")
                except Exception as e:
                    # Index might already exist, that's okay
                    if "already exists" not in str(e).lower():
                        print(f"  ⚠️  Index creation note: {e}")
            
            print("  ✅ Schema setup complete")
            
        except Exception as e:
            print(f"  ❌ Error setting up schema: {e}")
    
    async def reset_databases(self):
        """Complete database reset process."""
        print("🚀 STARTING DATABASE RESET FOR GRAPHITI INTEGRATION")
        print("=" * 80)
        
        await self.initialize()
        
        # Show current state
        self.backup_current_data()
        
        # Confirm reset
        print(f"\n⚠️  WARNING: This will DELETE ALL DATA in:")
        print(f"  • FalkorDB 'graphiti' database")
        print(f"  • Redis embeddings and references")
        
        confirm = input(f"\nType 'YES' to proceed with reset: ")
        if confirm != 'YES':
            print("❌ Reset cancelled")
            return False
        
        # Perform reset
        self.clear_falkordb()
        self.clear_redis_embeddings()
        self.setup_graphiti_schema()
        
        print(f"\n🎉 DATABASE RESET COMPLETE!")
        print(f"✅ Ready for Graphiti base class integration")
        print(f"💡 Next step: Run the new Graphiti-compliant ingestion pipeline")
        
        return True

async def main():
    """Main reset function."""
    resetter = DatabaseResetter()
    success = await resetter.reset_databases()
    
    if success:
        print(f"\n📋 NEXT STEPS:")
        print(f"1. Run the new Graphiti ingestion pipeline")
        print(f"2. Process your documents using EntityNode, EpisodicNode classes")
        print(f"3. Verify proper group_id assignment and relationships")
        print(f"4. Test Graphiti search and reasoning features")

if __name__ == "__main__":
    asyncio.run(main())
