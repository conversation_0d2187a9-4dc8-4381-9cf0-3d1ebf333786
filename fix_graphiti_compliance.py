#!/usr/bin/env python3
"""
<PERSON>ript to fix critical Graphiti compliance issues in the current system.
This script will update existing nodes to include required Graphiti fields.
"""

import asyncio
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class GraphitiComplianceFixer:
    """Fixes critical compliance issues to align with Graphiti specifications."""
    
    def __init__(self):
        self.falkor_adapter = None
        self.fixed_entities = 0
        self.fixed_episodes = 0
        self.fixed_facts = 0
        
    async def initialize(self):
        """Initialize database connection."""
        try:
            self.falkor_adapter = GraphitiFalkorDBAdapter('knowledge_graph')
            logger.info("✅ Database connection initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise
    
    def generate_group_id(self, source: str = "default") -> str:
        """Generate a group_id for graph partitioning."""
        # Use source-based grouping for now
        return f"group_{source}_{datetime.now().strftime('%Y%m%d')}"
    
    async def fix_entity_nodes(self):
        """Fix Entity nodes to include required Graphiti fields."""
        print("\n🔧 FIXING ENTITY NODES")
        print("=" * 50)

        try:
            # Get all existing entities
            result = self.falkor_adapter.execute_cypher("MATCH (e:Entity) RETURN e")

            if not result or len(result) <= 2:  # Header + at least one data row
                print("📊 No entities found to fix")
                return

            # Parse FalkorDB result format: [header, [entity_list], metadata]
            entities_to_fix = []
            if len(result) >= 2 and isinstance(result[1], list):
                entity_list = result[1]

                for entity_wrapper in entity_list:
                    if isinstance(entity_wrapper, list) and len(entity_wrapper) > 0:
                        entity_data = entity_wrapper[0]  # Get the actual entity data

                        if isinstance(entity_data, list) and len(entity_data) >= 3:
                            # Extract: [id, labels, properties]
                            node_id = entity_data[0][1] if len(entity_data[0]) > 1 else None  # ['id', value]
                            labels = entity_data[1][1] if len(entity_data[1]) > 1 else []     # ['labels', [list]]

                            # Parse properties: [['name', 'value'], ['type', 'value'], ...]
                            properties = {}
                            if len(entity_data) > 2 and len(entity_data[2]) > 1:
                                prop_list = entity_data[2][1]  # ['properties', [list]]
                                for prop in prop_list:
                                    if isinstance(prop, list) and len(prop) >= 2:
                                        properties[prop[0]] = prop[1]

                            entities_to_fix.append({
                                'node_id': node_id,
                                'properties': properties
                            })

            print(f"📊 Found {len(entities_to_fix)} entities to fix")

            # Fix each entity
            for i, entity in enumerate(entities_to_fix, 1):
                try:
                    props = entity['properties']
                    node_id = entity['node_id']

                    # Generate missing required fields
                    entity_uuid = props.get('uuid', str(uuid.uuid4()))
                    entity_name = props.get('name', f'Entity_{i}')
                    group_id = props.get('group_id', self.generate_group_id(props.get('source', 'unknown')))
                    created_at = props.get('created_at', int(datetime.now(timezone.utc).timestamp() * 1000))

                    # Clean strings for Cypher query
                    entity_name_clean = entity_name.replace("'", "\\'")
                    group_id_clean = group_id.replace("'", "\\'")

                    # Update entity with required Graphiti fields
                    update_query = f"""
                    MATCH (e:Entity) WHERE id(e) = {node_id}
                    SET e.uuid = '{entity_uuid}',
                        e.name = '{entity_name_clean}',
                        e.group_id = '{group_id_clean}',
                        e.created_at = {created_at}
                    RETURN e.uuid
                    """

                    result = self.falkor_adapter.execute_cypher(update_query)
                    if result:
                        self.fixed_entities += 1
                        if i <= 5:  # Show first 5 for brevity
                            print(f"  ✅ Fixed entity {i}: {entity_name}")
                        elif i == 6:
                            print(f"  ... (fixing remaining entities)")

                except Exception as e:
                    logger.error(f"❌ Failed to fix entity {i}: {e}")
                    continue

            print(f"✅ Fixed {self.fixed_entities} entities")

        except Exception as e:
            logger.error(f"❌ Failed to fix entity nodes: {e}")
    
    async def fix_episode_nodes(self):
        """Fix Episode nodes to include required Graphiti fields."""
        print("\n🔧 FIXING EPISODE NODES")
        print("=" * 50)
        
        try:
            # Get all existing episodes
            result = self.falkor_adapter.execute_cypher("MATCH (ep:Episode) RETURN ep")
            
            if not result or len(result) <= 1:
                print("📊 No episodes found to fix")
                return
            
            episodes_to_fix = []
            for row in result[1:]:  # Skip header
                if row and len(row) > 0:
                    episode_data = row[0]
                    if isinstance(episode_data, list) and len(episode_data) >= 3:
                        # Extract current properties
                        node_id = episode_data[0] if len(episode_data) > 0 else None
                        properties = {}
                        
                        if len(episode_data) > 2:
                            prop_list = episode_data[2]
                            for prop in prop_list:
                                if isinstance(prop, list) and len(prop) >= 2:
                                    properties[prop[0]] = prop[1]
                        
                        episodes_to_fix.append({
                            'node_id': node_id,
                            'properties': properties
                        })
            
            print(f"📊 Found {len(episodes_to_fix)} episodes to fix")
            
            # Fix each episode
            for i, episode in enumerate(episodes_to_fix, 1):
                try:
                    props = episode['properties']
                    node_id = episode['node_id']
                    
                    # Generate missing required fields
                    episode_uuid = props.get('uuid', str(uuid.uuid4()))
                    episode_name = props.get('name', f'Episode_{i}')
                    group_id = props.get('group_id', self.generate_group_id('episode'))
                    created_at = props.get('created_at', int(datetime.now(timezone.utc).timestamp() * 1000))
                    
                    # Add EpisodicNode specific fields
                    source = props.get('source', 'document')
                    source_description = props.get('source_description', 'Document processing')
                    content = props.get('content', props.get('file_path', 'Unknown content'))
                    valid_at = props.get('valid_at', created_at)
                    
                    # Update episode with required Graphiti fields
                    update_query = f"""
                    MATCH (ep:Episode) WHERE id(ep) = {node_id}
                    SET ep.uuid = '{episode_uuid}',
                        ep.name = '{episode_name}',
                        ep.group_id = '{group_id}',
                        ep.created_at = {created_at},
                        ep.source = '{source}',
                        ep.source_description = '{source_description}',
                        ep.content = '{content}',
                        ep.valid_at = {valid_at},
                        ep.entity_edges = []
                    RETURN ep.uuid
                    """
                    
                    result = self.falkor_adapter.execute_cypher(update_query)
                    if result:
                        self.fixed_episodes += 1
                        print(f"  ✅ Fixed episode {i}: {episode_name}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to fix episode {i}: {e}")
                    continue
            
            print(f"✅ Fixed {self.fixed_episodes} episodes")
            
        except Exception as e:
            logger.error(f"❌ Failed to fix episode nodes: {e}")
    
    async def fix_fact_nodes(self):
        """Fix Fact nodes to include required Graphiti fields."""
        print("\n🔧 FIXING FACT NODES")
        print("=" * 50)
        
        try:
            # Get all existing facts
            result = self.falkor_adapter.execute_cypher("MATCH (f:Fact) RETURN f")
            
            if not result or len(result) <= 1:
                print("📊 No facts found to fix")
                return
            
            facts_to_fix = []
            for row in result[1:]:  # Skip header
                if row and len(row) > 0:
                    fact_data = row[0]
                    if isinstance(fact_data, list) and len(fact_data) >= 3:
                        # Extract current properties
                        node_id = fact_data[0] if len(fact_data) > 0 else None
                        properties = {}
                        
                        if len(fact_data) > 2:
                            prop_list = fact_data[2]
                            for prop in prop_list:
                                if isinstance(prop, list) and len(prop) >= 2:
                                    properties[prop[0]] = prop[1]
                        
                        facts_to_fix.append({
                            'node_id': node_id,
                            'properties': properties
                        })
            
            print(f"📊 Found {len(facts_to_fix)} facts to fix")
            
            # Fix each fact
            for i, fact in enumerate(facts_to_fix, 1):
                try:
                    props = fact['properties']
                    node_id = fact['node_id']
                    
                    # Generate missing required fields
                    fact_uuid = props.get('uuid', str(uuid.uuid4()))
                    fact_name = props.get('name', f'Fact_{i}')
                    group_id = props.get('group_id', self.generate_group_id('fact'))
                    created_at = props.get('created_at', int(datetime.now(timezone.utc).timestamp() * 1000))
                    
                    # Update fact with required Graphiti fields
                    update_query = f"""
                    MATCH (f:Fact) WHERE id(f) = {node_id}
                    SET f.uuid = '{fact_uuid}',
                        f.name = '{fact_name}',
                        f.group_id = '{group_id}',
                        f.created_at = {created_at}
                    RETURN f.uuid
                    """
                    
                    result = self.falkor_adapter.execute_cypher(update_query)
                    if result:
                        self.fixed_facts += 1
                        print(f"  ✅ Fixed fact {i}: {fact_name}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to fix fact {i}: {e}")
                    continue
            
            print(f"✅ Fixed {self.fixed_facts} facts")
            
        except Exception as e:
            logger.error(f"❌ Failed to fix fact nodes: {e}")
    
    async def run_fixes(self):
        """Run all compliance fixes."""
        print("🚀 STARTING GRAPHITI COMPLIANCE FIXES")
        print("=" * 80)
        
        await self.initialize()
        
        # Run fixes in order of priority
        await self.fix_entity_nodes()
        await self.fix_episode_nodes()
        await self.fix_fact_nodes()
        
        # Generate summary
        print("\n📋 FIX SUMMARY")
        print("=" * 50)
        print(f"✅ Fixed {self.fixed_entities} Entity nodes")
        print(f"✅ Fixed {self.fixed_episodes} Episode nodes")
        print(f"✅ Fixed {self.fixed_facts} Fact nodes")
        print(f"📊 Total nodes fixed: {self.fixed_entities + self.fixed_episodes + self.fixed_facts}")
        
        if self.fixed_entities + self.fixed_episodes + self.fixed_facts > 0:
            print("\n🎉 Compliance fixes completed!")
            print("💡 Run verify_graphiti_compliance.py again to check improvement")
        else:
            print("\n⚠️  No nodes were fixed. Check if nodes exist and have proper structure.")

async def main():
    """Main function."""
    fixer = GraphitiComplianceFixer()
    await fixer.run_fixes()

if __name__ == "__main__":
    asyncio.run(main())
