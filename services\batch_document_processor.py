"""
Batch document processor optimized for fast processing using traditional methods.
Uses Mistral OCR + PyPDF2 for PDFs instead of Docling for better batch performance.
"""

import asyncio
from typing import Dict, Any, Union
from pathlib import Path
import logging

from processors.pdf_processor import PDFProcessor
from processors.word_processor import WordProcessor
from processors.text_processor import TextProcessor
from processors.excel_processor import ExcelProcessor
from processors.powerpoint_processor import PowerPointProcessor
from processors.html_processor import HTMLProcessor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class BatchDocumentProcessor:
    """
    Document processor optimized for batch processing with traditional fast methods.
    """
    
    def __init__(self):
        """Initialize the batch document processor with fast processors."""
        self.processors = {
            'pdf': PDFProcessor(),           # Fast: Mistral OCR + PyPDF2
            'word': WordProcessor(),         # Fast: python-docx
            'text': TextProcessor(),         # Fast: direct text reading
            'spreadsheet': ExcelProcessor(), # Fast: openpyxl
            'presentation': PowerPointProcessor(), # Fast: python-pptx
            'html': HTMLProcessor(),         # Fast: BeautifulSoup
        }
        
        logger.info("✅ Batch document processor initialized with fast traditional processors")
    
    def _get_file_category(self, file_path: str) -> str:
        """
        Determine the file category based on extension.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File category string
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # Map extensions to categories
        extension_map = {
            '.pdf': 'pdf',
            '.doc': 'word',
            '.docx': 'word',
            '.txt': 'text',
            '.md': 'text',
            '.rtf': 'text',
            '.xlsx': 'spreadsheet',
            '.xls': 'spreadsheet',
            '.csv': 'spreadsheet',
            '.pptx': 'presentation',
            '.ppt': 'presentation',
            '.html': 'html',
            '.htm': 'html',
        }
        
        return extension_map.get(extension, 'text')  # Default to text processor
    
    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_metadata: bool = True
    ) -> Dict[str, Any]:
        """
        Process a document using fast traditional methods.
        
        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks in characters
            overlap: Overlap between chunks in characters
            extract_metadata: Whether to extract metadata
            
        Returns:
            Processing result dictionary
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                "success": False,
                "error": f"File not found: {file_path}"
            }
        
        try:
            # Determine file category and get appropriate processor
            file_category = self._get_file_category(str(file_path))
            processor = self.processors.get(file_category)
            
            if not processor:
                return {
                    "success": False,
                    "error": f"No fast processor available for file category: {file_category}"
                }
            
            logger.info(f"🚀 Processing {file_path.name} with fast {file_category} processor")
            
            # Extract text using the appropriate processor
            if hasattr(processor, 'extract_text'):
                result = await processor.extract_text(file_path)
            elif hasattr(processor, 'process_file'):
                result = await processor.process_file(str(file_path))
            else:
                return {
                    "success": False,
                    "error": f"Processor {file_category} does not have extract_text or process_file method"
                }
            
            if not result.get("success", False):
                return result
            
            text = result.get("text", "")
            metadata = result.get("metadata", {})
            
            # Add processing method info
            metadata.update({
                "processor_type": "batch_traditional",
                "file_category": file_category,
                "extraction_method": result.get("extraction_method", f"{file_category}_processor")
            })
            
            logger.info(f"✅ Fast processing completed for {file_path.name}: {len(text)} characters extracted")
            
            return {
                "success": True,
                "text": text,
                "metadata": metadata,
                "file_path": str(file_path),
                "processor_type": "batch_traditional",
                "extraction_method": result.get("extraction_method", f"{file_category}_processor")
            }
            
        except Exception as e:
            error_msg = f"Error in batch processing {file_path.name}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return [
            '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
            '.xlsx', '.xls', '.csv', '.pptx', '.ppt',
            '.html', '.htm'
        ]
