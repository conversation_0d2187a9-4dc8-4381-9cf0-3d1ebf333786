#!/usr/bin/env python3
"""
Check what properties the facts actually have.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def check_fact_properties():
    """Check the actual properties of facts."""
    
    print("🔍 Checking fact properties...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # Get latest episode
        query = '''
        MATCH (ep:Episode)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN ep.uuid as uuid
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or not result[1]:
            print("❌ No episodes found")
            return
            
        episode_id = result[1][0][0]
        print(f"✅ Episode: {episode_id}")
        
        # Get facts and their properties
        print("\n1. Checking all fact properties...")
        fact_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f
        ORDER BY f.chunk_index
        LIMIT 3
        '''
        fact_result = adapter.execute_cypher(fact_query)
        
        if fact_result and len(fact_result) > 1 and fact_result[1]:
            for i, row in enumerate(fact_result[1]):
                fact = row[0]
                print(f"\n   Fact {i+1}: {fact}")
                
        # Check specifically for embedding property
        print("\n2. Checking embedding property specifically...")
        embedding_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid as uuid, f.embedding as embedding, EXISTS(f.embedding) as has_embedding
        ORDER BY f.chunk_index
        '''
        embedding_result = adapter.execute_cypher(embedding_query)
        
        if embedding_result and len(embedding_result) > 1 and embedding_result[1]:
            for i, row in enumerate(embedding_result[1]):
                fact_uuid = row[0]
                embedding = row[1]
                has_embedding = row[2]
                print(f"   Fact {i+1}: {fact_uuid[:8]}... | embedding: {repr(embedding)} | exists: {has_embedding}")
        
        # Test the exact query used by embedding processor
        print("\n3. Testing embedding processor query...")
        processor_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        WHERE NOT EXISTS(f.embedding) OR f.embedding IS NULL OR f.embedding = ''
        RETURN f.uuid AS uuid, f.body AS body
        '''
        processor_result = adapter.execute_cypher(processor_query)
        
        if processor_result and len(processor_result) > 1 and processor_result[1]:
            print(f"✅ Embedding processor query found {len(processor_result[1])} facts without embeddings:")
            for i, row in enumerate(processor_result[1]):
                fact_uuid = row[0]
                body_length = len(row[1]) if row[1] else 0
                print(f"   Fact {i+1}: {fact_uuid[:8]}... ({body_length} chars)")
        else:
            print("❌ Embedding processor query found 0 facts without embeddings")
            
        # Test alternative query
        print("\n4. Testing alternative query (all facts)...")
        all_facts_query = f'''
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid AS uuid, f.body AS body
        '''
        all_facts_result = adapter.execute_cypher(all_facts_query)
        
        if all_facts_result and len(all_facts_result) > 1 and all_facts_result[1]:
            print(f"✅ All facts query found {len(all_facts_result[1])} facts total")
        else:
            print("❌ All facts query found 0 facts")
            
    except Exception as e:
        print(f"❌ Error during checking: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_fact_properties())
