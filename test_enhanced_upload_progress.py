#!/usr/bin/env python3
"""
Test enhanced upload progress tracking by simulating a document upload.
"""

import asyncio
import aiohttp
import json
import websockets
from pathlib import Path

async def test_enhanced_upload_with_progress():
    """Test enhanced upload with real-time progress tracking."""
    print("🧪 Testing Enhanced Upload with Progress Tracking...")
    
    # Create a test PDF file if it doesn't exist
    test_file = Path("test_document.pdf")
    if not test_file.exists():
        print("❌ No test PDF file found. Please ensure you have a PDF file to test with.")
        return False
    
    operation_id = None
    
    try:
        # Step 1: Upload the document
        print("\n📤 Step 1: Uploading document...")
        
        async with aiohttp.ClientSession() as session:
            # Prepare the upload data
            data = aiohttp.FormData()
            data.add_field('file', open(test_file, 'rb'), filename=test_file.name, content_type='application/pdf')
            data.add_field('chunk_size', '1200')
            data.add_field('overlap', '0')
            data.add_field('extract_entities', 'true')
            data.add_field('extract_references', 'true')
            data.add_field('extract_metadata', 'true')
            data.add_field('generate_embeddings', 'true')
            
            # Make the upload request to enhanced upload endpoint
            async with session.post('http://localhost:9753/api/enhanced/upload-with-duplicate-check', data=data) as response:
                print(f"📊 Upload response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    operation_id = result.get('operation_id')
                    print(f"✅ Upload started successfully!")
                    print(f"   🆔 Operation ID: {operation_id}")
                    print(f"   📄 Filename: {result.get('filename', 'unknown')}")
                else:
                    error_text = await response.text()
                    print(f"❌ Upload failed: {error_text}")
                    return False
        
        if not operation_id:
            print("❌ No operation ID received")
            return False
        
        # Step 2: Connect to WebSocket for progress updates
        print(f"\n🔌 Step 2: Connecting to WebSocket for operation {operation_id}...")
        
        try:
            websocket_url = f"ws://localhost:9753/ws/progress/{operation_id}"
            print(f"🔗 WebSocket URL: {websocket_url}")
            
            async with websockets.connect(websocket_url) as websocket:
                print(f"✅ WebSocket connected!")
                
                # Listen for progress updates
                progress_updates = []
                timeout_count = 0
                max_timeout = 60  # 60 seconds timeout
                
                while timeout_count < max_timeout:
                    try:
                        # Wait for message with 1 second timeout
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        data = json.loads(message)
                        
                        message_type = data.get('type', 'unknown')
                        print(f"📨 WebSocket message: {message_type}")
                        
                        if message_type == 'progress_update':
                            progress_data = data.get('data', {})
                            overall_progress = progress_data.get('overall_progress', 0)
                            current_step = progress_data.get('current_step', 'unknown')
                            message_text = progress_data.get('message', 'no message')
                            statistics = progress_data.get('statistics', {})
                            
                            print(f"   📈 Progress: {overall_progress}% - {current_step}")
                            print(f"   💬 Message: {message_text}")
                            if statistics:
                                print(f"   📊 Stats: {statistics}")
                            
                            progress_updates.append(data)
                            timeout_count = 0  # Reset timeout on activity
                            
                        elif message_type == 'operation_complete':
                            print(f"   🎉 Operation completed!")
                            final_data = data.get('data', {})
                            success = final_data.get('success', False)
                            print(f"   ✅ Success: {success}")
                            if success:
                                print(f"   📊 Final stats: {final_data}")
                            break
                            
                        elif message_type == 'operation_failed':
                            print(f"   ❌ Operation failed!")
                            error_data = data.get('data', {})
                            print(f"   💥 Error: {error_data}")
                            break
                            
                        elif message_type == 'connection_established':
                            print(f"   🔌 Connection established")
                            
                        elif message_type == 'operation_metadata':
                            print(f"   📋 Metadata received")
                            metadata = data.get('data', {})
                            print(f"   📄 File: {metadata.get('filename', 'unknown')}")
                            
                        else:
                            print(f"   ❓ Unknown message type: {message_type}")
                            print(f"   📄 Data: {data}")
                        
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 10 == 0:  # Print every 10 seconds
                            print(f"   ⏳ Waiting for updates... ({timeout_count}s)")
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        print(f"   🔌 WebSocket connection closed")
                        break
                    except Exception as e:
                        print(f"   ❌ WebSocket error: {e}")
                        break
                
                if timeout_count >= max_timeout:
                    print(f"   ⏰ Timeout reached ({max_timeout}s)")
                
                print(f"\n📊 Total progress updates received: {len(progress_updates)}")
                
                if progress_updates:
                    print("✅ Progress tracking is working!")
                    return True
                else:
                    print("❌ No progress updates received")
                    return False
                    
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    print("🚀 Starting Enhanced Upload Progress Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:9753/') as response:
                if response.status == 200:
                    print("✅ Server is running")
                else:
                    print(f"❌ Server returned status {response.status}")
                    return
    except Exception as e:
        print(f"❌ Server is not running: {e}")
        print("   Please start the server with: python app.py")
        return
    
    # Run the test
    success = await test_enhanced_upload_with_progress()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Enhanced upload progress tracking test PASSED!")
    else:
        print("❌ Enhanced upload progress tracking test FAILED!")
        print("\n🔧 Troubleshooting tips:")
        print("1. Check browser console for WebSocket errors")
        print("2. Verify the enhanced upload UI is loading correctly")
        print("3. Check server logs for progress tracking issues")
        print("4. Ensure the enhanced progress tracker is being used")

if __name__ == "__main__":
    asyncio.run(main())
