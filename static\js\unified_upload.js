/**
 * Unified Upload Manager
 * Combines batch upload and enhanced upload functionality
 */

class UnifiedUploadManager {
    constructor() {
        this.selectedFiles = new Map(); // Use Map to avoid duplicates
        this.activeOperations = new Set();
        this.completedOperations = new Set();
        this.websocketManager = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeWebSocket();
    }
    
    initializeElements() {
        // Main elements
        this.dropzone = document.getElementById('dropzone');
        this.fileInput = document.getElementById('file-input');
        this.folderInput = document.getElementById('folder-input');
        this.selectFilesButton = document.getElementById('select-files-button');
        this.selectFolderButton = document.getElementById('select-folder-button');
        this.uploadButton = document.getElementById('upload-button');
        this.clearFilesButton = document.getElementById('clear-files-button');
        
        // Containers
        this.fileListContainer = document.getElementById('file-list-container');
        this.fileList = document.getElementById('file-list');
        this.progressContainer = document.getElementById('progress-container');
        this.progressList = document.getElementById('progress-list');
        this.alertContainer = document.getElementById('alert-container');
        
        // Settings
        this.chunkSizeInput = document.getElementById('chunk-size');
        this.overlapInput = document.getElementById('overlap');
        this.extractEntitiesCheckbox = document.getElementById('extract-entities');
        this.extractReferencesCheckbox = document.getElementById('extract-references');
        this.extractMetadataCheckbox = document.getElementById('extract-metadata');
        this.generateEmbeddingsCheckbox = document.getElementById('generate-embeddings');
        this.maxParallelInput = document.getElementById('max-parallel');
        this.duplicateDetectionCheckbox = document.getElementById('duplicate-detection');
        
        // Counters
        this.activeOperationsCounter = document.getElementById('active-operations');
        this.completedOperationsCounter = document.getElementById('completed-operations');
        this.totalFilesCounter = document.getElementById('total-files');
    }
    
    setupEventListeners() {
        console.log('🔧 Setting up event listeners...');

        // File selection
        if (this.selectFilesButton) {
            this.selectFilesButton.addEventListener('click', () => {
                console.log('📁 Select files button clicked');
                this.fileInput.click();
            });
        } else {
            console.error('❌ Select files button not found');
        }

        if (this.selectFolderButton) {
            this.selectFolderButton.addEventListener('click', () => {
                console.log('📂 Select folder button clicked');
                this.folderInput.click();
            });
        } else {
            console.error('❌ Select folder button not found');
        }

        if (this.fileInput) {
            this.fileInput.addEventListener('change', (e) => {
                console.log('📄 File input changed:', e.target.files.length, 'files');
                this.handleFileSelection(e.target.files);
            });
        } else {
            console.error('❌ File input not found');
        }

        if (this.folderInput) {
            this.folderInput.addEventListener('change', (e) => {
                console.log('📂 Folder input changed:', e.target.files.length, 'files');
                this.handleFileSelection(e.target.files);
            });
        } else {
            console.error('❌ Folder input not found');
        }

        // Drag and drop
        if (this.dropzone) {
            this.dropzone.addEventListener('dragover', (e) => this.handleDragOver(e));
            this.dropzone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            this.dropzone.addEventListener('drop', (e) => this.handleDrop(e));
        } else {
            console.error('❌ Dropzone not found');
        }

        // Upload controls
        if (this.uploadButton) {
            this.uploadButton.addEventListener('click', () => {
                console.log('🚀 Upload button clicked');
                this.startUpload();
            });
        } else {
            console.error('❌ Upload button not found');
        }

        if (this.clearFilesButton) {
            this.clearFilesButton.addEventListener('click', () => {
                console.log('🧹 Clear files button clicked');
                this.clearFiles();
            });
        } else {
            console.error('❌ Clear files button not found');
        }

        // Settings validation
        if (this.chunkSizeInput) {
            this.chunkSizeInput.addEventListener('change', () => this.validateSettings());
        }
        if (this.overlapInput) {
            this.overlapInput.addEventListener('change', () => this.validateSettings());
        }
        if (this.maxParallelInput) {
            this.maxParallelInput.addEventListener('change', () => this.validateSettings());
        }

        console.log('✅ Event listeners setup complete');
    }
    
    initializeWebSocket() {
        // Initialize WebSocket for real-time progress updates
        if (window.WebSocketUploader) {
            this.websocketManager = new WebSocketUploader();
            this.websocketManager.onProgressUpdate = (data) => this.handleProgressUpdate(data);
            this.websocketManager.onOperationComplete = (data) => this.handleOperationComplete(data);
        }
    }
    
    handleDragOver(e) {
        e.preventDefault();
        this.dropzone.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.dropzone.classList.remove('drag-over');
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.dropzone.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        this.handleFileSelection(files);
    }
    
    handleFileSelection(files) {
        console.log('📄 handleFileSelection called with:', files.length, 'files');
        const fileArray = Array.from(files);

        // Add files to selection (avoid duplicates)
        fileArray.forEach(file => {
            const fileKey = `${file.name}_${file.size}_${file.lastModified}`;
            console.log('📄 Processing file:', file.name, 'Key:', fileKey);
            if (!this.selectedFiles.has(fileKey)) {
                this.selectedFiles.set(fileKey, file);
                console.log('✅ Added file:', file.name);
            } else {
                console.log('⚠️ File already exists:', file.name);
            }
        });

        console.log('📊 Total selected files:', this.selectedFiles.size);
        this.updateFileList();
        this.updateCounters();
        this.showAlert(`Added ${fileArray.length} file(s). Total: ${this.selectedFiles.size}`, 'success');
    }
    
    updateFileList() {
        if (this.selectedFiles.size === 0) {
            this.fileListContainer.style.display = 'none';
            this.uploadButton.disabled = true;
            return;
        }
        
        this.fileListContainer.style.display = 'block';
        this.uploadButton.disabled = false;
        
        this.fileList.innerHTML = '';
        
        this.selectedFiles.forEach((file, fileKey) => {
            const fileItem = this.createFileItem(file, fileKey);
            this.fileList.appendChild(fileItem);
        });
    }
    
    createFileItem(file, fileKey) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item p-3 d-flex justify-content-between align-items-center';
        
        const fileInfo = document.createElement('div');
        fileInfo.className = 'd-flex align-items-center';
        
        const fileIcon = this.getFileIcon(file.name);
        const fileBadge = this.getFileBadge(file.name);
        const fileSize = this.formatFileSize(file.size);
        
        fileInfo.innerHTML = `
            <i class="bi ${fileIcon} me-2 text-primary"></i>
            <div>
                <div class="fw-medium">${file.name}</div>
                <small class="text-muted">${fileSize} ${fileBadge}</small>
            </div>
        `;
        
        const removeButton = document.createElement('button');
        removeButton.className = 'btn btn-sm btn-outline-danger';
        removeButton.innerHTML = '<i class="bi bi-trash"></i>';
        removeButton.onclick = () => this.removeFile(fileKey);
        
        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeButton);
        
        return fileItem;
    }
    
    getFileIcon(filename) {
        const ext = filename.toLowerCase().split('.').pop();
        const iconMap = {
            'pdf': 'bi-file-earmark-pdf',
            'doc': 'bi-file-earmark-word',
            'docx': 'bi-file-earmark-word',
            'txt': 'bi-file-earmark-text',
            'md': 'bi-file-earmark-text',
            'one': 'bi-journal-richtext',
            'xls': 'bi-file-earmark-excel',
            'xlsx': 'bi-file-earmark-excel',
            'ppt': 'bi-file-earmark-ppt',
            'pptx': 'bi-file-earmark-ppt',
            'html': 'bi-file-earmark-code',
            'htm': 'bi-file-earmark-code'
        };
        return iconMap[ext] || 'bi-file-earmark';
    }
    
    getFileBadge(filename) {
        const ext = filename.toLowerCase().split('.').pop();
        const badgeMap = {
            'one': '<span class="badge onenote-badge">OneNote</span>',
            'pdf': '<span class="badge pdf-badge">PDF</span>',
            'doc': '<span class="badge doc-badge">Word</span>',
            'docx': '<span class="badge doc-badge">Word</span>',
            'txt': '<span class="badge text-badge">Text</span>',
            'md': '<span class="badge text-badge">Markdown</span>'
        };
        return badgeMap[ext] || '<span class="badge other-badge">Other</span>';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    removeFile(fileKey) {
        this.selectedFiles.delete(fileKey);
        this.updateFileList();
        this.updateCounters();
    }
    
    clearFiles() {
        this.selectedFiles.clear();
        this.updateFileList();
        this.updateCounters();
        this.showAlert('All files cleared', 'info');
    }
    
    validateSettings() {
        const chunkSize = parseInt(this.chunkSizeInput.value);
        const overlap = parseInt(this.overlapInput.value);
        const maxParallel = parseInt(this.maxParallelInput.value);
        
        let isValid = true;
        
        if (isNaN(chunkSize) || chunkSize < 100 || chunkSize > 10000) {
            this.chunkSizeInput.classList.add('is-invalid');
            isValid = false;
        } else {
            this.chunkSizeInput.classList.remove('is-invalid');
        }
        
        if (isNaN(overlap) || overlap < 0 || overlap > 500) {
            this.overlapInput.classList.add('is-invalid');
            isValid = false;
        } else {
            this.overlapInput.classList.remove('is-invalid');
        }
        
        if (isNaN(maxParallel) || maxParallel < 1 || maxParallel > 8) {
            this.maxParallelInput.classList.add('is-invalid');
            isValid = false;
        } else {
            this.maxParallelInput.classList.remove('is-invalid');
        }
        
        return isValid;
    }
    
    async startUpload() {
        console.log('🚀 startUpload called');
        console.log('📊 Selected files count:', this.selectedFiles.size);

        if (this.selectedFiles.size === 0) {
            console.log('⚠️ No files selected');
            this.showAlert('Please select at least one file to upload.', 'warning');
            return;
        }
        
        if (!this.validateSettings()) {
            this.showAlert('Please fix the validation errors in settings.', 'danger');
            return;
        }
        
        this.uploadButton.disabled = true;

        // Use Enhanced Progress UI instead of old progress container
        if (this.progressContainer) {
            this.progressContainer.style.display = 'block';
        } else {
            console.log('📊 Using Enhanced Progress UI (no old progress container)');
        }
        
        // Convert Map to Array for processing
        const files = Array.from(this.selectedFiles.values());
        
        try {
            if (this.duplicateDetectionCheckbox.checked) {
                await this.uploadWithDuplicateDetection(files);
            } else {
                await this.uploadFiles(files);
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showAlert(`Upload failed: ${error.message}`, 'danger');
        } finally {
            this.uploadButton.disabled = false;
        }
    }
    
    async uploadFiles(files) {
        // Use the unified ingestion pipeline endpoint
        const formData = new FormData();
        
        // Add all files
        files.forEach(file => {
            formData.append('files', file);
        });
        
        // Add settings
        formData.append('chunk_size', this.chunkSizeInput.value);
        formData.append('overlap', this.overlapInput.value);
        formData.append('extract_entities', this.extractEntitiesCheckbox.checked);
        formData.append('extract_references', this.extractReferencesCheckbox.checked);
        formData.append('extract_metadata', this.extractMetadataCheckbox.checked);
        formData.append('generate_embeddings', this.generateEmbeddingsCheckbox.checked);
        formData.append('max_parallel_processes', this.maxParallelInput.value);
        
        const response = await fetch('/api/unified/batch-upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        this.handleUploadResponse(result);
    }
    
    async uploadWithDuplicateDetection(files) {
        // Process files one by one with duplicate detection
        for (const file of files) {
            try {
                await this.uploadSingleFileWithDuplicateCheck(file);
            } catch (error) {
                console.error(`Error uploading ${file.name}:`, error);
                this.showAlert(`Failed to upload ${file.name}: ${error.message}`, 'danger');
            }
        }
    }
    
    async uploadSingleFileWithDuplicateCheck(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('chunk_size', this.chunkSizeInput.value);
        formData.append('overlap', this.overlapInput.value);
        formData.append('extract_entities', this.extractEntitiesCheckbox.checked);
        formData.append('extract_references', this.extractReferencesCheckbox.checked);
        formData.append('extract_metadata', this.extractMetadataCheckbox.checked);
        formData.append('generate_embeddings', this.generateEmbeddingsCheckbox.checked);
        
        const response = await fetch('/api/unified/upload-with-duplicate-check', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.duplicate_detected) {
            await this.handleDuplicateDetection(result, file);
        } else {
            this.handleSingleUploadResponse(result);
        }
    }
    
    handleUploadResponse(result) {
        if (result.success) {
            this.showAlert(`Successfully started processing ${result.files_processed} files`, 'success');
            
            // Track operations
            if (result.operation_ids) {
                result.operation_ids.forEach(opId => {
                    this.activeOperations.add(opId);
                });
            }
        } else {
            this.showAlert(`Upload failed: ${result.message}`, 'danger');
        }
        
        this.updateCounters();
    }
    
    handleSingleUploadResponse(result) {
        if (result.operation_id) {
            this.activeOperations.add(result.operation_id);
            this.addProgressItem(result.operation_id, result.filename);

            // Start polling for this operation in case WebSocket fails
            this.startOperationPolling(result.operation_id);
        }
        this.updateCounters();
    }
    
    handleProgressUpdate(data) {
        // Update progress for specific operation
        this.updateProgressItem(data.operation_id, data);
    }
    
    async handleOperationComplete(data) {
        this.activeOperations.delete(data.operation_id);
        this.completedOperations.add(data.operation_id);

        // Fetch final results from the API
        try {
            const response = await fetch(`/api/unified/operations/${data.operation_id}`);
            if (response.ok) {
                const finalResults = await response.json();
                this.updateProgressItemWithResults(data.operation_id, finalResults);
            } else {
                this.updateProgressItem(data.operation_id, data);
            }
        } catch (error) {
            console.error('Error fetching final results:', error);
            this.updateProgressItem(data.operation_id, data);
        }

        this.updateCounters();
    }

    startOperationPolling(operationId) {
        // Poll every 2 seconds to check operation status
        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/unified/operations/${operationId}`);
                if (response.ok) {
                    const operationData = await response.json();

                    // Update progress
                    this.updateProgressItem(operationId, operationData);

                    // Check if completed
                    if (operationData.status === 'completed' || operationData.status === 'failed') {
                        clearInterval(pollInterval);

                        // Handle completion
                        this.activeOperations.delete(operationId);
                        this.completedOperations.add(operationId);
                        this.updateProgressItemWithResults(operationId, operationData);
                        this.updateCounters();
                    }
                } else if (response.status === 404) {
                    // Operation not found, might be completed and cleaned up
                    clearInterval(pollInterval);
                }
            } catch (error) {
                console.error('Error polling operation status:', error);
            }
        }, 2000);

        // Stop polling after 5 minutes to prevent infinite polling
        setTimeout(() => {
            clearInterval(pollInterval);
        }, 300000);
    }

    addProgressItem(operationId, filename) {
        const progressItem = document.createElement('div');
        progressItem.id = `progress-${operationId}`;
        progressItem.className = 'progress-item mb-3 p-3 border rounded';
        progressItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-medium">${filename}</span>
                <span class="badge bg-primary">Processing</span>
            </div>
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted">Initializing...</small>
        `;

        // Use Enhanced Progress UI instead of old progress list
        if (this.progressList) {
            this.progressList.appendChild(progressItem);
        } else {
            console.log('📊 Using Enhanced Progress UI (no old progress list)');
        }
    }
    
    updateProgressItem(operationId, data) {
        const progressItem = document.getElementById(`progress-${operationId}`);
        if (!progressItem) return;

        const progressBar = progressItem.querySelector('.progress-bar');
        const statusBadge = progressItem.querySelector('.badge');
        const statusText = progressItem.querySelector('small');

        if (data.progress !== undefined) {
            progressBar.style.width = `${data.progress}%`;
        }

        if (data.status) {
            statusBadge.textContent = data.status;
            statusBadge.className = `badge ${this.getStatusBadgeClass(data.status)}`;
        }

        if (data.message) {
            statusText.textContent = data.message;
        }
    }

    updateProgressItemWithResults(operationId, data) {
        const progressItem = document.getElementById(`progress-${operationId}`);
        if (!progressItem) return;

        const progressBar = progressItem.querySelector('.progress-bar');
        const statusBadge = progressItem.querySelector('.badge');
        const statusText = progressItem.querySelector('small');

        // Update progress to 100%
        progressBar.style.width = '100%';

        // Update status
        if (data.success) {
            statusBadge.textContent = 'completed';
            statusBadge.className = 'badge bg-success';
        } else {
            statusBadge.textContent = 'failed';
            statusBadge.className = 'badge bg-danger';
        }

        // Display final results with statistics
        if (data.result && data.success) {
            const result = data.result;
            const entities = result.entities || 0;
            const chunks = result.chunks || 0;
            const references = result.references || 0;
            const textLength = result.text_length || 0;

            statusText.innerHTML = `
                <strong>Processing Complete!</strong><br>
                📄 Text: ${textLength.toLocaleString()} chars |
                🧩 Chunks: ${chunks} |
                🏷️ Entities: ${entities} |
                📚 References: ${references}
            `;
        } else if (data.message) {
            statusText.textContent = data.message;
        } else {
            statusText.textContent = 'Processing completed';
        }
    }
    
    getStatusBadgeClass(status) {
        const statusMap = {
            'processing': 'bg-primary',
            'completed': 'bg-success',
            'failed': 'bg-danger',
            'duplicate': 'bg-warning'
        };
        return statusMap[status.toLowerCase()] || 'bg-secondary';
    }
    
    updateCounters() {
        this.activeOperationsCounter.textContent = this.activeOperations.size;
        this.completedOperationsCounter.textContent = this.completedOperations.size;
        this.totalFilesCounter.textContent = this.selectedFiles.size;
    }
    
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        this.alertContainer.appendChild(alertDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    async handleDuplicateDetection(result, file) {
        // Show duplicate detection modal
        if (window.showDuplicateModal) {
            const userChoice = await window.showDuplicateModal(result);
            
            if (userChoice === 'process') {
                // Process anyway
                const formData = new FormData();
                formData.append('file', file);
                formData.append('skip_duplicate_check', 'true');
                // Add other settings...
                
                const response = await fetch('/api/unified/upload-with-duplicate-check', {
                    method: 'POST',
                    body: formData
                });
                
                const retryResult = await response.json();
                this.handleSingleUploadResponse(retryResult);
            }
            // If 'skip', do nothing
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.unifiedUploadManager = new UnifiedUploadManager();
});
