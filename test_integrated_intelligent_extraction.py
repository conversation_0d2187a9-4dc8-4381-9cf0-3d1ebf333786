#!/usr/bin/env python3
"""
Test the fully integrated Intelligent Reference Extractor in the unified pipeline.
"""

import asyncio
import sys
from pathlib import Path
from unified_ingestion_pipeline import get_unified_pipeline
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def test_integrated_intelligent_extraction():
    """Test the integrated intelligent reference extraction."""
    print("🧪 Testing Integrated Intelligent Reference Extraction")
    print("=" * 70)
    
    # Find a test document
    test_documents = [
        "uploads/5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal Oseki.pdf",
        "uploads/c722751a-1835-4355-a66e-787e69ea9ff0_Breast Cancer.pdf",
        "uploads/ed3a2733-1fa7-4cff-ab8d-74de00b95b27_CURCUMIN.pdf"
    ]
    
    document_path = None
    for doc in test_documents:
        if Path(doc).exists():
            document_path = Path(doc)
            break
    
    if not document_path:
        print("❌ No test documents found")
        return False
    
    print(f"📄 Testing with: {document_path.name}")
    print(f"📊 File size: {document_path.stat().st_size:,} bytes")
    
    try:
        # Get the unified pipeline
        print(f"\n🔧 Initializing unified pipeline...")
        pipeline = await get_unified_pipeline()
        
        # Verify the intelligent extractor is loaded
        if hasattr(pipeline, 'intelligent_extractor'):
            print(f"✅ Intelligent extractor is loaded in pipeline")
        else:
            print(f"❌ Intelligent extractor not found in pipeline")
            return False
        
        # Process document with reference extraction only
        print(f"\n🚀 Processing document with integrated intelligent extraction...")
        result = await pipeline.process_document(
            file_path=document_path,
            chunk_size=1200,
            overlap=0,
            extract_entities=False,  # Focus on references only
            extract_references=True,  # This should use intelligent extractor
            extract_metadata=False,
            generate_embeddings=False
        )
        
        if result.get('success', False):
            print(f"\n✅ Integration test PASSED!")
            
            # Extract results
            references_found = result.get('references', 0)
            csv_path = result.get('reference_csv_path')
            
            print(f"\n📊 Results:")
            print(f"   📚 References found: {references_found}")
            print(f"   📄 CSV path: {csv_path}")
            
            # Performance assessment
            if references_found >= 50:
                print(f"\n🎉 EXCELLENT: Found {references_found} references!")
                print(f"   The Intelligent Reference Extractor is working perfectly!")
            elif references_found >= 20:
                print(f"\n✅ GOOD: Found {references_found} references")
                print(f"   The integration is working correctly")
            elif references_found >= 10:
                print(f"\n⚠️ FAIR: Found {references_found} references")
                print(f"   May need some optimization")
            else:
                print(f"\n❌ POOR: Only found {references_found} references")
                print(f"   Something may be wrong with the integration")
            
            # Check CSV file
            if csv_path and Path(csv_path).exists():
                print(f"\n📄 CSV file verification:")
                print(f"   ✅ CSV file created: {Path(csv_path).name}")
                
                # Read first few lines
                try:
                    with open(csv_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()[:6]  # Header + 5 references
                    
                    print(f"   📊 CSV contains {len(lines)-1} references (showing first 5):")
                    for i, line in enumerate(lines[1:], 1):  # Skip header
                        if i > 5:
                            break
                        # Extract reference text (assuming it's in column 2)
                        parts = line.split(',', 2)
                        if len(parts) >= 2:
                            ref_text = parts[1].strip('"')[:80]
                            print(f"     {i}. {ref_text}{'...' if len(parts[1]) > 80 else ''}")
                
                except Exception as e:
                    print(f"   ⚠️ Could not read CSV: {e}")
            else:
                print(f"\n❌ CSV file not created or not found")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"\n❌ Integration test FAILED: {error}")
            return False
            
    except Exception as e:
        print(f"\n❌ Integration test ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_intelligent_extractor():
    """Test the intelligent extractor directly to ensure it's working."""
    print(f"\n" + "=" * 70)
    print(f"🔬 Testing Direct Intelligent Extractor")
    print(f"=" * 70)
    
    try:
        from services.intelligent_reference_extractor import get_intelligent_reference_extractor
        
        extractor = get_intelligent_reference_extractor()
        print(f"✅ Intelligent extractor loaded successfully")
        
        # Test with sample text
        sample_text = """
        Australian Institute of Health and Welfare. Chronic Kidney Disease: Australian Facts. Aust Gov. 2023;(Feburary):1-121.
        
        Liyanage T, Toyama T, Hockham C, et al. BMJ Glob Heal. 2022;7(1):1-9. doi:10.1136/bmjgh-***********
        
        Zoccali, C., Vanholder, R., Massy, Z. A., Ortiz, A., Sarafidis (2017). Nature Reviews Nephrology, 13(6), 344–358. https://doi.org/10.1038/nrneph.2017.52
        """
        
        print(f"🧪 Testing with sample text...")
        result = await extractor.extract_references_comprehensive(sample_text, "test_document.pdf")
        
        print(f"✅ Direct test completed:")
        print(f"   📚 References found: {result['total_found']}")
        print(f"   🎯 Confidence: {result['confidence_score']}")
        print(f"   🔧 Methods: {result['extraction_methods']}")
        
        if result['total_found'] >= 3:
            print(f"✅ Direct extractor is working correctly!")
            return True
        else:
            print(f"⚠️ Direct extractor found fewer references than expected")
            return False
            
    except Exception as e:
        print(f"❌ Direct extractor test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Testing Fully Integrated Intelligent Reference Extraction")
    print("=" * 70)
    
    # Test 1: Direct extractor
    direct_success = await test_direct_intelligent_extractor()
    
    # Test 2: Integrated pipeline
    integration_success = await test_integrated_intelligent_extraction()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 Test Results Summary:")
    print(f"   Direct Extractor: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"   Pipeline Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    if direct_success and integration_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The Intelligent Reference Extractor is fully integrated and working!")
        print(f"All old reference extraction systems have been successfully deprecated.")
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print(f"Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
