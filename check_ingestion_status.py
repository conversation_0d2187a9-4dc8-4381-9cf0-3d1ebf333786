#!/usr/bin/env python3
"""
Check the current ingestion status and identify issues with entity extraction.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter

async def check_current_state():
    adapter = GraphitiFalkorDBAdapter('graphiti')
    
    print('=== CURRENT DATABASE STATE ===')
    
    # Check total counts
    queries = {
        'Episodes': 'MATCH (ep:Episode) RETURN count(ep) as count',
        'Facts': 'MATCH (f:Fact) RETURN count(f) as count', 
        'Entities': 'MATCH (e:Entity) RETURN count(e) as count',
        'MENTIONS relationships': 'MATCH ()-[r:MENTIONS]->() RETURN count(r) as count'
    }
    
    for name, query in queries.items():
        result = adapter.execute_cypher(query)
        if result and len(result) > 1 and len(result[1]) > 0:
            count = result[1][0][0]
            print(f'{name}: {count}')
    
    print('\n=== DOCUMENTS (EPISODES) ===')
    # Get all episodes with details
    query = '''
    MATCH (ep:Episode) 
    RETURN ep.uuid as uuid, ep.name as name, ep.processed_at as processed_at
    ORDER BY ep.processed_at DESC
    '''
    result = adapter.execute_cypher(query)
    if result and len(result) > 1:
        headers = result[0]
        for i, row in enumerate(result[1]):
            doc_data = dict(zip(headers, row))
            print(f'{i+1}. {doc_data["name"]}')
            print(f'   UUID: {doc_data["uuid"]}')
            print(f'   Processed: {doc_data["processed_at"]}')
            
            # Check facts for this episode
            fact_query = f'MATCH (ep:Episode {{uuid: "{doc_data["uuid"]}"}}) -[:CONTAINS]-> (f:Fact) RETURN count(f) as fact_count'
            fact_result = adapter.execute_cypher(fact_query)
            fact_count = fact_result[1][0][0] if fact_result and len(fact_result) > 1 else 0
            
            # Check entities mentioned by facts in this episode
            entity_query = f'''
            MATCH (ep:Episode {{uuid: "{doc_data["uuid"]}"}}) -[:CONTAINS]-> (f:Fact) -[:MENTIONS]-> (e:Entity)
            RETURN count(DISTINCT e) as entity_count
            '''
            entity_result = adapter.execute_cypher(entity_query)
            entity_count = entity_result[1][0][0] if entity_result and len(entity_result) > 1 else 0
            
            print(f'   Facts: {fact_count}, Entities: {entity_count}')
            print('   ---')
    
    print('\n=== FACTS WITHOUT ENTITIES ===')
    # Check for facts without entity mentions
    query = '''
    MATCH (f:Fact)
    WHERE NOT (f)-[:MENTIONS]->(:Entity)
    RETURN count(f) as facts_without_entities
    '''
    result = adapter.execute_cypher(query)
    if result and len(result) > 1 and len(result[1]) > 0:
        count = result[1][0][0]
        print(f'Facts without entity mentions: {count}')
        
        if count > 0:
            # Get sample facts without entities
            sample_query = '''
            MATCH (ep:Episode)-[:CONTAINS]->(f:Fact)
            WHERE NOT (f)-[:MENTIONS]->(:Entity)
            RETURN ep.name as doc_name, f.uuid as fact_uuid, f.body as fact_text
            LIMIT 3
            '''
            sample_result = adapter.execute_cypher(sample_query)
            if sample_result and len(sample_result) > 1:
                print('\nSample facts without entities:')
                headers = sample_result[0]
                for row in sample_result[1]:
                    fact_data = dict(zip(headers, row))
                    print(f'  Document: {fact_data["doc_name"]}')
                    print(f'  Fact UUID: {fact_data["fact_uuid"]}')
                    print(f'  Text sample: {fact_data["fact_text"][:100]}...')
                    print('  ---')

if __name__ == "__main__":
    asyncio.run(check_current_state())
