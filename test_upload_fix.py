#!/usr/bin/env python3
"""
Test the unified upload fix
"""

import asyncio
import aiohttp
import aiofiles
from pathlib import Path

async def test_upload_fix():
    """Test the unified upload fix."""
    
    print("🧪 TESTING UNIFIED UPLOAD FIX")
    print("=" * 40)
    
    try:
        # Create a test file
        test_file_path = Path("test_upload.txt")
        test_content = """
        This is a test document for the unified upload system.
        
        It contains some sample text about health and nutrition.
        Vitamin C is important for immune function.
        Magnesium supports over 300 enzymatic reactions.
        Omega-3 fatty acids have anti-inflammatory properties.
        """
        
        # Write test file
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 Created test file: {test_file_path}")
        
        # Test the upload API
        async with aiohttp.ClientSession() as session:
            # Prepare form data
            data = aiohttp.FormData()
            
            # Read file content
            with open(test_file_path, 'rb') as f:
                file_content = f.read()
            
            # Add file to form data
            data.add_field('file', file_content, filename='test_upload.txt', content_type='text/plain')
            data.add_field('chunk_size', '1200')
            data.add_field('overlap', '0')
            data.add_field('extract_entities', 'true')
            data.add_field('extract_references', 'true')
            data.add_field('extract_metadata', 'true')
            data.add_field('generate_embeddings', 'true')
            data.add_field('skip_duplicate_check', 'false')
            
            print(f"🚀 Testing upload API...")
            
            # Make the upload request
            async with session.post('http://localhost:9753/api/unified/upload-with-duplicate-check', data=data) as response:
                print(f"📊 Response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Upload successful!")
                    print(f"   📄 Filename: {result.get('filename', 'unknown')}")
                    print(f"   🆔 Operation ID: {result.get('operation_id', 'none')}")
                    print(f"   📝 File type: {result.get('file_type', 'unknown')}")
                    print(f"   🔍 Duplicate detected: {result.get('duplicate_detected', False)}")
                    print(f"   💬 Message: {result.get('message', 'no message')}")
                    
                    # Check if operation is being tracked
                    if result.get('operation_id'):
                        operation_id = result.get('operation_id')
                        
                        # Wait a moment for processing to start
                        await asyncio.sleep(2)
                        
                        # Check operation status
                        async with session.get(f'http://localhost:9753/api/unified/operations/{operation_id}') as status_response:
                            if status_response.status == 200:
                                status_data = await status_response.json()
                                print(f"📈 Operation status:")
                                print(f"   🎯 Progress: {status_data.get('progress', 0)}%")
                                print(f"   📊 Status: {status_data.get('status', 'unknown')}")
                                print(f"   💬 Message: {status_data.get('message', 'no message')}")
                            else:
                                print(f"⚠️ Could not get operation status: {status_response.status}")
                    
                else:
                    error_text = await response.text()
                    print(f"❌ Upload failed with status {response.status}")
                    print(f"   Error: {error_text}")
                    return False
        
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()
            print(f"🧹 Cleaned up test file")
        
        print(f"\n🎉 UNIFIED UPLOAD FIX TEST COMPLETE!")
        print(f"✅ The config access issue has been resolved!")
        print(f"📱 File uploads are now working through the unified interface!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Clean up test file on error
        if 'test_file_path' in locals() and test_file_path.exists():
            test_file_path.unlink()
        
        return False

if __name__ == "__main__":
    asyncio.run(test_upload_fix())
