#!/usr/bin/env python3
"""
Compare entity extraction between OpenRouter Llama Maverick and Ollama MedGemma.
"""

import asyncio
import time
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

# Medical text samples for testing
MEDICAL_SAMPLES = [
    {
        "name": "Ginger Sample",
        "text": """
        Ginger (Zingiber officinale) contains several bioactive compounds including gingerols, shogaols, and paradols. 
        These compounds exhibit anti-inflammatory properties and may help reduce oxidative stress. Studies have shown 
        that ginger supplementation can improve symptoms of osteoarthritis and reduce nausea in cancer patients 
        undergoing chemotherapy. The recommended dosage is typically 1-3 grams per day. Ginger may interact with 
        anticoagulant medications such as warfarin, so patients should consult their healthcare provider before use.
        """
    },
    {
        "name": "Vitamin C Sample", 
        "text": """
        Vitamin C (ascorbic acid) is a water-soluble antioxidant that plays crucial roles in immune function, 
        collagen synthesis, and iron absorption. Deficiency can lead to scurvy, characterized by bleeding gums, 
        joint pain, and delayed wound healing. The recommended daily allowance is 90mg for men and 75mg for women. 
        High doses may cause gastrointestinal upset including diarrhea and stomach cramps. Vitamin C enhances 
        the absorption of non-heme iron from plant sources.
        """
    }
]

async def test_openrouter_llama(text: str, sample_name: str):
    """Test entity extraction using OpenRouter Llama Maverick."""
    print(f"🔄 Testing OpenRouter Llama Maverick on {sample_name}...")
    
    # Set environment for OpenRouter
    original_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
    original_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
    
    os.environ['ENTITY_EXTRACTION_PROVIDER'] = 'openrouter'
    os.environ['ENTITY_EXTRACTION_MODEL'] = 'meta-llama/llama-4-maverick'
    
    try:
        extractor = LLMEntityExtractor()
        
        start_time = time.time()
        entities = await extractor.extract_entities(text)
        end_time = time.time()
        
        extraction_time = end_time - start_time
        
        print(f"   ✅ OpenRouter Results:")
        print(f"      Time: {extraction_time:.2f} seconds")
        print(f"      Entities found: {len(entities)}")
        
        # Show sample entities
        for i, entity in enumerate(entities[:5]):
            name = entity.get('name', 'Unknown')
            entity_type = entity.get('type', 'Unknown')
            confidence = entity.get('confidence', 0)
            print(f"      {i+1}. {name} ({entity_type}) - {confidence}")
        
        return {
            'provider': 'OpenRouter Llama Maverick',
            'time': extraction_time,
            'count': len(entities),
            'entities': entities,
            'sample': sample_name
        }
        
    except Exception as e:
        print(f"   ❌ OpenRouter Error: {e}")
        return {
            'provider': 'OpenRouter Llama Maverick',
            'time': 0,
            'count': 0,
            'entities': [],
            'error': str(e),
            'sample': sample_name
        }
    finally:
        # Restore original settings
        if original_provider:
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_provider
        if original_model:
            os.environ['ENTITY_EXTRACTION_MODEL'] = original_model

async def test_ollama_medgemma(text: str, sample_name: str):
    """Test entity extraction using Ollama MedGemma."""
    print(f"\n🔄 Testing Ollama MedGemma on {sample_name}...")
    
    # Set environment for Ollama MedGemma
    original_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
    original_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
    
    os.environ['ENTITY_EXTRACTION_PROVIDER'] = 'local'
    os.environ['ENTITY_EXTRACTION_MODEL'] = 'alibayram/medgemma:4b'
    
    try:
        extractor = LLMEntityExtractor()
        
        start_time = time.time()
        entities = await extractor.extract_entities(text)
        end_time = time.time()
        
        extraction_time = end_time - start_time
        
        print(f"   ✅ MedGemma Results:")
        print(f"      Time: {extraction_time:.2f} seconds")
        print(f"      Entities found: {len(entities)}")
        
        # Show sample entities
        for i, entity in enumerate(entities[:5]):
            name = entity.get('name', 'Unknown')
            entity_type = entity.get('type', 'Unknown')
            confidence = entity.get('confidence', 0)
            print(f"      {i+1}. {name} ({entity_type}) - {confidence}")
        
        return {
            'provider': 'Ollama MedGemma',
            'time': extraction_time,
            'count': len(entities),
            'entities': entities,
            'sample': sample_name
        }
        
    except Exception as e:
        print(f"   ❌ MedGemma Error: {e}")
        return {
            'provider': 'Ollama MedGemma',
            'time': 0,
            'count': 0,
            'entities': [],
            'error': str(e),
            'sample': sample_name
        }
    finally:
        # Restore original settings
        if original_provider:
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_provider
        if original_model:
            os.environ['ENTITY_EXTRACTION_MODEL'] = original_model

def analyze_comparison(llama_results: List[Dict], medgemma_results: List[Dict]):
    """Analyze and compare the results from both models."""
    print(f"\n📊 COMPREHENSIVE COMPARISON ANALYSIS")
    print("=" * 60)
    
    # Overall statistics
    llama_total_time = sum(r.get('time', 0) for r in llama_results)
    medgemma_total_time = sum(r.get('time', 0) for r in medgemma_results)
    
    llama_total_entities = sum(r.get('count', 0) for r in llama_results)
    medgemma_total_entities = sum(r.get('count', 0) for r in medgemma_results)
    
    print(f"🏃 SPEED COMPARISON:")
    print(f"   OpenRouter Llama: {llama_total_time:.2f}s total")
    print(f"   Ollama MedGemma: {medgemma_total_time:.2f}s total")
    
    if llama_total_time > 0 and medgemma_total_time > 0:
        if llama_total_time < medgemma_total_time:
            speed_winner = "OpenRouter Llama Maverick"
            speed_diff = medgemma_total_time - llama_total_time
        else:
            speed_winner = "Ollama MedGemma"
            speed_diff = llama_total_time - medgemma_total_time
        
        print(f"   🏆 Speed Winner: {speed_winner} (by {speed_diff:.2f}s)")
    
    print(f"\n📈 ENTITY COUNT COMPARISON:")
    print(f"   OpenRouter Llama: {llama_total_entities} entities")
    print(f"   Ollama MedGemma: {medgemma_total_entities} entities")
    
    if llama_total_entities > 0 and medgemma_total_entities > 0:
        if llama_total_entities > medgemma_total_entities:
            count_winner = "OpenRouter Llama Maverick"
            count_diff = llama_total_entities - medgemma_total_entities
        else:
            count_winner = "Ollama MedGemma"
            count_diff = medgemma_total_entities - llama_total_entities
        
        print(f"   🏆 Count Winner: {count_winner} (+{count_diff} entities)")
    
    # Sample-by-sample comparison
    print(f"\n🔍 SAMPLE-BY-SAMPLE ANALYSIS:")
    for i, sample in enumerate(MEDICAL_SAMPLES):
        sample_name = sample['name']
        llama_result = llama_results[i] if i < len(llama_results) else {}
        medgemma_result = medgemma_results[i] if i < len(medgemma_results) else {}
        
        print(f"\n   📝 {sample_name}:")
        print(f"      Llama: {llama_result.get('count', 0)} entities in {llama_result.get('time', 0):.2f}s")
        print(f"      MedGemma: {medgemma_result.get('count', 0)} entities in {medgemma_result.get('time', 0):.2f}s")
    
    # Quality analysis
    print(f"\n🎯 QUALITY ANALYSIS:")
    
    # Check for medical-specific entities
    medical_terms = ['compound', 'medication', 'disease', 'symptom', 'treatment', 'nutrient', 'vitamin']
    
    llama_medical_count = 0
    medgemma_medical_count = 0
    
    for result in llama_results:
        for entity in result.get('entities', []):
            entity_type = entity.get('type', '').lower()
            if any(term in entity_type for term in medical_terms):
                llama_medical_count += 1
    
    for result in medgemma_results:
        for entity in result.get('entities', []):
            entity_type = entity.get('type', '').lower()
            if any(term in entity_type for term in medical_terms):
                medgemma_medical_count += 1
    
    print(f"   Medical Entities - Llama: {llama_medical_count}")
    print(f"   Medical Entities - MedGemma: {medgemma_medical_count}")
    
    # Final recommendation
    print(f"\n💡 RECOMMENDATION:")
    
    errors_llama = sum(1 for r in llama_results if r.get('error'))
    errors_medgemma = sum(1 for r in medgemma_results if r.get('error'))
    
    if errors_llama > 0 and errors_medgemma == 0:
        print("   🏆 Use Ollama MedGemma - OpenRouter had errors")
    elif errors_medgemma > 0 and errors_llama == 0:
        print("   🏆 Use OpenRouter Llama Maverick - MedGemma had errors")
    elif errors_llama == 0 and errors_medgemma == 0:
        # Both working, compare performance
        if medgemma_total_entities > llama_total_entities and medgemma_total_time <= llama_total_time * 1.5:
            print("   🏆 Use Ollama MedGemma - Better entity extraction, reasonable speed")
        elif llama_total_time < medgemma_total_time * 0.7:
            print("   🏆 Use OpenRouter Llama Maverick - Significantly faster")
        else:
            print("   🤝 Both are good options - consider cost and reliability preferences")
    else:
        print("   ⚠️ Both had errors - check configuration")

async def main():
    """Main comparison function."""
    print("🚀 MEDGEMMA vs LLAMA MAVERICK COMPARISON")
    print("=" * 60)
    
    llama_results = []
    medgemma_results = []
    
    # Test both models on each sample
    for sample in MEDICAL_SAMPLES:
        sample_name = sample['name']
        text = sample['text'].strip()
        
        print(f"\n📝 Testing Sample: {sample_name}")
        print(f"   Text length: {len(text)} characters")
        
        # Test OpenRouter Llama
        llama_result = await test_openrouter_llama(text, sample_name)
        llama_results.append(llama_result)
        
        # Test Ollama MedGemma
        medgemma_result = await test_ollama_medgemma(text, sample_name)
        medgemma_results.append(medgemma_result)
    
    # Analyze results
    analyze_comparison(llama_results, medgemma_results)

if __name__ == "__main__":
    asyncio.run(main())
