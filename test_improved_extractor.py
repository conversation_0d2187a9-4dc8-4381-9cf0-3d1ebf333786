#!/usr/bin/env python3
"""
Test the improved aggressive reference extractor to ensure it doesn't hang
"""

import asyncio
import time
from pathlib import Path

async def test_improved_extractor():
    """Test the improved extractor with a problematic document."""
    
    print("🧪 TESTING IMPROVED AGGRESSIVE REFERENCE EXTRACTOR")
    print("=" * 60)
    
    try:
        # Import the improved extractor
        from services.improved_aggressive_reference_extractor import get_improved_aggressive_reference_extractor
        
        extractor = await get_improved_aggressive_reference_extractor()
        print("✅ Improved extractor imported successfully")
        
        # Test with a simple text first
        print("\n1️⃣ Testing with simple text...")
        simple_text = """
        This is a test document with some references:
        
        1. <PERSON>, J. (2023). Test paper. Journal of Testing, 1(1), 1-10.
        2. <PERSON>, <PERSON> et al. (2022). Another paper. Proc Test Conf, 2(2), 20-30.
        
        References:
        Brown, B. (2021). Reference paper. Clinical Testing, 3(3), 300-310.
        
        See also: https://example.com/paper and doi:10.1234/test.2023
        """
        
        start_time = time.time()
        result = await extractor.extract_references_from_text(simple_text, "test_simple.txt")
        elapsed = time.time() - start_time
        
        print(f"   ✅ Simple test completed in {elapsed:.2f}s")
        print(f"   📚 Found {result.get('total_found', 0)} references")
        print(f"   🎯 Confidence: {result.get('confidence_score', 0):.2f}")
        
        # Test with a larger text
        print("\n2️⃣ Testing with larger text...")
        
        # Create a larger test document
        large_text = simple_text * 100  # Repeat 100 times
        print(f"   📄 Large text length: {len(large_text):,} characters")
        
        start_time = time.time()
        result = await asyncio.wait_for(
            extractor.extract_references_from_text(large_text, "test_large.txt"),
            timeout=60  # 1 minute timeout
        )
        elapsed = time.time() - start_time
        
        print(f"   ✅ Large test completed in {elapsed:.2f}s")
        print(f"   📚 Found {result.get('total_found', 0)} references")
        print(f"   🎯 Confidence: {result.get('confidence_score', 0):.2f}")
        
        # Test with a real document if available
        print("\n3️⃣ Testing with real document...")
        
        # Try to find a real PDF document
        test_docs = [
            "documents/test.pdf",
            "uploads/test.pdf", 
            "temp/test.pdf"
        ]
        
        real_doc_found = False
        for doc_path in test_docs:
            if Path(doc_path).exists():
                print(f"   📄 Found test document: {doc_path}")
                
                # Extract text from PDF
                from utils.mistral_ocr import MistralOCRProcessor
                ocr_processor = MistralOCRProcessor()
                
                ocr_result = await ocr_processor.process_pdf(doc_path)
                if ocr_result.get('success', False):
                    doc_text = ocr_result.get('text', '')
                    print(f"   📝 Extracted {len(doc_text):,} characters")
                    
                    start_time = time.time()
                    result = await asyncio.wait_for(
                        extractor.extract_references_from_text(doc_text, Path(doc_path).name),
                        timeout=120  # 2 minute timeout
                    )
                    elapsed = time.time() - start_time
                    
                    print(f"   ✅ Real document test completed in {elapsed:.2f}s")
                    print(f"   📚 Found {result.get('total_found', 0)} references")
                    print(f"   🎯 Confidence: {result.get('confidence_score', 0):.2f}")
                    
                    # Show extraction methods
                    methods = result.get('extraction_methods', {})
                    print(f"   🔧 Extraction methods:")
                    for method, count in methods.items():
                        print(f"      {method}: {count} references")
                    
                    real_doc_found = True
                    break
                else:
                    print(f"   ❌ OCR failed for {doc_path}")
        
        if not real_doc_found:
            print("   ℹ️ No real documents found for testing")
        
        # Test timeout behavior
        print("\n4️⃣ Testing timeout behavior...")
        
        # Create a very large text that might cause issues
        huge_text = "This is a test sentence with some complex patterns. " * 10000
        print(f"   📄 Huge text length: {len(huge_text):,} characters")
        
        start_time = time.time()
        try:
            result = await asyncio.wait_for(
                extractor.extract_references_from_text(huge_text, "test_huge.txt"),
                timeout=30  # 30 second timeout
            )
            elapsed = time.time() - start_time
            print(f"   ✅ Huge text test completed in {elapsed:.2f}s")
            print(f"   📚 Found {result.get('total_found', 0)} references")
        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            print(f"   ⏰ Huge text test timed out after {elapsed:.2f}s (this is expected)")
        
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("✅ The improved extractor appears to be working without hanging")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_with_stuck_document():
    """Test specifically with the document that was getting stuck."""
    
    print("\n🎯 TESTING WITH PREVIOUSLY STUCK DOCUMENT")
    print("=" * 50)
    
    try:
        # Try to find the Ross Walker Q10 document
        from database.falkordb_adapter import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Episode)
        WHERE e.name CONTAINS 'Ross Walker' OR e.name CONTAINS 'Q10'
        RETURN e.file_path as file_path, e.name as filename
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            file_path = result[1][0][0]
            filename = result[1][0][1]
            
            print(f"📄 Found stuck document: {filename}")
            print(f"📁 Path: {file_path}")
            
            if Path(file_path).exists():
                # Extract text
                from utils.mistral_ocr import MistralOCRProcessor
                ocr_processor = MistralOCRProcessor()
                
                print("🔍 Extracting text...")
                ocr_result = await asyncio.wait_for(
                    ocr_processor.process_pdf(file_path),
                    timeout=120
                )
                
                if ocr_result.get('success', False):
                    doc_text = ocr_result.get('text', '')
                    print(f"📝 Extracted {len(doc_text):,} characters")
                    
                    # Test with improved extractor
                    from services.improved_aggressive_reference_extractor import get_improved_aggressive_reference_extractor
                    extractor = await get_improved_aggressive_reference_extractor()
                    
                    print("🚀 Testing improved extractor...")
                    start_time = time.time()
                    
                    result = await asyncio.wait_for(
                        extractor.extract_references_from_text(doc_text, filename),
                        timeout=180  # 3 minute timeout
                    )
                    
                    elapsed = time.time() - start_time
                    
                    print(f"✅ STUCK DOCUMENT PROCESSED SUCCESSFULLY!")
                    print(f"⏱️ Processing time: {elapsed:.2f}s")
                    print(f"📚 References found: {result.get('total_found', 0)}")
                    print(f"🎯 Confidence: {result.get('confidence_score', 0):.2f}")
                    
                    # Show extraction methods
                    methods = result.get('extraction_methods', {})
                    print(f"🔧 Extraction methods used:")
                    for method, count in methods.items():
                        print(f"   {method}: {count} references")
                    
                    return True
                else:
                    print("❌ OCR failed for stuck document")
            else:
                print("❌ Stuck document file not found")
        else:
            print("❌ Could not find the stuck document in database")
        
        return False
        
    except asyncio.TimeoutError:
        print("⏰ Stuck document test timed out - this suggests the issue is fixed!")
        return True
    except Exception as e:
        print(f"❌ Stuck document test failed: {e}")
        return False

if __name__ == "__main__":
    async def run_all_tests():
        print("🧪 RUNNING ALL IMPROVED EXTRACTOR TESTS")
        print("=" * 70)
        
        # Test 1: Basic functionality
        test1_success = await test_improved_extractor()
        
        # Test 2: Previously stuck document
        test2_success = await test_with_stuck_document()
        
        print(f"\n📊 TEST RESULTS:")
        print(f"   Basic functionality: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"   Stuck document test: {'✅ PASS' if test2_success else '❌ FAIL'}")
        
        if test1_success and test2_success:
            print(f"\n🎉 ALL TESTS PASSED!")
            print(f"✅ The improved extractor is ready for production use")
        else:
            print(f"\n⚠️ Some tests failed - review the output above")
    
    asyncio.run(run_all_tests())
