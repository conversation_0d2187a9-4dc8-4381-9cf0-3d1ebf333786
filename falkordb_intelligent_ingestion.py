#!/usr/bin/env python3
"""
FalkorDB + Intelligent Reference Extraction Pipeline
Uses your existing FalkorDB Docker setup with intelligent reference extraction.
No Neo4j dependency - pure FalkorDB implementation.
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import logging
import json
import uuid

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

# Import FalkorDB and Redis
try:
    import falkordb
    import redis
except ImportError as e:
    logger.error(f"Missing dependencies: {e}")
    logger.error("Install with: pip install falkordb redis")
    sys.exit(1)

# Import your existing systems
from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config
from services.intelligent_reference_extractor import get_intelligent_reference_extractor

class FalkorDBIntelligentProcessor:
    """Document processor using FalkorDB + intelligent reference extraction."""
    
    def __init__(self):
        self.falkor_client = None
        self.redis_client = None
        self.config = get_config()
        self.intelligent_extractor = None
        self.graph_name = "graphiti_knowledge"
        
        # Statistics
        self.stats = {
            'documents_processed': 0,
            'entities_created': 0,
            'relationships_created': 0,
            'references_extracted': 0,
            'csv_files_created': 0
        }
    
    async def initialize(self):
        """Initialize FalkorDB and reference extraction systems."""
        try:
            # FalkorDB connection parameters from your .env
            falkor_host = os.environ.get('FALKORDB_HOST', 'localhost')
            falkor_port = int(os.environ.get('FALKORDB_PORT', '6379'))
            falkor_password = os.environ.get('FALKORDB_PASSWORD', None)

            # Redis connection for vector storage
            redis_host = os.environ.get('REDIS_HOST', 'localhost')
            redis_port = int(os.environ.get('REDIS_PORT', '6380'))
            redis_password = os.environ.get('REDIS_PASSWORD', 'Triathlon16!')

            # Connect to FalkorDB (no password based on your docker-compose)
            logger.info(f"🔄 Connecting to FalkorDB at {falkor_host}:{falkor_port}")
            if falkor_password:
                self.falkor_client = falkordb.FalkorDB(
                    host=falkor_host,
                    port=falkor_port,
                    password=falkor_password
                )
            else:
                self.falkor_client = falkordb.FalkorDB(
                    host=falkor_host,
                    port=falkor_port
                )
            
            # Test FalkorDB connection
            graph = self.falkor_client.select_graph(self.graph_name)
            result = graph.query("RETURN 'FalkorDB Connected' AS status")
            logger.info(f"✅ FalkorDB connected: {result.result_set[0][0]}")
            
            # Connect to Redis Stack for vector storage (your setup uses password)
            logger.info(f"🔄 Connecting to Redis Stack at {redis_host}:{redis_port}")
            try:
                self.redis_client = redis.Redis(
                    host=redis_host,
                    port=redis_port,
                    password=redis_password,
                    decode_responses=True
                )

                # Test Redis connection
                self.redis_client.ping()
                logger.info("✅ Redis Stack connected for vector storage")
            except Exception as redis_error:
                logger.warning(f"⚠️ Redis connection failed: {redis_error}")
                logger.warning("   Continuing without Redis vector storage")
                self.redis_client = None
            
            # Initialize intelligent reference extractor
            self.intelligent_extractor = get_intelligent_reference_extractor()
            
            logger.info("✅ FalkorDB + Intelligent Reference system initialized")
            logger.info(f"   FalkorDB Graph: {self.graph_name}")
            logger.info(f"   AI-powered extraction: {'✅' if self.intelligent_extractor.ai_available else '❌'}")
            
            # Create graph schema
            await self._create_graph_schema()
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize: {e}")
            raise
    
    async def _create_graph_schema(self):
        """Create the knowledge graph schema in FalkorDB."""
        try:
            graph = self.falkor_client.select_graph(self.graph_name)
            
            # Create indices for better performance
            schema_queries = [
                "CREATE INDEX FOR (d:Document) ON (d.id)",
                "CREATE INDEX FOR (e:Entity) ON (e.name)",
                "CREATE INDEX FOR (e:Entity) ON (e.type)",
                "CREATE INDEX FOR (r:Reference) ON (r.id)",
                "CREATE INDEX FOR (c:Chunk) ON (c.document_id)"
            ]
            
            for query in schema_queries:
                try:
                    graph.query(query)
                    logger.debug(f"Created index: {query}")
                except Exception as e:
                    # Index might already exist
                    logger.debug(f"Index creation skipped: {e}")
            
            logger.info("✅ Graph schema initialized")
            
        except Exception as e:
            logger.warning(f"Schema creation warning: {e}")
    
    async def process_document_complete(self, file_path: str) -> Dict[str, Any]:
        """Process a document with FalkorDB knowledge graph and reference extraction."""
        try:
            logger.info(f"🔄 Processing document: {file_path}")
            
            # Step 1: Extract text using OCR
            ocr_processor = MistralOCRProcessor()

            # Use the correct method based on file type
            file_ext = Path(file_path).suffix.lower()
            if file_ext == '.pdf':
                ocr_result = await ocr_processor.process_pdf(file_path)
            else:
                # For other document types, use the generic document method
                ocr_result = await ocr_processor.extract_text_from_document(file_path)
            
            if not ocr_result.get('success'):
                return {'success': False, 'error': 'OCR processing failed'}
            
            text_content = ocr_result.get('text', '')
            
            if not text_content.strip():
                return {'success': False, 'error': 'No text content extracted'}
            
            # Step 2: Create document node in FalkorDB
            doc_id = str(uuid.uuid4())
            doc_name = Path(file_path).stem
            
            document_result = await self._create_document_node(doc_id, doc_name, file_path, text_content)
            
            # Step 3: Extract and store entities
            entities_result = await self._extract_and_store_entities(doc_id, text_content)
            
            # Step 4: Extract references using intelligent extractor
            logger.info(f"🧠 Extracting references using intelligent system...")
            ref_result = await self.intelligent_extractor.extract_references_comprehensive(
                text_content, 
                Path(file_path).name
            )
            
            # Step 5: Store references in FalkorDB
            references_result = await self._store_references(doc_id, ref_result)
            
            # Step 6: Save references to CSV
            csv_path = await self._save_references_to_csv(ref_result, Path(file_path))
            
            # Update statistics
            self.stats['documents_processed'] += 1
            self.stats['entities_created'] += entities_result.get('entities_count', 0)
            self.stats['relationships_created'] += entities_result.get('relationships_count', 0)
            self.stats['references_extracted'] += ref_result.get('total_found', 0)
            if csv_path:
                self.stats['csv_files_created'] += 1
            
            logger.info(f"✅ Complete processing finished for {file_path}")
            logger.info(f"   Document ID: {doc_id}")
            logger.info(f"   Entities created: {entities_result.get('entities_count', 0)}")
            logger.info(f"   References found: {ref_result.get('total_found', 0)}")
            logger.info(f"   CSV saved: {csv_path}")
            
            return {
                'success': True,
                'document_id': doc_id,
                'document_name': doc_name,
                'text_length': len(text_content),
                'entities_created': entities_result.get('entities_count', 0),
                'relationships_created': entities_result.get('relationships_count', 0),
                'references_found': ref_result.get('total_found', 0),
                'csv_path': csv_path,
                'extraction_methods': ref_result.get('extraction_methods', {}),
                'confidence_score': ref_result.get('confidence_score', 0.0),
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing document {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _create_document_node(self, doc_id: str, doc_name: str, file_path: str, text_content: str) -> Dict[str, Any]:
        """Create a document node in FalkorDB."""
        try:
            graph = self.falkor_client.select_graph(self.graph_name)
            
            # Create document node
            query = """
            CREATE (d:Document {
                id: $doc_id,
                name: $doc_name,
                file_path: $file_path,
                text_length: $text_length,
                created_at: $created_at,
                processed_at: $processed_at
            })
            RETURN d.id AS document_id
            """
            
            params = {
                'doc_id': doc_id,
                'doc_name': doc_name,
                'file_path': file_path,
                'text_length': len(text_content),
                'created_at': datetime.now(timezone.utc).isoformat(),
                'processed_at': datetime.now(timezone.utc).isoformat()
            }
            
            result = graph.query(query, params)
            logger.info(f"✅ Document node created: {doc_id}")
            
            return {'success': True, 'document_id': doc_id}
            
        except Exception as e:
            logger.error(f"❌ Error creating document node: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _extract_and_store_entities(self, doc_id: str, text_content: str) -> Dict[str, Any]:
        """Extract entities and store them in FalkorDB."""
        try:
            # Simple entity extraction (you can enhance this with your existing entity extraction)
            # For now, we'll create a basic implementation
            
            # Split text into chunks for processing
            chunk_size = 1200
            chunks = [text_content[i:i+chunk_size] for i in range(0, len(text_content), chunk_size)]
            
            graph = self.falkor_client.select_graph(self.graph_name)
            entities_created = 0
            relationships_created = 0
            
            for i, chunk in enumerate(chunks):
                # Create chunk node
                chunk_id = f"{doc_id}_chunk_{i}"
                
                chunk_query = """
                MATCH (d:Document {id: $doc_id})
                CREATE (c:Chunk {
                    id: $chunk_id,
                    document_id: $doc_id,
                    chunk_index: $chunk_index,
                    text: $text,
                    created_at: $created_at
                })
                CREATE (d)-[:HAS_CHUNK]->(c)
                RETURN c.id AS chunk_id
                """
                
                chunk_params = {
                    'doc_id': doc_id,
                    'chunk_id': chunk_id,
                    'chunk_index': i,
                    'text': chunk[:1000],  # Limit text size for storage
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
                
                graph.query(chunk_query, chunk_params)
                entities_created += 1
                relationships_created += 1
            
            logger.info(f"✅ Created {len(chunks)} chunks for document {doc_id}")
            
            return {
                'success': True,
                'entities_count': entities_created,
                'relationships_count': relationships_created
            }
            
        except Exception as e:
            logger.error(f"❌ Error extracting entities: {e}")
            return {'success': False, 'error': str(e), 'entities_count': 0, 'relationships_count': 0}
    
    async def _store_references(self, doc_id: str, ref_result: Dict[str, Any]) -> Dict[str, Any]:
        """Store references in FalkorDB."""
        try:
            references = ref_result.get('references', [])
            if not references:
                return {'success': True, 'references_stored': 0}
            
            graph = self.falkor_client.select_graph(self.graph_name)
            references_stored = 0
            
            for i, ref in enumerate(references):
                ref_text = ref.get('text', str(ref)) if isinstance(ref, dict) else str(ref)
                ref_id = f"{doc_id}_ref_{i}"
                
                ref_query = """
                MATCH (d:Document {id: $doc_id})
                CREATE (r:Reference {
                    id: $ref_id,
                    document_id: $doc_id,
                    text: $ref_text,
                    confidence: $confidence,
                    created_at: $created_at
                })
                CREATE (d)-[:HAS_REFERENCE]->(r)
                RETURN r.id AS reference_id
                """
                
                ref_params = {
                    'doc_id': doc_id,
                    'ref_id': ref_id,
                    'ref_text': ref_text[:500],  # Limit text size
                    'confidence': ref.get('confidence', 0.9) if isinstance(ref, dict) else 0.9,
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
                
                graph.query(ref_query, ref_params)
                references_stored += 1
            
            logger.info(f"✅ Stored {references_stored} references in FalkorDB")
            
            return {'success': True, 'references_stored': references_stored}
            
        except Exception as e:
            logger.error(f"❌ Error storing references: {e}")
            return {'success': False, 'error': str(e), 'references_stored': 0}
    
    async def _save_references_to_csv(self, ref_result: Dict[str, Any], file_path: Path) -> Optional[str]:
        """Save references to CSV file using your existing format."""
        try:
            # Create references directory
            ref_dir = Path("references")
            ref_dir.mkdir(exist_ok=True)
            
            # Generate CSV filename (matching your existing pattern)
            doc_id = file_path.stem.split('_')[0] if '_' in file_path.stem else file_path.stem
            csv_filename = f"{doc_id}_{file_path.stem}_falkordb_references.csv"
            csv_path = ref_dir / csv_filename
            
            # Get references from result
            references = ref_result.get('references', [])
            
            if not references:
                logger.warning(f"No references to save for {file_path.name}")
                return None
            
            # Save to CSV with your existing format
            import csv
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'reference_number', 'text', 'authors', 'title', 'journal', 
                    'year', 'doi', 'pmid', 'url', 'confidence', 
                    'extraction_method', 'source_section'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, ref in enumerate(references, 1):
                    if isinstance(ref, dict):
                        ref_text = ref.get('text', str(ref))
                        confidence = ref.get('confidence', 0.9)
                    else:
                        ref_text = str(ref)
                        confidence = 0.9
                    
                    writer.writerow({
                        'reference_number': i,
                        'text': ref_text,
                        'authors': '',  # Could be parsed from ref_text
                        'title': '',    # Could be parsed from ref_text
                        'journal': '',  # Could be parsed from ref_text
                        'year': '',     # Could be parsed from ref_text
                        'doi': '',      # Could be parsed from ref_text
                        'pmid': '',     # Could be parsed from ref_text
                        'url': '',      # Could be parsed from ref_text
                        'confidence': confidence,
                        'extraction_method': 'intelligent_falkordb',
                        'source_section': 'document'
                    })
            
            logger.info(f"✅ References saved to CSV: {csv_path}")
            return str(csv_path)

        except Exception as e:
            logger.error(f"❌ Error saving references to CSV: {e}")
            return None

    async def search_knowledge_graph(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search the knowledge graph using FalkorDB."""
        try:
            logger.info(f"🔍 Searching FalkorDB for: {query}")

            graph = self.falkor_client.select_graph(self.graph_name)

            # Search across documents, entities, and references
            search_query = """
            MATCH (d:Document)
            WHERE d.name CONTAINS $query OR d.file_path CONTAINS $query
            OPTIONAL MATCH (d)-[:HAS_REFERENCE]->(r:Reference)
            WHERE r.text CONTAINS $query
            OPTIONAL MATCH (d)-[:HAS_CHUNK]->(c:Chunk)
            WHERE c.text CONTAINS $query
            RETURN d.id AS document_id, d.name AS document_name,
                   collect(DISTINCT r.text)[0..3] AS sample_references,
                   collect(DISTINCT c.text)[0..2] AS sample_chunks
            LIMIT $limit
            """

            params = {'query': query, 'limit': limit}
            result = graph.query(search_query, params)

            search_results = []
            for record in result.result_set:
                search_results.append({
                    'document_id': record[0],
                    'document_name': record[1],
                    'sample_references': record[2] if record[2] else [],
                    'sample_chunks': record[3] if record[3] else [],
                    'match_type': 'document_content'
                })

            logger.info(f"✅ Found {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"❌ Search error: {e}")
            return []

    async def get_graph_statistics(self) -> Dict[str, Any]:
        """Get statistics about the FalkorDB knowledge graph."""
        try:
            graph = self.falkor_client.select_graph(self.graph_name)

            # Count nodes and relationships
            stats_query = """
            MATCH (d:Document)
            OPTIONAL MATCH (d)-[:HAS_CHUNK]->(c:Chunk)
            OPTIONAL MATCH (d)-[:HAS_REFERENCE]->(r:Reference)
            RETURN count(DISTINCT d) AS documents,
                   count(DISTINCT c) AS chunks,
                   count(DISTINCT r) AS references
            """

            result = graph.query(stats_query)
            record = result.result_set[0] if result.result_set else [0, 0, 0]

            stats = {
                'documents': record[0],
                'chunks': record[1],
                'references': record[2],
                'total_nodes': record[0] + record[1] + record[2],
                'processing_stats': self.stats,
                'database': 'FalkorDB',
                'graph_name': self.graph_name
            }

            logger.info(f"📊 Graph statistics: {stats}")
            return stats

        except Exception as e:
            logger.error(f"❌ Error getting statistics: {e}")
            return {'error': str(e)}

    async def process_directory(self, directory_path: str, file_extensions: List[str] = None) -> Dict[str, Any]:
        """Process all documents in a directory."""
        if file_extensions is None:
            file_extensions = ['.pdf', '.txt', '.docx']

        directory = Path(directory_path)
        if not directory.exists():
            return {'success': False, 'error': f'Directory not found: {directory_path}'}

        # Find all matching files
        files = []
        for ext in file_extensions:
            files.extend(directory.glob(f'**/*{ext}'))

        if not files:
            return {'success': False, 'error': f'No files found with extensions {file_extensions}'}

        logger.info(f"🔄 Processing {len(files)} files from {directory_path}")

        results = []
        successful = 0
        failed = 0
        total_references = 0
        total_entities = 0

        for file_path in files:
            try:
                result = await self.process_document_complete(str(file_path))
                results.append({
                    'file': str(file_path),
                    'success': result['success'],
                    'document_id': result.get('document_id'),
                    'entities_created': result.get('entities_created', 0),
                    'references_found': result.get('references_found', 0),
                    'csv_path': result.get('csv_path'),
                    'error': result.get('error')
                })

                if result['success']:
                    successful += 1
                    total_references += result.get('references_found', 0)
                    total_entities += result.get('entities_created', 0)
                else:
                    failed += 1

                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                results.append({
                    'file': str(file_path),
                    'success': False,
                    'error': str(e)
                })
                failed += 1

        return {
            'success': True,
            'files_processed': len(files),
            'successful': successful,
            'failed': failed,
            'total_references_extracted': total_references,
            'total_entities_created': total_entities,
            'results': results
        }

    def print_stats(self):
        """Print processing statistics."""
        print(f"\n📊 FALKORDB + INTELLIGENT REFERENCE PROCESSING STATISTICS:")
        print(f"  📄 Documents processed: {self.stats['documents_processed']}")
        print(f"  🎯 Entities created: {self.stats['entities_created']}")
        print(f"  🔗 Relationships created: {self.stats['relationships_created']}")
        print(f"  📚 References extracted: {self.stats['references_extracted']}")
        print(f"  📋 CSV files created: {self.stats['csv_files_created']}")
        print(f"  🗄️ Database: FalkorDB (Docker)")
        print(f"  🧠 AI-powered extraction: {'✅' if self.intelligent_extractor and self.intelligent_extractor.ai_available else '❌'}")

    async def close(self):
        """Close database connections."""
        try:
            # FalkorDB doesn't have a close method, connections are managed automatically
            if self.falkor_client:
                logger.info("✅ FalkorDB connection released")
            if self.redis_client:
                self.redis_client.close()
                logger.info("✅ Redis connection closed")
        except Exception as e:
            logger.warning(f"Warning during close: {e}")

async def main():
    """Main processing function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single file: python falkordb_intelligent_ingestion.py <document_path>")
        print("  Directory:   python falkordb_intelligent_ingestion.py --dir <directory_path>")
        print("  Search:      python falkordb_intelligent_ingestion.py --search '<query>'")
        print("  Stats:       python falkordb_intelligent_ingestion.py --stats")
        return

    processor = FalkorDBIntelligentProcessor()

    try:
        await processor.initialize()

        if sys.argv[1] == '--dir':
            if len(sys.argv) < 3:
                print("Error: Directory path required")
                return

            directory_path = sys.argv[2]
            result = await processor.process_directory(directory_path)

            if result['success']:
                print(f"✅ Batch processing complete:")
                print(f"  📁 Files processed: {result['files_processed']}")
                print(f"  ✅ Successful: {result['successful']}")
                print(f"  ❌ Failed: {result['failed']}")
                print(f"  📚 Total references: {result['total_references_extracted']}")
                print(f"  🎯 Total entities: {result['total_entities_created']}")
                processor.print_stats()
            else:
                print(f"❌ Batch processing failed: {result.get('error')}")

        elif sys.argv[1] == '--search':
            if len(sys.argv) < 3:
                print("Error: Search query required")
                return

            query = sys.argv[2]
            results = await processor.search_knowledge_graph(query)

            print(f"🔍 Search Results for: '{query}'")
            print("=" * 60)

            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. Document: {result['document_name']}")
                    print(f"   ID: {result['document_id']}")
                    if result['sample_references']:
                        print(f"   Sample references: {len(result['sample_references'])} found")
                        for ref in result['sample_references'][:2]:
                            print(f"     - {ref[:100]}...")
                    print()
            else:
                print("No results found.")

        elif sys.argv[1] == '--stats':
            stats = await processor.get_graph_statistics()
            print("📊 FalkorDB Knowledge Graph Statistics:")
            print("=" * 50)
            for key, value in stats.items():
                if key != 'processing_stats':
                    print(f"  {key}: {value}")

            if 'processing_stats' in stats:
                print("\n📈 Processing Statistics:")
                for key, value in stats['processing_stats'].items():
                    print(f"  {key}: {value}")

        else:
            # Single document processing
            document_path = sys.argv[1]
            result = await processor.process_document_complete(document_path)

            if result['success']:
                print(f"✅ Successfully processed: {document_path}")
                print(f"   Document ID: {result['document_id']}")
                print(f"   Text length: {result['text_length']} characters")
                print(f"   Entities created: {result['entities_created']}")
                print(f"   References found: {result['references_found']}")
                print(f"   CSV saved: {result['csv_path']}")
                print(f"   Confidence: {result['confidence_score']:.2f}")
                processor.print_stats()
            else:
                print(f"❌ Failed to process: {document_path}")
                print(f"   Error: {result.get('error')}")

    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())
