#!/usr/bin/env python3
"""
Fix entity extraction for existing facts.
This script will run entity extraction on existing facts that don't have entity mentions.
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from services.entity_extraction_service import extract_entities_from_text
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def fix_entity_extraction():
    """Run entity extraction on existing facts that don't have entity mentions."""
    
    try:
        adapter = GraphitiFalkorDBAdapter('graphiti')
        
        # Get facts that don't have entity mentions
        query = '''
        MATCH (f:Fact)
        WHERE NOT (f)-[:MENTIONS]->(:Entity)
        RETURN f.uuid as fact_uuid, f.body as fact_text, f.episode_id as episode_id
        '''
        
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or len(result[1]) == 0:
            logger.info("No facts without entity mentions found")
            return
        
        logger.info(f"Found {len(result[1])} facts without entity mentions")
        
        total_entities_extracted = 0
        
        for row in result[1]:
            fact_uuid = row[0]
            fact_text = row[1]
            episode_id = row[2] if len(row) > 2 else None
            
            logger.info(f"Processing fact: {fact_uuid}")
            logger.info(f"Text sample: {fact_text[:100]}...")
            
            try:
                # Extract entities with proper fact_id
                entities = await extract_entities_from_text(
                    text=fact_text,
                    document_id=episode_id,
                    fact_id=fact_uuid,
                    llm_provider='openrouter'
                )
                
                logger.info(f"Extracted {len(entities)} entities from fact {fact_uuid}")
                total_entities_extracted += len(entities)
                
                for entity in entities:
                    logger.info(f"  - {entity.get('name', 'Unknown')} ({entity.get('type', 'Unknown')})")
                
            except Exception as e:
                logger.error(f"Error extracting entities from fact {fact_uuid}: {e}")
                continue
        
        logger.info(f"✅ Total entities extracted: {total_entities_extracted}")
        
        # Verify the results
        count_query = 'MATCH (e:Entity) RETURN count(e) as count'
        count_result = adapter.execute_cypher(count_query)
        
        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            entity_count = count_result[1][0][0]
            logger.info(f"✅ Total entities in database: {entity_count}")
        
        # Check mentions relationships
        mentions_query = 'MATCH ()-[r:MENTIONS]->() RETURN count(r) as count'
        mentions_result = adapter.execute_cypher(mentions_query)
        
        if mentions_result and len(mentions_result) > 1 and len(mentions_result[1]) > 0:
            mentions_count = mentions_result[1][0][0]
            logger.info(f"✅ Total MENTIONS relationships: {mentions_count}")
        
    except Exception as e:
        logger.error(f"Error in entity extraction fix: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(fix_entity_extraction())
