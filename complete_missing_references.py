#!/usr/bin/env python3
"""
Complete the missing reference processing for the remaining 33 documents
"""

import asyncio
import os
import csv
from pathlib import Path
from datetime import datetime

async def process_missing_references():
    """Process the remaining documents that don't have aggressive CSV files"""
    
    print("🔧 COMPLETING MISSING REFERENCE PROCESSING")
    print("=" * 60)
    
    try:
        # Get all documents from database
        from database.falkordb_adapter import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Episode)
        RETURN e.uuid as document_id, 
               e.name as filename, 
               e.file_path as file_path
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        
        documents = []
        if result and len(result) > 1:
            for row in result[1]:
                doc_data = {
                    'document_id': row[0] if len(row) > 0 else '',
                    'filename': row[1] if len(row) > 1 else '',
                    'file_path': row[2] if len(row) > 2 else ''
                }
                documents.append(doc_data)
        
        print(f"📄 Found {len(documents)} total documents in database")
        
        # Check which documents already have aggressive CSV files
        ref_dir = Path("references")
        ref_dir.mkdir(exist_ok=True)
        
        existing_files = set()
        for csv_file in ref_dir.glob("*_aggressive_references.csv"):
            # Extract document ID from filename (first part before underscore)
            doc_id = csv_file.name.split('_')[0]
            existing_files.add(doc_id)
        
        print(f"📁 Found {len(existing_files)} existing aggressive CSV files")
        
        # Find documents that need processing
        missing_docs = []
        for doc in documents:
            doc_id = doc['document_id']
            if doc_id not in existing_files and doc['file_path']:
                missing_docs.append(doc)
        
        print(f"❌ Found {len(missing_docs)} documents missing aggressive CSV files")
        
        if not missing_docs:
            print("✅ All documents already have aggressive CSV files!")
            return
        
        # Show what we'll process
        print(f"\n📋 DOCUMENTS TO PROCESS:")
        for i, doc in enumerate(missing_docs[:10], 1):
            filename = doc['filename'][:50] + "..." if len(doc['filename']) > 50 else doc['filename']
            print(f"   {i:2d}. {filename}")
        
        if len(missing_docs) > 10:
            print(f"   ... and {len(missing_docs) - 10} more documents")
        
        print(f"\n🚀 Starting processing of {len(missing_docs)} documents...")
        
        # Initialize components
        from services.aggressive_reference_extractor import get_aggressive_reference_extractor
        from utils.mistral_ocr import MistralOCRProcessor
        
        extractor = await get_aggressive_reference_extractor()
        ocr_processor = MistralOCRProcessor()
        
        # Process each document
        results = []
        successful_count = 0
        total_new_refs = 0
        
        for i, doc in enumerate(missing_docs, 1):
            print(f"\n📖 Processing {i}/{len(missing_docs)}: {doc['filename'][:40]}...")
            
            try:
                file_path = doc['file_path']
                if not file_path or not os.path.exists(file_path):
                    print(f"   ⚠️ File not found, skipping")
                    results.append({
                        'document_id': doc['document_id'],
                        'filename': doc['filename'],
                        'status': 'file_not_found',
                        'references_found': 0
                    })
                    continue
                
                # Extract text with timeout
                print(f"   🔍 Extracting text...")
                try:
                    ocr_result = await asyncio.wait_for(
                        ocr_processor.process_pdf(file_path),
                        timeout=120  # 2 minute timeout
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ OCR timeout, skipping")
                    results.append({
                        'document_id': doc['document_id'],
                        'filename': doc['filename'],
                        'status': 'ocr_timeout',
                        'references_found': 0
                    })
                    continue
                
                if not ocr_result.get('success', False):
                    print(f"   ❌ OCR failed, skipping")
                    results.append({
                        'document_id': doc['document_id'],
                        'filename': doc['filename'],
                        'status': 'ocr_failed',
                        'references_found': 0
                    })
                    continue
                
                extracted_text = ocr_result.get('text', '')
                print(f"   📝 Extracted {len(extracted_text):,} characters")
                
                if len(extracted_text) < 100:
                    print(f"   ⚠️ Text too short, skipping")
                    results.append({
                        'document_id': doc['document_id'],
                        'filename': doc['filename'],
                        'status': 'text_too_short',
                        'references_found': 0
                    })
                    continue
                
                # Extract references with timeout
                print(f"   🚀 Extracting references...")
                try:
                    ref_result = await asyncio.wait_for(
                        extractor.extract_references_from_text(extracted_text, doc['filename']),
                        timeout=90  # 90 second timeout
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ Reference extraction timeout, skipping")
                    results.append({
                        'document_id': doc['document_id'],
                        'filename': doc['filename'],
                        'status': 'extraction_timeout',
                        'references_found': 0
                    })
                    continue
                
                references_found = ref_result.get('total_found', 0)
                confidence = ref_result.get('confidence_score', 0.0)
                
                # Save to CSV
                csv_filename = f"{doc['document_id']}_{Path(file_path).stem}_aggressive_references.csv"
                csv_path = ref_dir / csv_filename
                
                await save_references_to_csv(ref_result, csv_path)
                
                print(f"   ✅ Found {references_found} references (confidence: {confidence:.2f})")
                print(f"   💾 Saved to: {csv_filename}")
                
                results.append({
                    'document_id': doc['document_id'],
                    'filename': doc['filename'],
                    'status': 'success',
                    'references_found': references_found,
                    'confidence_score': confidence,
                    'csv_path': str(csv_path)
                })
                
                successful_count += 1
                total_new_refs += references_found
                
                # Brief pause every 3 documents
                if i % 3 == 0:
                    print(f"   ⏸️ Brief pause...")
                    await asyncio.sleep(3)
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                results.append({
                    'document_id': doc['document_id'],
                    'filename': doc['filename'],
                    'status': 'error',
                    'references_found': 0,
                    'error': str(e)
                })
                continue
        
        # Final summary
        print(f"\n🎉 PROCESSING COMPLETE!")
        print(f"   ✅ Successfully processed: {successful_count}/{len(missing_docs)} documents")
        print(f"   📚 New references found: {total_new_refs}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"missing_references_processing_{timestamp}.json"
        
        import json
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'total_documents': len(missing_docs),
                'successful_count': successful_count,
                'total_references_found': total_new_refs,
                'results': results
            }, f, indent=2)
        
        print(f"   💾 Results saved to: {results_file}")
        
        # Check final reference count
        await check_final_reference_count()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def save_references_to_csv(ref_result, csv_path):
    """Save reference results to CSV file"""
    try:
        detailed_refs = ref_result.get('detailed_references', [])
        
        if not detailed_refs:
            # Fallback to simple references
            simple_refs = ref_result.get('references', [])
            detailed_refs = [{'text': ref, 'extraction_method': 'aggressive'} for ref in simple_refs]
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'reference_number', 'text', 'authors', 'title', 'journal', 
                'year', 'doi', 'pmid', 'url', 'confidence', 
                'extraction_method', 'source_section'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for i, ref in enumerate(detailed_refs, 1):
                writer.writerow({
                    'reference_number': ref.get('number', i),
                    'text': ref.get('text', ''),
                    'authors': ref.get('authors', ''),
                    'title': ref.get('title', ''),
                    'journal': ref.get('journal', ''),
                    'year': ref.get('year', ''),
                    'doi': ref.get('doi', ''),
                    'pmid': ref.get('pmid', ''),
                    'url': ref.get('url', ''),
                    'confidence': ref.get('confidence', 0.0),
                    'extraction_method': ref.get('extraction_method', 'aggressive'),
                    'source_section': ref.get('source_section', 'document')
                })
        
    except Exception as e:
        print(f"   ⚠️ Error saving CSV: {e}")

async def check_final_reference_count():
    """Check the final reference count after processing"""
    print(f"\n📊 FINAL REFERENCE COUNT CHECK:")
    
    ref_dir = Path("references")
    total_refs = 0
    aggressive_refs = 0
    file_count = 0
    
    if ref_dir.exists():
        for csv_file in ref_dir.glob("*.csv"):
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    ref_count = len(rows) - 1  # Subtract header
                    
                    if ref_count > 0:
                        total_refs += ref_count
                        file_count += 1
                        
                        if 'aggressive' in csv_file.name:
                            aggressive_refs += ref_count
                            
            except Exception:
                continue
    
    print(f"   📁 Total CSV files: {file_count}")
    print(f"   🚀 Aggressive references: {aggressive_refs}")
    print(f"   📚 Total references: {total_refs}")
    print(f"   🎯 Target (from batch): 1,043 references")
    
    if total_refs >= 1000:
        print(f"   🎉 SUCCESS! We've captured most/all references!")
    else:
        remaining = 1043 - total_refs
        print(f"   ⚠️ Still missing ~{remaining} references")
    
    # Test API response
    try:
        import requests
        response = requests.get("http://localhost:9753/api/fast/graph-stats", timeout=10)
        if response.status_code == 200:
            api_data = response.json()
            api_refs = api_data.get('total_references', 0)
            print(f"   📱 API will show: {api_refs} references (after refresh)")
    except:
        print(f"   📱 API check failed - refresh dashboard to see updated count")

if __name__ == "__main__":
    asyncio.run(process_missing_references())
