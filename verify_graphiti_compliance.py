#!/usr/bin/env python3
"""
Comprehensive verification script to check if entities, facts, episodes, nodes/edges/relationships
are being created correctly according to Graphiti base specifications and stored properly in FalkorDB and Redis.
"""

import asyncio
import json
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.redis_vector_search import get_redis_vector_search_client
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class GraphitiComplianceVerifier:
    """Verifies that the current implementation follows Graphiti specifications."""
    
    def __init__(self):
        self.falkor_adapter = None
        self.redis_client = None
        self.issues = []
        self.compliance_score = 0
        
    async def initialize(self):
        """Initialize database connections."""
        try:
            self.falkor_adapter = GraphitiFalkorDBAdapter('knowledge_graph')
            self.redis_client = get_redis_vector_search_client()
            logger.info("✅ Database connections initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize databases: {e}")
            raise
    
    def add_issue(self, category: str, severity: str, description: str, recommendation: str = ""):
        """Add a compliance issue."""
        self.issues.append({
            "category": category,
            "severity": severity,  # CRITICAL, HIGH, MEDIUM, LOW
            "description": description,
            "recommendation": recommendation,
            "timestamp": datetime.now().isoformat()
        })
    
    def verify_graphiti_node_structure(self):
        """Verify that nodes follow Graphiti Node base class specifications."""
        print("\n🔍 VERIFYING GRAPHITI NODE STRUCTURE")
        print("=" * 60)

        # According to Graphiti specs, all nodes should have:
        # - uuid: str
        # - name: str
        # - group_id: str
        # - labels: list[str]
        # - created_at: datetime

        try:
            # Check Entity nodes with simpler query
            result = self.falkor_adapter.execute_cypher("MATCH (e:Entity) RETURN e LIMIT 5")

            if not result:
                self.add_issue(
                    "Node Structure",
                    "CRITICAL",
                    "No query result returned for Entity nodes",
                    "Check FalkorDB connection and query execution"
                )
                return

            # Parse FalkorDB result format: [header, [entity_list], metadata]
            entity_count = 0
            if len(result) >= 2 and isinstance(result[1], list):
                entity_list = result[1]

                for entity_wrapper in entity_list:
                    if isinstance(entity_wrapper, list) and len(entity_wrapper) > 0:
                        entity_count += 1
                        entity_data = entity_wrapper[0]  # Get the actual entity data

                        # Extract properties from FalkorDB format
                        properties = {}
                        if isinstance(entity_data, list) and len(entity_data) >= 3:
                            # Parse properties: [['name', 'value'], ['type', 'value'], ...]
                            if len(entity_data) > 2 and len(entity_data[2]) > 1:
                                prop_list = entity_data[2][1]  # ['properties', [list]]
                                for prop in prop_list:
                                    if isinstance(prop, list) and len(prop) >= 2:
                                        properties[prop[0]] = prop[1]

                        if entity_count <= 5:  # Show details for first 5 entities
                            print(f"  Entity {entity_count}:")
                            print(f"    UUID: {'✅' if 'uuid' in properties else '❌'}")
                            print(f"    Name: {'✅' if 'name' in properties else '❌'}")
                            print(f"    Group ID: {'✅' if 'group_id' in properties else '❌'}")
                            print(f"    Created At: {'✅' if 'created_at' in properties else '❌'}")

                        # Check for missing Graphiti required fields (only report once per type)
                        if entity_count == 1:  # Only check first entity to avoid duplicate issues
                            if 'group_id' not in properties:
                                self.add_issue(
                                    "Node Structure",
                                    "HIGH",
                                    "Entity nodes missing required 'group_id' field from Graphiti Node specification",
                                    "Add group_id field to all Entity nodes for proper Graphiti compliance"
                                )

                            if 'uuid' not in properties:
                                self.add_issue(
                                    "Node Structure",
                                    "CRITICAL",
                                    "Entity nodes missing required 'uuid' field from Graphiti Node specification",
                                    "Ensure all nodes have UUID field for proper identification"
                                )

            print(f"📊 Found {entity_count} Entity records")

            if entity_count == 0:
                self.add_issue(
                    "Node Structure",
                    "CRITICAL",
                    "No Entity nodes found in database",
                    "Ensure document processing creates Entity nodes according to Graphiti specifications"
                )

        except Exception as e:
            self.add_issue(
                "Node Structure",
                "CRITICAL",
                f"Failed to verify Entity node structure: {e}",
                "Check FalkorDB connection and Entity node creation process"
            )
    
    def verify_graphiti_episode_structure(self):
        """Verify EpisodicNode structure according to Graphiti specs."""
        print("\n🔍 VERIFYING EPISODIC NODE STRUCTURE")
        print("=" * 60)
        
        # According to Graphiti specs, EpisodicNode should have:
        # - All Node fields (uuid, name, group_id, labels, created_at)
        # - source: EpisodeType
        # - source_description: str
        # - content: str
        # - valid_at: datetime
        # - entity_edges: list[str]
        
        try:
            result = self.falkor_adapter.execute_cypher("""
                MATCH (ep:Episode) 
                RETURN ep
                LIMIT 5
            """)
            
            episode_count = len(result) - 1 if result and len(result) > 1 else 0
            print(f"📊 Found {episode_count} Episode records")
            
            if episode_count == 0:
                self.add_issue(
                    "Episode Structure",
                    "CRITICAL",
                    "No Episode/Episodic nodes found in database",
                    "Document processing should create EpisodicNode instances according to Graphiti specifications"
                )
            else:
                print("✅ Episode nodes exist - checking structure...")
                # Additional structure verification would go here
                
        except Exception as e:
            self.add_issue(
                "Episode Structure", 
                "CRITICAL",
                f"Failed to verify Episode structure: {e}",
                "Check Episode node creation in document processing pipeline"
            )
    
    def verify_graphiti_fact_structure(self):
        """Verify that Facts are properly structured."""
        print("\n🔍 VERIFYING FACT STRUCTURE")
        print("=" * 60)
        
        try:
            result = self.falkor_adapter.execute_cypher("""
                MATCH (f:Fact)
                RETURN f
                LIMIT 5
            """)
            
            fact_count = len(result) - 1 if result and len(result) > 1 else 0
            print(f"📊 Found {fact_count} Fact records")
            
            if fact_count == 0:
                self.add_issue(
                    "Fact Structure",
                    "CRITICAL", 
                    "No Fact nodes found in database",
                    "Document processing should create Fact nodes for text chunks"
                )
            else:
                print("✅ Fact nodes exist - checking structure...")
                
        except Exception as e:
            self.add_issue(
                "Fact Structure",
                "CRITICAL",
                f"Failed to verify Fact structure: {e}",
                "Check Fact node creation in document processing pipeline"
            )
    
    def verify_graphiti_edge_structure(self):
        """Verify Edge/Relationship structure according to Graphiti specs."""
        print("\n🔍 VERIFYING EDGE/RELATIONSHIP STRUCTURE")
        print("=" * 60)
        
        # According to Graphiti specs, edges should have:
        # - uuid: str
        # - group_id: str  
        # - source_node_uuid: str
        # - target_node_uuid: str
        # - created_at: datetime
        
        try:
            result = self.falkor_adapter.execute_cypher("""
                MATCH ()-[r]->()
                RETURN type(r), count(r)
            """)
            
            if result and len(result) > 1:
                print("📊 Relationship types found:")
                for row in result[1:]:  # Skip header
                    if len(row) >= 2:
                        rel_type, count = row[0], row[1]
                        print(f"  {rel_type}: {count}")
            else:
                print("📊 No relationships found")
                self.add_issue(
                    "Edge Structure",
                    "HIGH",
                    "No relationships/edges found in database", 
                    "Implement EntityEdge and EpisodicEdge creation according to Graphiti specifications"
                )
                
        except Exception as e:
            self.add_issue(
                "Edge Structure",
                "CRITICAL",
                f"Failed to verify Edge structure: {e}",
                "Check relationship creation in document processing pipeline"
            )
    
    def verify_redis_embeddings(self):
        """Verify Redis vector embeddings structure."""
        print("\n🔍 VERIFYING REDIS EMBEDDINGS")
        print("=" * 60)
        
        try:
            keys = self.redis_client.keys('embedding:*')
            print(f"📊 Found {len(keys)} embeddings in Redis")
            
            if len(keys) > 0:
                # Check sample embedding structure
                sample_key = keys[0]
                data = self.redis_client.hgetall(sample_key)
                
                required_fields = ['fact_uuid', 'episode_uuid', 'body', 'embedding', 'dimensions']
                missing_fields = []
                
                for field in required_fields:
                    if field.encode() not in data:
                        missing_fields.append(field)
                
                if missing_fields:
                    self.add_issue(
                        "Redis Embeddings",
                        "MEDIUM",
                        f"Embedding records missing fields: {missing_fields}",
                        "Ensure all required fields are stored with embeddings"
                    )
                else:
                    print("✅ Embedding structure looks correct")
                    
                # Check dimensions
                dimensions = data.get(b'dimensions', b'0').decode()
                if dimensions != '1024':
                    self.add_issue(
                        "Redis Embeddings",
                        "MEDIUM", 
                        f"Unexpected embedding dimensions: {dimensions} (expected 1024)",
                        "Verify embedding model configuration"
                    )
                else:
                    print("✅ Embedding dimensions correct (1024)")
                    
            else:
                self.add_issue(
                    "Redis Embeddings",
                    "HIGH",
                    "No embeddings found in Redis",
                    "Ensure document processing generates and stores embeddings"
                )
                
        except Exception as e:
            self.add_issue(
                "Redis Embeddings",
                "CRITICAL",
                f"Failed to verify Redis embeddings: {e}",
                "Check Redis connection and embedding storage process"
            )
    
    def verify_data_consistency(self):
        """Verify data consistency between FalkorDB and Redis."""
        print("\n🔍 VERIFYING DATA CONSISTENCY")
        print("=" * 60)

        try:
            # Get fact count from FalkorDB
            falkor_result = self.falkor_adapter.execute_cypher("MATCH (f:Fact) RETURN count(f)")
            fact_count = 0
            if falkor_result and len(falkor_result) > 1:
                count_data = falkor_result[1]
                if isinstance(count_data, list) and len(count_data) > 0:
                    fact_count = count_data[0] if isinstance(count_data[0], int) else 0
                elif isinstance(count_data, int):
                    fact_count = count_data

            # Get embedding count from Redis
            redis_keys = self.redis_client.keys('embedding:*')
            embedding_count = len(redis_keys)

            print(f"📊 Facts in FalkorDB: {fact_count}")
            print(f"📊 Embeddings in Redis: {embedding_count}")

            if fact_count > 0 and embedding_count == 0:
                self.add_issue(
                    "Data Consistency",
                    "HIGH",
                    "Facts exist in FalkorDB but no embeddings in Redis",
                    "Ensure embedding generation and storage is working"
                )
            elif fact_count == 0 and embedding_count > 0:
                self.add_issue(
                    "Data Consistency",
                    "MEDIUM",
                    "Embeddings exist in Redis but no Facts in FalkorDB",
                    "Check if Facts are being created during document processing"
                )
            elif fact_count > 0 and abs(fact_count - embedding_count) > fact_count * 0.1:  # More than 10% difference
                self.add_issue(
                    "Data Consistency",
                    "MEDIUM",
                    f"Significant mismatch between Facts ({fact_count}) and Embeddings ({embedding_count})",
                    "Investigate why some facts may not have embeddings or vice versa"
                )
            else:
                print("✅ Data consistency looks reasonable")

        except Exception as e:
            self.add_issue(
                "Data Consistency",
                "HIGH",
                f"Failed to verify data consistency: {e}",
                "Check database connections and data integrity"
            )
    
    async def run_verification(self):
        """Run complete verification process."""
        print("🚀 STARTING GRAPHITI COMPLIANCE VERIFICATION")
        print("=" * 80)
        
        await self.initialize()
        
        # Run all verification checks
        self.verify_graphiti_node_structure()
        self.verify_graphiti_episode_structure() 
        self.verify_graphiti_fact_structure()
        self.verify_graphiti_edge_structure()
        self.verify_redis_embeddings()
        self.verify_data_consistency()
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate compliance report."""
        print("\n📋 COMPLIANCE REPORT")
        print("=" * 80)
        
        if not self.issues:
            print("🎉 EXCELLENT! No compliance issues found.")
            print("✅ Your system appears to be following Graphiti specifications correctly.")
            return
        
        # Categorize issues by severity
        critical = [i for i in self.issues if i['severity'] == 'CRITICAL']
        high = [i for i in self.issues if i['severity'] == 'HIGH'] 
        medium = [i for i in self.issues if i['severity'] == 'MEDIUM']
        low = [i for i in self.issues if i['severity'] == 'LOW']
        
        print(f"🔴 CRITICAL Issues: {len(critical)}")
        print(f"🟠 HIGH Issues: {len(high)}")
        print(f"🟡 MEDIUM Issues: {len(medium)}")
        print(f"🟢 LOW Issues: {len(low)}")
        print()
        
        # Show issues by category
        for severity, issues in [('CRITICAL', critical), ('HIGH', high), ('MEDIUM', medium), ('LOW', low)]:
            if issues:
                print(f"\n{severity} ISSUES:")
                print("-" * 40)
                for i, issue in enumerate(issues, 1):
                    print(f"{i}. [{issue['category']}] {issue['description']}")
                    if issue['recommendation']:
                        print(f"   💡 Recommendation: {issue['recommendation']}")
                    print()
        
        # Overall assessment
        total_issues = len(self.issues)
        critical_weight = len(critical) * 4
        high_weight = len(high) * 3
        medium_weight = len(medium) * 2
        low_weight = len(low) * 1
        
        total_weight = critical_weight + high_weight + medium_weight + low_weight
        max_possible = 20  # Assume max 5 checks with weight 4 each
        
        compliance_percentage = max(0, 100 - (total_weight / max_possible * 100))
        
        print(f"\n📊 OVERALL COMPLIANCE SCORE: {compliance_percentage:.1f}%")
        
        if compliance_percentage >= 90:
            print("🎉 EXCELLENT compliance with Graphiti specifications!")
        elif compliance_percentage >= 75:
            print("✅ GOOD compliance - minor issues to address")
        elif compliance_percentage >= 50:
            print("⚠️  MODERATE compliance - several issues need attention")
        else:
            print("❌ POOR compliance - significant issues require immediate attention")

async def main():
    """Main verification function."""
    verifier = GraphitiComplianceVerifier()
    await verifier.run_verification()

if __name__ == "__main__":
    asyncio.run(main())
