#!/usr/bin/env python3
"""
Simple test of MedGemma vs current system.
"""

import asyncio
import time
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging_utils import get_logger

logger = get_logger(__name__)

# Simple medical text for testing
TEST_TEXT = """
Ginger (Zingiber officinale) contains gingerols and shogaols. These compounds have anti-inflammatory properties 
and may help reduce nausea in cancer patients undergoing chemotherapy. The recommended dosage is 1-3 grams per day.
"""

async def test_current_system():
    """Test the current OpenRouter system."""
    print("🔄 Testing Current System (OpenRouter Llama Maverick)...")
    
    try:
        # Import and test the current system
        from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
        
        # Make sure we're using the current settings
        os.environ['ENTITY_EXTRACTION_PROVIDER'] = 'openrouter'
        os.environ['ENTITY_EXTRACTION_MODEL'] = 'meta-llama/llama-4-maverick'
        
        extractor = LLMEntityExtractor()
        
        start_time = time.time()
        
        # Call the extraction method directly without await issues
        try:
            entities = extractor._extract_with_openrouter(TEST_TEXT, extractor._build_system_prompt())
            end_time = time.time()
            
            extraction_time = end_time - start_time
            
            print(f"   ✅ Current System Results:")
            print(f"      Time: {extraction_time:.2f} seconds")
            print(f"      Entities found: {len(entities)}")
            
            # Show entities
            for i, entity in enumerate(entities[:5]):
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                confidence = entity.get('confidence', 0)
                print(f"      {i+1}. {name} ({entity_type}) - {confidence}")
            
            return {
                'success': True,
                'time': extraction_time,
                'count': len(entities),
                'entities': entities
            }
            
        except Exception as e:
            print(f"   ❌ Current System Error: {e}")
            return {'success': False, 'error': str(e)}
            
    except Exception as e:
        print(f"   ❌ Import Error: {e}")
        return {'success': False, 'error': str(e)}

async def test_medgemma_direct():
    """Test MedGemma directly via Ollama API."""
    print("\n🔄 Testing MedGemma (Direct Ollama API)...")
    
    try:
        import requests
        import json
        
        # Test if Ollama is running
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if response.status_code != 200:
                print(f"   ❌ Ollama not responding: {response.status_code}")
                return {'success': False, 'error': 'Ollama not responding'}
        except Exception as e:
            print(f"   ❌ Ollama connection error: {e}")
            return {'success': False, 'error': f'Ollama connection error: {e}'}
        
        # Check if MedGemma model is available
        models = response.json().get('models', [])
        model_names = [m.get('name', '') for m in models]
        
        medgemma_available = any('medgemma' in name.lower() for name in model_names)
        if not medgemma_available:
            print(f"   ❌ MedGemma model not found in: {model_names}")
            return {'success': False, 'error': 'MedGemma model not available'}
        
        # Find the exact model name
        medgemma_model = None
        for name in model_names:
            if 'medgemma' in name.lower():
                medgemma_model = name
                break
        
        print(f"   📊 Using model: {medgemma_model}")
        
        # Create entity extraction prompt
        prompt = f"""
        Extract medical entities from the following text. Return a JSON object with an "entities" array.
        Each entity should have: name, type, description, confidence (0-1).
        
        Entity types: Herb, Compound, Disease, Symptom, Treatment, Medication, Dosage, Process, Mechanism
        
        Text: {TEST_TEXT}
        
        JSON:
        """
        
        # Make request to Ollama
        start_time = time.time()
        
        ollama_request = {
            "model": medgemma_model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 1000
            }
        }
        
        response = requests.post(
            'http://localhost:11434/api/generate',
            json=ollama_request,
            timeout=60
        )
        
        end_time = time.time()
        extraction_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"   ✅ MedGemma Results:")
            print(f"      Time: {extraction_time:.2f} seconds")
            print(f"      Response length: {len(response_text)} characters")
            print(f"      Response preview: {response_text[:200]}...")
            
            # Try to parse JSON from response
            try:
                # Look for JSON in the response
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed = json.loads(json_str)
                    entities = parsed.get('entities', [])
                    
                    print(f"      Entities found: {len(entities)}")
                    
                    # Show entities
                    for i, entity in enumerate(entities[:5]):
                        name = entity.get('name', 'Unknown')
                        entity_type = entity.get('type', 'Unknown')
                        confidence = entity.get('confidence', 0)
                        print(f"      {i+1}. {name} ({entity_type}) - {confidence}")
                    
                    return {
                        'success': True,
                        'time': extraction_time,
                        'count': len(entities),
                        'entities': entities,
                        'raw_response': response_text
                    }
                else:
                    print(f"      ⚠️ No JSON found in response")
                    return {
                        'success': False,
                        'error': 'No JSON in response',
                        'raw_response': response_text
                    }
                    
            except json.JSONDecodeError as e:
                print(f"      ❌ JSON parsing error: {e}")
                return {
                    'success': False,
                    'error': f'JSON parsing error: {e}',
                    'raw_response': response_text
                }
        else:
            print(f"   ❌ Ollama API error: {response.status_code}")
            print(f"      Response: {response.text}")
            return {
                'success': False,
                'error': f'Ollama API error: {response.status_code}',
                'raw_response': response.text
            }
            
    except Exception as e:
        print(f"   ❌ MedGemma test error: {e}")
        return {'success': False, 'error': str(e)}

def compare_results(current_result: dict, medgemma_result: dict):
    """Compare the results from both systems."""
    print(f"\n📊 COMPARISON RESULTS")
    print("=" * 40)
    
    if current_result.get('success') and medgemma_result.get('success'):
        print(f"✅ Both systems working!")
        
        current_time = current_result.get('time', 0)
        medgemma_time = medgemma_result.get('time', 0)
        
        current_count = current_result.get('count', 0)
        medgemma_count = medgemma_result.get('count', 0)
        
        print(f"\n🏃 Speed Comparison:")
        print(f"   Current (OpenRouter): {current_time:.2f}s")
        print(f"   MedGemma (Ollama): {medgemma_time:.2f}s")
        
        if current_time < medgemma_time:
            print(f"   🏆 OpenRouter is faster by {medgemma_time - current_time:.2f}s")
        else:
            print(f"   🏆 MedGemma is faster by {current_time - medgemma_time:.2f}s")
        
        print(f"\n📈 Entity Count:")
        print(f"   Current (OpenRouter): {current_count} entities")
        print(f"   MedGemma (Ollama): {medgemma_count} entities")
        
        if current_count > medgemma_count:
            print(f"   🏆 OpenRouter found more entities (+{current_count - medgemma_count})")
        elif medgemma_count > current_count:
            print(f"   🏆 MedGemma found more entities (+{medgemma_count - current_count})")
        else:
            print(f"   🤝 Both found the same number of entities")
        
        print(f"\n💡 Recommendation:")
        if medgemma_count >= current_count and medgemma_time <= current_time * 1.5:
            print("   🏆 Consider switching to MedGemma - Good performance, local processing")
        elif current_time < medgemma_time * 0.7:
            print("   🏆 Stick with OpenRouter - Significantly faster")
        else:
            print("   🤝 Both are viable - consider cost and reliability preferences")
            
    elif current_result.get('success'):
        print(f"✅ Current system working, ❌ MedGemma failed")
        print(f"   Stick with OpenRouter Llama Maverick")
        
    elif medgemma_result.get('success'):
        print(f"❌ Current system failed, ✅ MedGemma working")
        print(f"   Consider switching to MedGemma")
        
    else:
        print(f"❌ Both systems failed")
        print(f"   Current error: {current_result.get('error', 'Unknown')}")
        print(f"   MedGemma error: {medgemma_result.get('error', 'Unknown')}")

async def main():
    """Main test function."""
    print("🚀 MEDGEMMA vs CURRENT SYSTEM TEST")
    print("=" * 50)
    print(f"📝 Test Text: {TEST_TEXT[:100]}...")
    
    # Test current system
    current_result = await test_current_system()
    
    # Test MedGemma
    medgemma_result = await test_medgemma_direct()
    
    # Compare results
    compare_results(current_result, medgemma_result)

if __name__ == "__main__":
    asyncio.run(main())
