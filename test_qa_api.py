#!/usr/bin/env python3
"""
Test the Q&A API endpoint.
"""

import requests
import json

def test_qa_api():
    """Test the Q&A API with sample questions."""
    print("🔄 Testing Q&A API...")
    
    test_questions = [
        "can you tell me about cocoa and human health",
        "can you tell me about cancer and antioxidants"
    ]
    
    for question in test_questions:
        print(f"\n🔍 Testing: '{question}'")
        
        try:
            # Test the Q&A endpoint
            response = requests.post(
                'http://localhost:9753/api/qa/answer',
                json={
                    "question": question,
                    "max_facts": 10,
                    "response_length": "brief"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('answer', 'No answer provided')
                sources = data.get('sources', [])
                
                print(f"✅ API Response:")
                print(f"   Answer: {answer[:200]}...")
                print(f"   Sources: {len(sources)} documents referenced")
                
                if sources:
                    for i, source in enumerate(sources[:2]):
                        doc_name = source.get('document_name', 'Unknown')
                        print(f"     {i+1}. {doc_name}")
                        
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_qa_api()
