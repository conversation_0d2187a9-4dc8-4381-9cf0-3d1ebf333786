#!/usr/bin/env python3
"""
Restart processing with the UNIFIED INGESTION PIPELINE

This script will:
1. Stop any running reprocessing
2. Verify the unified pipeline is working
3. Continue processing remaining documents with the unified system
"""

import asyncio
import os
import signal
import psutil
from pathlib import Path
from datetime import datetime

async def restart_with_unified_pipeline():
    """Restart processing with the unified pipeline."""
    
    print("🔄 RESTARTING WITH UNIFIED INGESTION PIPELINE")
    print("=" * 60)
    
    # Step 1: Stop any running reprocessing
    print("1️⃣ Stopping any running reprocessing...")
    
    # Find and stop Python processes running reprocessing scripts
    stopped_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] == 'python.exe' or proc.info['name'] == 'python':
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # Check if it's running a reprocessing script
                reprocessing_keywords = [
                    'complete_missing_references.py',
                    'reprocess_documents',
                    'aggressive_reference',
                    'batch_processing'
                ]
                
                if any(keyword in cmdline for keyword in reprocessing_keywords):
                    print(f"   🛑 Stopping process: {proc.info['pid']} - {cmdline[:80]}...")
                    proc.terminate()
                    stopped_processes.append(proc.info['pid'])
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if stopped_processes:
        print(f"   ✅ Stopped {len(stopped_processes)} reprocessing processes")
        # Wait a moment for processes to stop
        await asyncio.sleep(3)
    else:
        print("   ℹ️ No reprocessing processes found")
    
    # Step 2: Verify unified pipeline is working
    print("\n2️⃣ Verifying unified pipeline...")
    
    try:
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print("   ✅ Unified pipeline imported successfully")
        
        # Test with a small text sample
        test_result = await pipeline._extract_references_aggressive(
            Path("test.txt"), 
            "This is a test document with a reference: Smith, J. (2023). Test paper. Journal of Testing, 1(1), 1-10."
        )
        
        if test_result.get('success', False):
            print(f"   ✅ Aggressive reference extraction working: {test_result.get('count', 0)} refs found")
        else:
            print(f"   ⚠️ Reference extraction test failed: {test_result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"   ❌ Unified pipeline verification failed: {e}")
        return False
    
    # Step 3: Check current status
    print("\n3️⃣ Checking current processing status...")
    
    # Count existing CSV files
    ref_dir = Path("references")
    if ref_dir.exists():
        csv_files = list(ref_dir.glob("*.csv"))
        aggressive_files = [f for f in csv_files if 'aggressive' in f.name]
        traditional_files = [f for f in csv_files if 'aggressive' not in f.name]
        
        print(f"   📁 Total CSV files: {len(csv_files)}")
        print(f"   🚀 Aggressive files: {len(aggressive_files)}")
        print(f"   📄 Traditional files: {len(traditional_files)}")
    else:
        print("   📁 No references directory found")
    
    # Check database documents
    try:
        from database.falkordb_adapter import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        query = "MATCH (e:Episode) RETURN count(e) as total_docs"
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            total_docs = result[1][0][0] if result[1] else 0
            print(f"   📄 Total documents in database: {total_docs}")
        else:
            print("   📄 Could not count documents in database")
            
    except Exception as e:
        print(f"   ❌ Database check failed: {e}")
    
    # Step 4: Offer options
    print("\n4️⃣ Next steps:")
    print("   A) Continue processing remaining documents with unified pipeline")
    print("   B) Reprocess ALL documents with unified pipeline (clean start)")
    print("   C) Just verify current counts and exit")
    
    choice = input("\nEnter your choice (A/B/C): ").upper().strip()
    
    if choice == 'A':
        await continue_processing_remaining()
    elif choice == 'B':
        await reprocess_all_documents()
    elif choice == 'C':
        await verify_current_counts()
    else:
        print("Invalid choice. Exiting.")

async def continue_processing_remaining():
    """Continue processing documents that don't have aggressive CSV files."""
    print("\n🔄 CONTINUING WITH REMAINING DOCUMENTS")
    print("=" * 50)
    
    try:
        from unified_ingestion_pipeline import get_unified_pipeline
        from database.falkordb_adapter import get_falkordb_adapter
        
        # Get all documents
        adapter = await get_falkordb_adapter()
        query = """
        MATCH (e:Episode)
        RETURN e.uuid as document_id, 
               e.name as filename, 
               e.file_path as file_path
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        documents = []
        
        if result and len(result) > 1:
            for row in result[1]:
                documents.append({
                    'document_id': row[0],
                    'filename': row[1],
                    'file_path': row[2]
                })
        
        # Find documents without aggressive CSV files
        ref_dir = Path("references")
        existing_aggressive = set()
        
        if ref_dir.exists():
            for csv_file in ref_dir.glob("*_aggressive_references.csv"):
                doc_id = csv_file.name.split('_')[0]
                existing_aggressive.add(doc_id)
        
        missing_docs = [doc for doc in documents if doc['document_id'] not in existing_aggressive]
        
        print(f"📊 Status:")
        print(f"   📄 Total documents: {len(documents)}")
        print(f"   ✅ Already processed: {len(existing_aggressive)}")
        print(f"   ❌ Need processing: {len(missing_docs)}")
        
        if not missing_docs:
            print("\n🎉 All documents already have aggressive CSV files!")
            return
        
        # Process remaining documents
        pipeline = await get_unified_pipeline()
        
        print(f"\n🚀 Processing {len(missing_docs)} remaining documents...")
        
        for i, doc in enumerate(missing_docs, 1):
            print(f"\n📖 Processing {i}/{len(missing_docs)}: {doc['filename'][:50]}...")
            
            try:
                if not doc['file_path'] or not os.path.exists(doc['file_path']):
                    print(f"   ⚠️ File not found, skipping")
                    continue
                
                result = await pipeline.process_document(
                    file_path=doc['file_path'],
                    extract_references=True,
                    extract_entities=False,  # Focus on references
                    extract_metadata=False,
                    generate_embeddings=False,
                    force_reprocess=False  # Don't reprocess if already done
                )
                
                if result.get('success', False):
                    refs = result.get('references', 0)
                    print(f"   ✅ Found {refs} references")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
                # Brief pause
                if i % 3 == 0:
                    await asyncio.sleep(2)
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        print(f"\n🎉 Remaining documents processing complete!")
        
    except Exception as e:
        print(f"❌ Error in continue processing: {e}")

async def reprocess_all_documents():
    """Reprocess ALL documents with the unified pipeline."""
    print("\n🔄 REPROCESSING ALL DOCUMENTS")
    print("=" * 50)
    
    confirm = input("⚠️ This will reprocess ALL documents. Continue? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("Cancelled.")
        return
    
    try:
        from unified_ingestion_pipeline import get_unified_pipeline
        from database.falkordb_adapter import get_falkordb_adapter
        
        # Get all documents
        adapter = await get_falkordb_adapter()
        query = """
        MATCH (e:Episode)
        RETURN e.file_path as file_path
        WHERE e.file_path IS NOT NULL
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        file_paths = []
        
        if result and len(result) > 1:
            for row in result[1]:
                if row[0] and os.path.exists(row[0]):
                    file_paths.append(row[0])
        
        print(f"📄 Found {len(file_paths)} documents to reprocess")
        
        # Use batch processing
        pipeline = await get_unified_pipeline()
        result = await pipeline.process_batch(
            file_paths=file_paths,
            max_parallel=3,
            extract_references=True,
            extract_entities=False,  # Focus on references for now
            extract_metadata=False,
            generate_embeddings=False
        )
        
        print(f"\n🎉 Batch reprocessing complete!")
        print(f"   ✅ Successful: {result.get('successful_count', 0)}")
        print(f"   📚 Total references: {result.get('total_references', 0)}")
        
    except Exception as e:
        print(f"❌ Error in reprocess all: {e}")

async def verify_current_counts():
    """Verify current reference counts."""
    print("\n📊 VERIFYING CURRENT COUNTS")
    print("=" * 50)
    
    # Count CSV references
    ref_dir = Path("references")
    total_csv_refs = 0
    aggressive_refs = 0
    
    if ref_dir.exists():
        for csv_file in ref_dir.glob("*.csv"):
            try:
                import csv
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    ref_count = len(rows) - 1
                    
                    if ref_count > 0:
                        total_csv_refs += ref_count
                        if 'aggressive' in csv_file.name:
                            aggressive_refs += ref_count
            except:
                continue
    
    print(f"📁 CSV Files:")
    print(f"   🚀 Aggressive references: {aggressive_refs}")
    print(f"   📄 Traditional references: {total_csv_refs - aggressive_refs}")
    print(f"   📚 Total CSV references: {total_csv_refs}")
    
    # Check API count
    try:
        import requests
        response = requests.get("http://localhost:9753/api/fast/graph-stats", timeout=10)
        if response.status_code == 200:
            api_data = response.json()
            api_refs = api_data.get('total_references', 0)
            print(f"   📱 API reports: {api_refs} references")
        else:
            print(f"   📱 API check failed: {response.status_code}")
    except Exception as e:
        print(f"   📱 API check failed: {e}")
    
    print(f"\n✅ Verification complete!")

if __name__ == "__main__":
    asyncio.run(restart_with_unified_pipeline())
