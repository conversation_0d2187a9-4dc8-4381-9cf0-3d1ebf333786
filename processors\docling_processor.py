"""
Docling Document Processor
Uses IBM's Docling library for advanced document processing including .doc files.
"""

import asyncio
from typing import Dict, Any
from pathlib import Path
import logging
from datetime import datetime

from utils.logging_utils import get_logger

logger = get_logger(__name__)

class DoclingProcessor:
    """
    Document processor using IBM's Docling library.
    Supports multiple formats including PDF, DOCX, DOC, PPTX, HTML, images, etc.
    """
    
    def __init__(self):
        """Initialize the Docling processor."""
        # Docling natively supports these formats
        self.supported_extensions = {
            '.pdf', '.docx', '.pptx', '.html', '.htm',
            '.jpg', '.jpeg', '.png', '.tiff', '.tif',
            '.bmp', '.webp', '.xlsx', '.csv', '.md'
        }
        
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports the file type."""
        return Path(file_path).suffix.lower() in self.supported_extensions
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from document using Docling.

        Args:
            file_path: Path to the document file

        Returns:
            Dictionary with extraction results
        """
        try:
            logger.info(f"Processing document with Docling: {file_path}")

            # Import Docling components
            from docling.document_converter import DocumentConverter

            # Initialize the converter
            converter = DocumentConverter()

            # Convert the document
            result = converter.convert(str(file_path))

            # Extract text content
            text_content = result.document.export_to_markdown()
            
            if not text_content or len(text_content.strip()) == 0:
                return {
                    'success': False,
                    'error': 'No text content extracted from document',
                    'text': '',
                    'metadata': {}
                }
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat(),
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'extraction_method': 'docling',
                'docling_version': self._get_docling_version()
            }
            
            # Add document-specific metadata if available
            if hasattr(result.document, 'meta'):
                doc_meta = result.document.meta
                if hasattr(doc_meta, 'title') and doc_meta.title:
                    metadata['document_title'] = doc_meta.title
                if hasattr(doc_meta, 'author') and doc_meta.author:
                    metadata['author'] = doc_meta.author
                if hasattr(doc_meta, 'creation_date') and doc_meta.creation_date:
                    metadata['creation_date'] = str(doc_meta.creation_date)
                if hasattr(doc_meta, 'modification_date') and doc_meta.modification_date:
                    metadata['modification_date'] = str(doc_meta.modification_date)
            
            logger.info(f"✅ Successfully extracted {len(text_content)} characters from {file_path}")
            
            return {
                'success': True,
                'text': text_content.strip(),
                'metadata': metadata,
                'ocr_provider': 'docling',
                'extraction_method': 'docling'
            }
            
        except ImportError as e:
            logger.error(f"Docling not available: {e}")
            return {
                'success': False,
                'error': 'Docling library not available. Install with: pip install docling',
                'text': '',
                'metadata': {}
            }
        except Exception as e:
            logger.error(f"Error processing document with Docling: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the document content.
        
        Args:
            file_path: Path to the document file
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary with preview results
        """
        try:
            # Extract full text first
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            # Create preview
            full_text = result['text']
            preview_text = full_text[:max_chars]
            if len(full_text) > max_chars:
                preview_text += "..."
            
            return {
                'success': True,
                'preview_text': preview_text,
                'metadata': result['metadata'],
                'extraction_method': 'docling'
            }
            
        except Exception as e:
            logger.error(f"Error generating document preview: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'metadata': {}
            }
    
    def _get_docling_version(self) -> str:
        """Get the version of the Docling library."""
        try:
            import docling
            return getattr(docling, '__version__', 'unknown')
        except:
            return 'unknown'
    


    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return list(self.supported_extensions)
