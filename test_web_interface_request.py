#!/usr/bin/env python3
"""
Test the exact request that the web interface makes.
"""

import requests
import json

def test_web_interface_request():
    """Test the exact request the web interface makes."""
    print("🔄 Testing Web Interface Q&A Request...")
    
    # This mimics exactly what the web interface sends
    request_data = {
        "question": "tell me about cocoa and human health",
        "llm_model": "meta-llama/llama-3.1-70b-instruct",  # Default model
        "llm_provider": "openrouter",  # Default provider
        "max_facts": 5,
        "response_length": "detailed",
        "conversation_context": [],
        "temperature": 0.3
    }
    
    print(f"📝 Request data:")
    for key, value in request_data.items():
        print(f"   {key}: {value}")
    
    try:
        response = requests.post(
            'http://localhost:9753/api/qa/answer',
            json=request_data,
            timeout=120  # Give it time to process
        )
        
        print(f"\n📊 Response:")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            answer = data.get('answer', 'No answer')
            sources = data.get('sources', [])
            
            print(f"   Answer length: {len(answer)} characters")
            print(f"   Answer preview: {answer[:200]}...")
            print(f"   Sources: {len(sources)} documents")
            
            if sources:
                for i, source in enumerate(sources[:2]):
                    doc_name = source.get('document_name', 'Unknown')
                    print(f"     {i+1}. {doc_name}")
            
            # Check if it's the "not enough information" response
            if "don't have enough information" in answer.lower():
                print("   ⚠️ Still getting 'not enough information' response")
                return False
            else:
                print("   ✅ Got a real answer!")
                return True
                
        else:
            print(f"   ❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_simple_request():
    """Test with a simpler request."""
    print("\n🔄 Testing Simple Q&A Request...")
    
    simple_data = {
        "question": "cocoa health benefits",
        "max_facts": 10,
        "response_length": "brief"
    }
    
    try:
        response = requests.post(
            'http://localhost:9753/api/qa/answer',
            json=simple_data,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('answer', 'No answer')
            print(f"   Simple answer: {answer[:150]}...")
            
            if "don't have enough information" in answer.lower():
                print("   ⚠️ Still getting 'not enough information'")
                return False
            else:
                print("   ✅ Simple request worked!")
                return True
        else:
            print(f"   ❌ Simple request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Simple request error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING WEB INTERFACE Q&A REQUESTS")
    print("=" * 50)
    
    # Test the full web interface request
    web_ok = test_web_interface_request()
    
    # Test a simpler request
    simple_ok = test_simple_request()
    
    print(f"\n📊 RESULTS:")
    print(f"   Web Interface Request: {'✅' if web_ok else '❌'}")
    print(f"   Simple Request: {'✅' if simple_ok else '❌'}")
    
    if not web_ok and not simple_ok:
        print("\n❌ Both requests failed - there's still an issue with the Q&A API")
    elif simple_ok and not web_ok:
        print("\n⚠️ Simple request works but web interface request fails")
    else:
        print("\n🎉 Q&A API is working correctly!")
