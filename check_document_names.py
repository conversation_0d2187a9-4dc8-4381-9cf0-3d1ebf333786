#!/usr/bin/env python3
"""
Check document names in the database.
"""

import falkordb

def check_document_names():
    """Check what document names look like in the database."""
    try:
        # Connect directly to FalkorDB
        client = falkordb.FalkorDB(host='localhost', port=6379)
        graph = client.select_graph('graphiti_knowledge')

        # Check document names
        result = graph.query('MATCH (d:Document) RETURN d.name, d.file_path LIMIT 5')
        print('📊 Document names in database:')
        for row in result.result_set:
            print(f'   Name: "{row[0]}"')
            print(f'   Path: "{row[1]}"')
            print()
            
        # Check if names are None/empty
        empty_result = graph.query('MATCH (d:Document) WHERE d.name IS NULL OR d.name = "" RETURN count(d) as empty_names')
        empty_count = empty_result.result_set[0][0] if empty_result.result_set else 0
        print(f'📊 Documents with empty/null names: {empty_count}')
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    check_document_names()
