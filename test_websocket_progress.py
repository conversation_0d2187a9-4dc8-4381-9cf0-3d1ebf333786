#!/usr/bin/env python3
"""
Test WebSocket progress updates for unified upload
"""

import asyncio
import aiohttp
import websockets
import json
from pathlib import Path

async def test_websocket_progress():
    """Test WebSocket progress updates during file upload."""
    
    print("🧪 TESTING WEBSOCKET PROGRESS UPDATES")
    print("=" * 50)
    
    try:
        # Create a test file
        test_file_path = Path("test_websocket.txt")
        test_content = """
        This is a test document for WebSocket progress testing.
        
        It contains some sample text about health and nutrition.
        Vitamin C is important for immune function.
        Magnesium supports over 300 enzymatic reactions.
        Omega-3 fatty acids have anti-inflammatory properties.
        """
        
        # Write test file
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 Created test file: {test_file_path}")
        
        # Start upload and get operation ID
        operation_id = None
        
        async with aiohttp.ClientSession() as session:
            # Prepare form data
            data = aiohttp.FormData()
            
            # Read file content
            with open(test_file_path, 'rb') as f:
                file_content = f.read()
            
            # Add file to form data
            data.add_field('file', file_content, filename='test_websocket.txt', content_type='text/plain')
            data.add_field('chunk_size', '1200')
            data.add_field('overlap', '0')
            data.add_field('extract_entities', 'true')
            data.add_field('extract_references', 'true')
            data.add_field('extract_metadata', 'true')
            data.add_field('generate_embeddings', 'true')
            data.add_field('skip_duplicate_check', 'false')
            
            print(f"🚀 Starting upload...")
            
            # Make the upload request
            async with session.post('http://localhost:9753/api/unified/upload-with-duplicate-check', data=data) as response:
                print(f"📊 Upload response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    operation_id = result.get('operation_id')
                    print(f"✅ Upload started successfully!")
                    print(f"   🆔 Operation ID: {operation_id}")
                    print(f"   📄 Filename: {result.get('filename', 'unknown')}")
                else:
                    error_text = await response.text()
                    print(f"❌ Upload failed: {error_text}")
                    return False
        
        if not operation_id:
            print("❌ No operation ID received")
            return False
        
        # Connect to WebSocket for progress updates
        print(f"\n🔌 Connecting to WebSocket for operation {operation_id}...")
        
        try:
            websocket_url = f"ws://localhost:9753/ws/progress/{operation_id}"
            
            async with websockets.connect(websocket_url) as websocket:
                print(f"✅ WebSocket connected!")
                
                # Listen for progress updates
                progress_updates = []
                timeout_count = 0
                max_timeout = 30  # 30 seconds timeout
                
                while timeout_count < max_timeout:
                    try:
                        # Wait for message with 1 second timeout
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        data = json.loads(message)
                        
                        print(f"📨 WebSocket message: {data.get('type', 'unknown')}")
                        
                        if data.get('type') == 'progress_update':
                            progress_data = data.get('data', {})
                            progress = progress_data.get('progress', 0)
                            status = progress_data.get('status', 'unknown')
                            message_text = progress_data.get('message', 'no message')
                            
                            print(f"   📈 Progress: {progress}% - {status} - {message_text}")
                            progress_updates.append(data)
                            
                        elif data.get('type') == 'operation_complete':
                            print(f"   🎉 Operation completed!")
                            final_data = data.get('data', {})
                            success = final_data.get('success', False)
                            print(f"   ✅ Success: {success}")
                            break
                            
                        elif data.get('type') == 'operation_error':
                            print(f"   ❌ Operation error: {data.get('message', 'unknown error')}")
                            break
                            
                        elif data.get('type') == 'connection_established':
                            print(f"   🔗 Connection established")
                            
                        timeout_count = 0  # Reset timeout on message
                        
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 5 == 0:  # Print every 5 seconds
                            print(f"   ⏳ Waiting for updates... ({timeout_count}s)")
                
                print(f"\n📊 WEBSOCKET TEST RESULTS:")
                print(f"   📨 Total progress updates received: {len(progress_updates)}")
                
                if progress_updates:
                    print(f"   📈 Progress updates:")
                    for i, update in enumerate(progress_updates):
                        data = update.get('data', {})
                        progress = data.get('progress', 0)
                        status = data.get('status', 'unknown')
                        message = data.get('message', 'no message')
                        print(f"      {i+1}. {progress}% - {status} - {message}")
                else:
                    print(f"   ⚠️ No progress updates received via WebSocket")
                
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            print(f"   This might be why the UI progress isn't updating")
        
        # Check final operation status via API
        print(f"\n🔍 Checking final operation status via API...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f'http://localhost:9753/api/unified/operations/{operation_id}') as response:
                if response.status == 200:
                    status_data = await response.json()
                    print(f"   📊 Final status: {status_data.get('status', 'unknown')}")
                    print(f"   📈 Final progress: {status_data.get('progress', 0)}%")
                    print(f"   💬 Final message: {status_data.get('message', 'no message')}")
                else:
                    print(f"   ❌ Could not get final status: {response.status}")
        
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()
            print(f"\n🧹 Cleaned up test file")
        
        print(f"\n🎉 WEBSOCKET PROGRESS TEST COMPLETE!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Clean up test file on error
        if 'test_file_path' in locals() and test_file_path.exists():
            test_file_path.unlink()
        
        return False

if __name__ == "__main__":
    asyncio.run(test_websocket_progress())
