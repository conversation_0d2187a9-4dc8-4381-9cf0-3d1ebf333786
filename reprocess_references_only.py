#!/usr/bin/env python3
"""
Reprocess Documents for References Only
- Uses AI-powered Intelligent Reference Extractor
- Ensures proper document metadata linking
- Updates master CSV with quality filtering
- Maintains existing document processing but focuses on references
"""

import asyncio
import os
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
from services.intelligent_reference_extractor import get_intelligent_reference_extractor
from utils.mistral_ocr import MistralOCRProcessor
from utils.logging_utils import get_logger
from dotenv import load_dotenv
from datetime import datetime
import json
import re

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

class ReferenceReprocessor:
    """Focused reference reprocessing with proper metadata linking."""
    
    def __init__(self):
        self.extractor = get_intelligent_reference_extractor()
        self.ocr_processor = MistralOCRProcessor()
        self.processed_documents = []
        self.all_references = []
        self.existing_master_csv = None
        
    async def find_documents_to_reprocess(self) -> List[Path]:
        """Find all documents that need reference reprocessing."""
        print("🔍 Finding documents for reference reprocessing...")
        
        # Search for documents
        search_paths = [
            Path("uploads"),
            Path("documents"),
            Path("processed_documents"),
            Path(".")
        ]
        
        documents = []
        for search_path in search_paths:
            if search_path.exists():
                # Focus on PDF files for now (most reliable with OCR)
                pdf_files = list(search_path.glob("*.pdf"))
                documents.extend(pdf_files)
        
        # Remove duplicates and filter
        unique_documents = []
        seen_names = set()
        
        for doc in documents:
            # Skip temp files and very small files
            if any(skip in doc.name.lower() for skip in ['temp', 'tmp', '~']):
                continue
            if doc.stat().st_size < 10000:  # Less than 10KB
                continue
                
            key = doc.name.lower()
            if key not in seen_names:
                seen_names.add(key)
                unique_documents.append(doc)
        
        print(f"📄 Found {len(unique_documents)} documents to reprocess:")
        for i, doc in enumerate(unique_documents, 1):
            size_mb = doc.stat().st_size / (1024 * 1024)
            print(f"   {i:2d}. {doc.name} ({size_mb:.1f} MB)")
        
        return unique_documents
    
    async def extract_text_from_document(self, document_path: Path) -> Dict[str, Any]:
        """Extract text from document using OCR."""
        try:
            print(f"📄 Extracting text from: {document_path.name}")
            
            result = await self.ocr_processor.process_pdf(str(document_path))
            
            if result.get('success', False):
                text = result.get('text', '')
                print(f"✅ Text extracted: {len(text):,} characters")
                return {'success': True, 'text': text}
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ Text extraction failed: {error}")
                return {'success': False, 'error': error, 'text': ''}
                
        except Exception as e:
            print(f"❌ Error extracting text: {e}")
            return {'success': False, 'error': str(e), 'text': ''}
    
    async def process_document_references(self, document_path: Path, doc_index: int, total_docs: int) -> Dict[str, Any]:
        """Process a single document for references with metadata linking."""
        print(f"\n{'='*60}")
        print(f"🚀 Processing References {doc_index}/{total_docs}")
        print(f"📄 Document: {document_path.name}")
        print(f"📊 Size: {document_path.stat().st_size/(1024*1024):.1f} MB")
        print(f"{'='*60}")
        
        start_time = datetime.now()
        
        try:
            # Step 1: Extract text
            text_result = await self.extract_text_from_document(document_path)
            if not text_result['success']:
                return {
                    'document_name': document_path.name,
                    'document_path': str(document_path),
                    'success': False,
                    'error': text_result['error'],
                    'references_found': 0,
                    'processing_time': 0
                }
            
            extracted_text = text_result['text']
            
            # Step 2: AI-powered reference extraction
            print(f"🤖 Applying AI-powered reference extraction...")
            extraction_result = await self.extractor.extract_references_comprehensive(
                extracted_text, 
                document_path.name
            )
            
            # Step 3: Quality filtering and metadata preparation
            filtered_references = self.prepare_references_with_metadata(
                extraction_result['references'], 
                document_path
            )
            
            # Step 4: Save individual document CSV
            csv_path = await self.save_document_references_csv(filtered_references, document_path)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            print(f"✅ Reference processing complete:")
            print(f"   📚 Raw references: {extraction_result['total_found']}")
            print(f"   🎯 Quality filtered: {len(filtered_references)}")
            print(f"   📄 CSV saved: {Path(csv_path).name if csv_path else 'None'}")
            print(f"   ⏱️ Processing time: {processing_time:.1f}s")
            
            # Store results
            document_result = {
                'document_name': document_path.name,
                'document_path': str(document_path),
                'success': True,
                'text_length': len(extracted_text),
                'raw_references': extraction_result['total_found'],
                'filtered_references': len(filtered_references),
                'references': filtered_references,
                'csv_path': csv_path,
                'extraction_methods': extraction_result['extraction_methods'],
                'confidence_score': extraction_result['confidence_score'],
                'processing_time': processing_time,
                'processed_at': datetime.now().isoformat()
            }
            
            self.processed_documents.append(document_result)
            self.all_references.extend(filtered_references)
            
            return document_result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            print(f"❌ Error processing references: {e}")
            return {
                'document_name': document_path.name,
                'document_path': str(document_path),
                'success': False,
                'error': str(e),
                'references_found': 0,
                'processing_time': processing_time
            }
    
    def prepare_references_with_metadata(self, references: List[Dict[str, Any]], document_path: Path) -> List[Dict[str, Any]]:
        """Prepare references with proper document metadata linking."""
        print(f"🔗 Preparing references with metadata linking...")
        
        prepared_refs = []
        
        for i, ref_data in enumerate(references, 1):
            ref_text = ref_data['text'].strip()
            
            # Quality filtering
            if not self.is_quality_reference(ref_text):
                continue
            
            # Calculate quality score
            quality_score = self.calculate_quality_score(ref_text)
            
            # Prepare reference with full metadata
            prepared_ref = {
                'reference_id': f"{document_path.stem}_{i:03d}",
                'reference_text': ref_text,
                'source_document': document_path.name,
                'source_document_path': str(document_path),
                'document_stem': document_path.stem,
                'confidence': ref_data.get('confidence', 0.0),
                'quality_score': quality_score,
                'extraction_method': 'ai_powered_intelligent',
                'ai_enhanced': True,
                'reference_length': len(ref_text),
                'has_doi': bool(re.search(r'doi:', ref_text, re.IGNORECASE)),
                'has_year': bool(re.search(r'\b(19|20)\d{2}\b', ref_text)),
                'has_journal': bool(re.search(r'\b(journal|nature|science|cell|lancet|nejm|bmj)\b', ref_text, re.IGNORECASE)),
                'processed_at': datetime.now().isoformat(),
                'normalized_text': self.normalize_reference(ref_text)
            }
            
            prepared_refs.append(prepared_ref)
        
        print(f"✅ Prepared {len(prepared_refs)} references with metadata")
        return prepared_refs
    
    def is_quality_reference(self, ref_text: str) -> bool:
        """Check if reference meets quality standards."""
        if len(ref_text) < 25:
            return False
        
        # Must have year
        if not re.search(r'\b(19|20)\d{2}\b', ref_text):
            return False
        
        # Must have author-like patterns
        author_patterns = [
            r'[A-Z][a-z]+,?\s+[A-Z]\.?',
            r'[A-Z][a-z]+\s+et\s+al\.?',
            r'[A-Z][a-z]+,\s+[A-Z][a-z]+',
        ]
        
        has_author = any(re.search(pattern, ref_text) for pattern in author_patterns)
        if not has_author:
            return False
        
        # Exclude junk
        junk_patterns = [
            r'^https?://[^\s]+$',
            r'^\d+$',
            r'^[A-Z\s]+$',
            r'^\W+$',
        ]
        
        is_junk = any(re.search(pattern, ref_text) for pattern in junk_patterns)
        return not is_junk
    
    def calculate_quality_score(self, ref_text: str) -> float:
        """Calculate quality score (0-1) for reference."""
        score = 0.0
        
        # Length scoring
        length = len(ref_text)
        if 50 <= length <= 300:
            score += 0.25
        elif 30 <= length <= 500:
            score += 0.15
        
        # Has DOI
        if re.search(r'doi:', ref_text, re.IGNORECASE):
            score += 0.25
        
        # Has journal
        if re.search(r'\b(journal|nature|science|cell|lancet|nejm|bmj)\b', ref_text, re.IGNORECASE):
            score += 0.20
        
        # Has volume/pages
        if re.search(r'\b\d+\(\d+\)', ref_text):
            score += 0.10
        if re.search(r'\b\d+[-–]\d+\b', ref_text):
            score += 0.10
        
        # Has proper author format
        if re.search(r'[A-Z][a-z]+,\s*[A-Z]\.', ref_text):
            score += 0.10
        
        return min(score, 1.0)
    
    def normalize_reference(self, ref_text: str) -> str:
        """Normalize reference for deduplication."""
        normalized = ref_text.lower()
        normalized = re.sub(r'\s+', ' ', normalized)
        normalized = re.sub(r'[^\w\s]', '', normalized)
        return normalized.strip()
    
    async def save_document_references_csv(self, references: List[Dict[str, Any]], document_path: Path) -> str:
        """Save references for individual document to CSV."""
        if not references:
            return ""
        
        try:
            # Create DataFrame
            df = pd.DataFrame(references)
            
            # Generate CSV filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{document_path.stem}_references_{timestamp}.csv"
            csv_path = Path("references") / csv_filename
            
            # Ensure directory exists
            csv_path.parent.mkdir(exist_ok=True)
            
            # Save CSV
            df.to_csv(csv_path, index=False, encoding='utf-8')
            
            print(f"💾 Document CSV saved: {csv_path.name}")
            return str(csv_path)
            
        except Exception as e:
            print(f"❌ Failed to save document CSV: {e}")
            return ""
    
    async def load_existing_master_csv(self) -> pd.DataFrame:
        """Load existing master CSV if available."""
        print("🔍 Looking for existing master CSV...")
        
        # Look for existing master CSV files
        references_dir = Path("references")
        if references_dir.exists():
            master_files = list(references_dir.glob("master_references_*.csv"))
            if master_files:
                # Use the most recent one
                latest_master = max(master_files, key=lambda x: x.stat().st_mtime)
                print(f"📄 Found existing master CSV: {latest_master.name}")
                
                try:
                    df = pd.read_csv(latest_master)
                    print(f"✅ Loaded {len(df)} existing references")
                    return df
                except Exception as e:
                    print(f"⚠️ Failed to load existing master CSV: {e}")
        
        print("📄 No existing master CSV found, will create new one")
        return pd.DataFrame()
    
    async def create_updated_master_csv(self) -> str:
        """Create updated master CSV with all references."""
        print(f"\n📊 Creating updated master CSV...")
        
        # Load existing master CSV
        existing_df = await self.load_existing_master_csv()
        
        # Create new references DataFrame
        if self.all_references:
            new_df = pd.DataFrame(self.all_references)
        else:
            new_df = pd.DataFrame()
        
        # Combine existing and new references
        if not existing_df.empty and not new_df.empty:
            print("🔄 Merging with existing master CSV...")
            
            # Ensure consistent columns
            common_columns = ['reference_text', 'source_document', 'quality_score', 'confidence']
            
            # Add missing columns with defaults
            for col in common_columns:
                if col not in existing_df.columns:
                    existing_df[col] = 0.0 if 'score' in col or 'confidence' in col else ''
                if col not in new_df.columns:
                    new_df[col] = 0.0 if 'score' in col or 'confidence' in col else ''
            
            # Combine DataFrames
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            
        elif not new_df.empty:
            combined_df = new_df
        elif not existing_df.empty:
            combined_df = existing_df
        else:
            print("⚠️ No references to save")
            return ""
        
        # Deduplication based on normalized text
        if 'normalized_text' in combined_df.columns:
            initial_count = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['normalized_text'], keep='first')
            final_count = len(combined_df)
            print(f"🔄 Deduplication: {initial_count:,} → {final_count:,} references")
        
        # Sort by quality score
        if 'quality_score' in combined_df.columns:
            combined_df = combined_df.sort_values(['quality_score', 'confidence'], ascending=[False, False])
        
        # Add master reference IDs
        combined_df['master_reference_id'] = range(1, len(combined_df) + 1)
        
        # Save updated master CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"master_references_updated_{timestamp}.csv"
        csv_path = Path("references") / csv_filename
        
        # Ensure directory exists
        csv_path.parent.mkdir(exist_ok=True)
        
        # Save CSV
        combined_df.to_csv(csv_path, index=False, encoding='utf-8')
        
        print(f"✅ Updated master CSV created: {csv_path}")
        print(f"📊 Total references: {len(combined_df):,}")
        if 'quality_score' in combined_df.columns:
            print(f"🎯 Average quality: {combined_df['quality_score'].mean():.3f}")
            print(f"🏆 High quality (>0.7): {len(combined_df[combined_df['quality_score'] > 0.7]):,}")
        print(f"📚 Documents represented: {combined_df['source_document'].nunique()}")
        
        return str(csv_path)
    
    async def generate_processing_summary(self) -> str:
        """Generate processing summary report."""
        print(f"\n📋 Generating processing summary...")
        
        successful_docs = [d for d in self.processed_documents if d['success']]
        failed_docs = [d for d in self.processed_documents if not d['success']]
        
        total_refs = sum(d['filtered_references'] for d in successful_docs)
        avg_refs = total_refs / len(successful_docs) if successful_docs else 0
        avg_time = sum(d['processing_time'] for d in successful_docs) / len(successful_docs) if successful_docs else 0
        
        report = f"""# REFERENCE REPROCESSING SUMMARY
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
System: AI-Powered Intelligent Reference Extractor

## 📊 PROCESSING RESULTS
- **Documents Processed:** {len(self.processed_documents)}
- **Successful:** {len(successful_docs)}
- **Failed:** {len(failed_docs)}
- **Success Rate:** {(len(successful_docs)/len(self.processed_documents)*100):.1f}%

## 📚 REFERENCE EXTRACTION
- **Total References:** {total_refs:,}
- **Average per Document:** {avg_refs:.1f}
- **Average Processing Time:** {avg_time:.1f} seconds

## 📄 DOCUMENT DETAILS
"""
        
        for doc in self.processed_documents:
            if doc['success']:
                report += f"✅ **{doc['document_name']}**: {doc['filtered_references']} references ({doc['processing_time']:.1f}s)\n"
            else:
                report += f"❌ **{doc['document_name']}**: FAILED - {doc.get('error', 'Unknown error')}\n"
        
        report += f"""
## 🎯 QUALITY METRICS
- **AI Enhancement:** All references processed with AI assistance
- **Quality Filtering:** Applied to ensure high-quality references
- **Metadata Linking:** Complete document metadata preserved
- **Deduplication:** Applied in master CSV creation

## 🚀 IMPROVEMENTS
This reprocessing represents a major improvement in reference extraction:
- **AI-powered extraction** finds references missed by pattern-matching
- **Quality scoring** ensures only valuable references are retained
- **Proper metadata linking** maintains document relationships
- **Master CSV integration** provides comprehensive reference database
"""
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"reference_reprocessing_summary_{timestamp}.md"
        report_path = Path("reports") / report_filename
        
        # Ensure directory exists
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Summary report saved: {report_path}")
        return str(report_path)

async def main():
    """Main reference reprocessing function."""
    print("🚀 REFERENCE REPROCESSING WITH AI-POWERED EXTRACTION")
    print("=" * 70)
    print("Focuses on reference extraction with proper metadata linking")
    print("Updates master CSV with quality filtering and deduplication")
    print("=" * 70)
    
    # Check API keys
    openrouter_key = os.getenv('OPEN_ROUTER_API_KEY') or os.getenv('OPENROUTER_API_KEY')
    if not openrouter_key:
        print("❌ OpenRouter API key not found! Please check your .env file.")
        return
    
    print(f"🔑 OpenRouter API: ✅ Available")
    
    # Initialize reprocessor
    reprocessor = ReferenceReprocessor()
    
    if not reprocessor.extractor.ai_available:
        print("❌ AI extraction not available despite API keys")
        return
    
    print(f"🤖 AI extraction: ✅ Available (meta-llama/llama-4-maverick)")
    
    try:
        # Find documents to reprocess
        documents = await reprocessor.find_documents_to_reprocess()
        
        if not documents:
            print("❌ No documents found to reprocess!")
            return
        
        # Process each document for references
        print(f"\n🔄 Processing {len(documents)} documents for references...")
        
        for i, document_path in enumerate(documents, 1):
            await reprocessor.process_document_references(document_path, i, len(documents))
        
        # Create updated master CSV
        master_csv_path = await reprocessor.create_updated_master_csv()
        
        # Generate summary report
        report_path = await reprocessor.generate_processing_summary()
        
        # Final summary
        successful_docs = len([d for d in reprocessor.processed_documents if d['success']])
        total_refs = len(reprocessor.all_references)
        
        print(f"\n🎉 REFERENCE REPROCESSING COMPLETE!")
        print(f"=" * 70)
        print(f"📊 Documents processed: {successful_docs}/{len(documents)}")
        print(f"📚 Total references extracted: {total_refs:,}")
        print(f"📄 Updated master CSV: {Path(master_csv_path).name if master_csv_path else 'None'}")
        print(f"📋 Summary report: {Path(report_path).name if report_path else 'None'}")
        print(f"=" * 70)
        print(f"🚀 All documents reprocessed with AI-powered reference extraction!")
        print(f"   ✅ Proper document metadata linking maintained")
        print(f"   ✅ Master CSV updated with quality filtering")
        print(f"   ✅ Individual document CSVs created")
        
    except Exception as e:
        print(f"❌ Reference reprocessing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
