"""
Database service for the Graphiti application.
"""

import asyncio
from typing import List, Dict, Any, Optional, Union, Tuple

from database.falkordb_adapter import FalkorDBAdapter
from utils.config import FALKORDB_GRAPH
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Global adapter instance
_adapter = None

async def get_falkordb_adapter() -> FalkorDBAdapter:
    """
    Get a FalkorDB adapter instance.

    Returns:
        FalkorDB adapter instance
    """
    global _adapter

    if _adapter is None:
        _adapter = FalkorDBAdapter(FALKORDB_GRAPH)

    return _adapter

async def execute_cypher(query: str, params: Dict[str, Any] = None) -> List[Any]:
    """
    Execute a Cypher query.
    
    Args:
        query: Cypher query
        params: Query parameters
        
    Returns:
        Query results
    """
    adapter = await get_falkordb_adapter()
    return adapter.execute_cypher(query, params)

async def create_episode_node(name: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create an Episode node.

    Args:
        name: Name of the episode
        properties: Additional properties

    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()

    # Clean the name
    cleaned_name = _clean_text_for_storage(name)

    # Prepare properties
    props = {"name": cleaned_name}
    if properties:
        # Clean properties as well
        cleaned_properties = _clean_properties_for_storage(properties)
        props.update(cleaned_properties)

    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())

    # Create node
    return adapter.create_node("Episode", props)

async def create_fact_node(body: str, episode_uuid: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create a Fact node and link it to an Episode.

    Args:
        body: Text content of the fact
        episode_uuid: UUID of the episode
        properties: Additional properties

    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()

    # Clean the body text to remove OCR artifacts
    cleaned_body = _clean_text_for_storage(body)

    # Prepare properties
    props = {"body": cleaned_body}
    if properties:
        # Clean properties as well
        cleaned_properties = _clean_properties_for_storage(properties)
        props.update(cleaned_properties)

    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())

    # Create node
    fact_uuid = adapter.create_node("Fact", props)

    if fact_uuid:
        # Create relationship
        adapter.create_relationship(episode_uuid, fact_uuid, "CONTAINS")

    return fact_uuid

async def create_entity_node(name: str, entity_type: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create an Entity node.

    Args:
        name: Name of the entity
        entity_type: Type of the entity
        properties: Additional properties

    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()

    # Clean the name and type
    cleaned_name = _clean_text_for_storage(name)
    cleaned_type = _clean_text_for_storage(entity_type)

    # Prepare properties
    props = {"name": cleaned_name, "type": cleaned_type}
    if properties:
        # Clean properties as well
        cleaned_properties = _clean_properties_for_storage(properties)
        props.update(cleaned_properties)

    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())

    # Create node
    return adapter.create_node("Entity", props)

def _clean_text_for_storage(text: str) -> str:
    """
    Clean text content to remove OCR artifacts and metadata before storage.

    Args:
        text: Raw text content

    Returns:
        Cleaned text safe for database storage
    """
    import re

    if not isinstance(text, str):
        text = str(text)

    # Remove OCR object representations
    text = re.sub(r'OCRPageObject\([^)]*\)', '', text)
    text = re.sub(r'OCRImageObject\([^)]*\)', '', text)
    text = re.sub(r'OCRPageDimensions\([^)]*\)', '', text)
    text = re.sub(r'pages=\[[^\]]*\]', '', text)
    text = re.sub(r'images=\[[^\]]*\]', '', text)
    text = re.sub(r'dimensions=[^,)]*', '', text)
    text = re.sub(r'index=\d+', '', text)
    text = re.sub(r'markdown=["\'][^"\']*["\']', '', text)

    # Remove image references
    text = re.sub(r'img-\d+\.jpeg', '', text)
    text = re.sub(r'!\[.*?\]\(.*?\)', '', text)

    # Additional cleaning for Cypher safety
    # Remove or replace problematic patterns that can break Cypher queries
    try:
        text = re.sub(r'[^\x20-\x7E\s]', '', text)  # Remove non-ASCII characters except whitespace
    except re.error:
        # If regex fails, do simple character filtering
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')

    # Safe character replacements without regex
    text = text.replace(';', ',')    # Replace semicolons with commas to prevent multiple statements
    text = text.replace('`', "'")    # Replace backticks with single quotes

    # Normalize multiple backslashes safely
    while '\\\\' in text:
        text = text.replace('\\\\', '\\')

    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s*\n', '\n\n', text)

    # Truncate very long text to prevent database issues
    if len(text) > 40000:  # Reduced from 50000 to be safer
        text = text[:40000] + "..."

    return text.strip()

def _clean_properties_for_storage(properties: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean properties dictionary to remove complex objects and ensure database compatibility.

    Args:
        properties: Raw properties dictionary

    Returns:
        Cleaned properties safe for database storage
    """
    cleaned = {}

    for key, value in properties.items():
        if isinstance(value, str):
            # Clean string values
            cleaned[key] = _clean_text_for_storage(value)
        elif isinstance(value, (int, float, bool)):
            # Keep simple types as-is
            cleaned[key] = value
        elif value is None:
            # Keep None values
            cleaned[key] = value
        elif isinstance(value, (list, dict)):
            # Convert complex objects to JSON strings
            import json
            try:
                json_str = json.dumps(value)
                # Clean the JSON string as well
                cleaned[key] = _clean_text_for_storage(json_str)
            except (TypeError, ValueError):
                # If can't serialize, convert to string and clean
                cleaned[key] = _clean_text_for_storage(str(value))
        else:
            # For other types, convert to string and clean
            cleaned[key] = _clean_text_for_storage(str(value))

    return cleaned

async def link_entity_to_fact(entity_uuid: str, fact_uuid: str, properties: Dict[str, Any] = None) -> bool:
    """
    Link an Entity to a Fact.
    
    Args:
        entity_uuid: UUID of the entity
        fact_uuid: UUID of the fact
        properties: Relationship properties
        
    Returns:
        True if the relationship was created, False otherwise
    """
    adapter = await get_falkordb_adapter()
    
    # Create relationship
    return adapter.create_relationship(fact_uuid, entity_uuid, "MENTIONS", properties)

async def create_relationship(from_uuid: str, to_uuid: str, rel_type: str, properties: Dict[str, Any] = None) -> bool:
    """
    Create a relationship between two nodes.
    
    Args:
        from_uuid: UUID of the source node
        to_uuid: UUID of the target node
        rel_type: Relationship type
        properties: Relationship properties
        
    Returns:
        True if the relationship was created, False otherwise
    """
    adapter = await get_falkordb_adapter()
    
    # Create relationship
    return adapter.create_relationship(from_uuid, to_uuid, rel_type, properties)

async def get_entities_by_type(entity_type: str, limit: int = 100) -> List[Dict[str, Any]]:
    """
    Get entities by type.
    
    Args:
        entity_type: Type of the entity
        limit: Maximum number of entities to return
        
    Returns:
        List of entity properties
    """
    query = f"""
    MATCH (e:Entity)
    WHERE e.type = '{entity_type}'
    RETURN e
    LIMIT {limit}
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return [row[0] for row in result[1]]
    
    return []

async def get_entity_types() -> List[str]:
    """
    Get all entity types.
    
    Returns:
        List of entity types
    """
    query = """
    MATCH (e:Entity)
    RETURN DISTINCT e.type
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return [row[0] for row in result[1]]
    
    return []

async def get_entity_counts() -> Dict[str, int]:
    """
    Get counts of entities by type.
    
    Returns:
        Dictionary mapping entity types to counts
    """
    query = """
    MATCH (e:Entity)
    RETURN e.type, count(e) as count
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return {row[0]: row[1] for row in result[1]}
    
    return {}
