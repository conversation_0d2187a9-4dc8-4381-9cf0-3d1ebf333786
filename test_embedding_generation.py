#!/usr/bin/env python3
"""
Test embedding generation to see why it's failing.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter
from services.embedding_processor import EmbeddingProcessor

async def test_embedding_generation():
    """Test the embedding generation process."""
    
    print("🔍 Testing embedding generation...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # 1. Get latest episode
        print("\n1. Getting latest episode...")
        query = '''
        MATCH (ep:Episode)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN ep.uuid as uuid, ep.name as name
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or not result[1]:
            print("❌ No episodes found")
            return
            
        episode_id = result[1][0][0]
        episode_name = result[1][0][1]
        
        print(f"✅ Episode: {episode_id}")
        print(f"   Name: {episode_name}")
        
        # 2. Get facts for this episode
        print("\n2. Getting facts...")
        fact_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid as uuid, f.body as body
        ORDER BY f.chunk_index
        '''
        fact_result = adapter.execute_cypher(fact_query)
        
        if not fact_result or len(fact_result) <= 1 or not fact_result[1]:
            print("❌ No facts found")
            return
            
        facts = fact_result[1]
        print(f"✅ Found {len(facts)} facts")
        
        for i, fact in enumerate(facts):
            fact_uuid = fact[0]
            fact_body = fact[1]
            print(f"   Fact {i+1}: {fact_uuid[:8]}... ({len(fact_body)} chars)")
        
        # 3. Test embedding processor
        print("\n3. Testing embedding processor...")
        processor = EmbeddingProcessor()
        
        # Check if Ollama client is available
        if hasattr(processor, 'ollama_client') and processor.ollama_client:
            print("✅ Ollama client available")
            
            # Test generating embedding for first fact
            first_fact = facts[0]
            fact_uuid = first_fact[0]
            fact_body = first_fact[1]
            
            print(f"\n4. Testing embedding generation for fact: {fact_uuid[:8]}...")
            print(f"   Fact body sample: {fact_body[:100]}...")
            
            try:
                # Test Ollama embedding generation directly
                embedding = processor.ollama_client.generate_embeddings(
                    fact_body,
                    model="snowflake-arctic-embed2"
                )
                
                if embedding:
                    print(f"✅ Embedding generated successfully!")
                    print(f"   Embedding dimensions: {len(embedding)}")
                    print(f"   First 5 values: {embedding[:5]}")
                    
                    # Test storing in Redis
                    print(f"\n5. Testing Redis storage...")
                    success = await processor._store_embedding(fact_uuid, embedding, adapter)
                    if success:
                        print("✅ Embedding stored in Redis successfully!")
                    else:
                        print("❌ Failed to store embedding in Redis")
                        
                else:
                    print("❌ No embedding generated")
                    
            except Exception as e:
                print(f"❌ Error generating embedding: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print("❌ Ollama client not available")
            print(f"   Processor attributes: {dir(processor)}")
        
        # 6. Test full document embedding generation
        print(f"\n6. Testing full document embedding generation...")
        try:
            result = await processor.generate_embeddings_for_document(episode_id)
            print(f"📊 Full generation result: {result}")
        except Exception as e:
            print(f"❌ Full generation failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_embedding_generation())
