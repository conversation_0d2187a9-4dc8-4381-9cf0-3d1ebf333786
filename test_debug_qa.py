#!/usr/bin/env python3
"""
Test Q&A with debug logging.
"""

import requests
import json

def test_debug_qa():
    """Test Q&A with debug logging."""
    print("🔄 Testing Q&A with Debug Logging...")
    
    response = requests.post(
        'http://localhost:9753/api/qa/answer',
        json={
            'question': 'cocoa health benefits',
            'max_facts': 5,
            'response_length': 'brief'
        },
        timeout=30
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        answer = data.get('answer', 'No answer')
        print(f"Answer: {answer[:100]}...")
    else:
        print(f"Error: {response.text}")

if __name__ == "__main__":
    test_debug_qa()
