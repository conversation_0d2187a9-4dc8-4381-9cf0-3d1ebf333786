#!/usr/bin/env python3
"""
Install PyMuPDF for better PDF text extraction.
"""

import subprocess
import sys

def install_pymupdf():
    """Install PyMuPDF package."""
    try:
        print("📦 Installing PyMuPDF for better PDF text extraction...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "PyMuPDF"
        ], capture_output=True, text=True, check=True)
        
        print("✅ PyMuPDF installed successfully!")
        print(result.stdout)
        
        # Test the installation
        try:
            import fitz
            print("✅ PyMuPDF import test successful!")
            return True
        except ImportError as e:
            print(f"❌ PyMuPDF import test failed: {e}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyMuPDF: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = install_pymupdf()
    if success:
        print("🎉 PyMuPDF is ready for use!")
    else:
        print("💡 Falling back to PyPDF2 for text extraction")
