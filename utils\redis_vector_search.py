"""
Redis Vector Search client for vector similarity search.

This module provides functions to interact with <PERSON><PERSON>ack for vector search capabilities,
complementing FalkorDB's graph database functionality.
"""

import os
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Union
import redis
try:
    from redis.commands.search.field import <PERSON><PERSON><PERSON><PERSON>, TextField
    from redis.commands.search.index_definition import IndexDefinition, IndexType
except ImportError:
    # Fallback for older Redis versions
    try:
        from redisearch import VectorField, TextField, IndexDefinition
        IndexType = None
    except ImportError:
        # If neither works, create dummy classes
        class VectorField:
            def __init__(self, *args, **kwargs):
                pass
        class TextField:
            def __init__(self, *args, **kwargs):
                pass
        class IndexDefinition:
            def __init__(self, *args, **kwargs):
                pass
        IndexType = None

# Set up logger
logger = logging.getLogger(__name__)

# Redis Vector Search configuration
REDIS_VECTOR_SEARCH_HOST = os.environ.get("REDIS_VECTOR_SEARCH_HOST", "localhost")
REDIS_VECTOR_SEARCH_PORT = int(os.environ.get("REDIS_VECTOR_SEARCH_PORT", 6380))
REDIS_VECTOR_SEARCH_PASSWORD = None  # No password
REDIS_VECTOR_SEARCH_INDEX_NAME = "fact_embeddings"

# Singleton Redis client
_redis_client = None

def get_redis_vector_search_client():
    """
    Get Redis client for vector search.

    Returns:
        Redis client instance
    """
    global _redis_client

    if _redis_client is None:
        try:
            # Only use password if it's provided
            if REDIS_VECTOR_SEARCH_PASSWORD:
                _redis_client = redis.Redis(
                    host=REDIS_VECTOR_SEARCH_HOST,
                    port=REDIS_VECTOR_SEARCH_PORT,
                    password=REDIS_VECTOR_SEARCH_PASSWORD,
                    decode_responses=False  # Important for binary vector data
                )
            else:
                _redis_client = redis.Redis(
                    host=REDIS_VECTOR_SEARCH_HOST,
                    port=REDIS_VECTOR_SEARCH_PORT,
                    decode_responses=False  # Important for binary vector data
                )

            # Test the connection
            _redis_client.ping()
            logger.info(f"Connected to Redis Vector Search at {REDIS_VECTOR_SEARCH_HOST}:{REDIS_VECTOR_SEARCH_PORT}")
        except Exception as e:
            logger.error(f"Error connecting to Redis Vector Search: {e}")
            raise

    return _redis_client

def create_vector_index(dimensions: int = 1024, distance_metric: str = "COSINE"):
    """
    Create a vector index in Redis for fact embeddings.

    Args:
        dimensions: Dimensionality of the embedding vectors (default: 1024 for compatibility with various models)
        distance_metric: Distance metric to use (COSINE, L2, IP)

    Returns:
        True if index was created, False otherwise
    """
    try:
        client = get_redis_vector_search_client()

        # Check if index already exists
        try:
            client.ft(REDIS_VECTOR_SEARCH_INDEX_NAME).info()
            logger.info(f"Vector index '{REDIS_VECTOR_SEARCH_INDEX_NAME}' already exists")
            return True
        except:
            # Index doesn't exist, create it
            pass

        # Define the schema with a vector field
        schema = [
            TextField("embedding_id"),
            TextField("fact_uuid"),
            TextField("episode_uuid"),
            TextField("body"),
            TextField("created_at"),
            TextField("dimensions"),
            VectorField("embedding",
                        "HNSW",
                        {"TYPE": "FLOAT32",
                         "DIM": dimensions,
                         "DISTANCE_METRIC": distance_metric})
        ]

        try:
            # Create the index
            client.ft(REDIS_VECTOR_SEARCH_INDEX_NAME).create_index(
                schema,
                definition=IndexDefinition(prefix=["embedding:"], index_type=IndexType.HASH)
            )
        except redis.exceptions.ResponseError as e:
            # If index already exists, that's fine
            if "Index already exists" in str(e):
                logger.info(f"Vector index '{REDIS_VECTOR_SEARCH_INDEX_NAME}' already exists")
            else:
                raise

        logger.info(f"Created vector index '{REDIS_VECTOR_SEARCH_INDEX_NAME}' with {dimensions} dimensions")
        return True

    except Exception as e:
        logger.error(f"Error creating vector index: {e}")
        return False

def store_embedding(fact_uuid: str, episode_uuid: str, body: str, embedding: List[float], embedding_id: str = None):
    """
    Store an embedding in Redis Vector Search.

    Args:
        fact_uuid: UUID of the fact
        episode_uuid: UUID of the episode
        body: Text content of the fact
        embedding: Embedding vector as a list of floats
        embedding_id: Optional unique ID for the embedding (defaults to fact_uuid)

    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_vector_search_client()

        # Use fact_uuid as embedding_id if not provided
        if embedding_id is None:
            embedding_id = f"{fact_uuid}_embedding"

        # Convert embedding to numpy array
        embedding_np = np.array(embedding, dtype=np.float32)

        # Get current timestamp
        import datetime
        timestamp = datetime.datetime.now().isoformat()

        # Log embedding dimensions
        dimensions = len(embedding)
        logger.info(f"Storing embedding with {dimensions} dimensions for fact {fact_uuid}")

        # Store in Redis
        client.hset(
            f"embedding:{embedding_id}",
            mapping={
                "embedding_id": embedding_id,
                "fact_uuid": fact_uuid,
                "episode_uuid": episode_uuid,
                "body": body,
                "embedding": embedding_np.tobytes(),
                "dimensions": dimensions,
                "created_at": timestamp,
                "model": "snowflake-arctic-embed2"  # Add model information
            }
        )

        logger.info(f"Successfully stored embedding for fact {fact_uuid} with ID {embedding_id}")
        return True

    except Exception as e:
        logger.error(f"Error storing embedding for fact {fact_uuid}: {e}")
        return False

def semantic_search(query_embedding: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
    """
    Perform semantic search using vector embeddings.

    Args:
        query_embedding: Embedding vector for the query
        top_k: Number of results to return

    Returns:
        List of matching facts with scores
    """
    try:
        client = get_redis_vector_search_client()

        # Convert query embedding to numpy array
        query_embedding_np = np.array(query_embedding, dtype=np.float32)

        # Get all embedding keys
        all_keys = client.keys("embedding:*")

        # Manually calculate cosine similarity for each embedding
        search_results = []

        for key in all_keys:
            try:
                # Get the embedding data
                embedding_data = client.hgetall(key)

                # Skip if no embedding data
                if not embedding_data:
                    continue

                # Get the embedding
                if b'embedding' not in embedding_data:
                    continue

                # Convert the stored embedding back to a numpy array
                stored_embedding_bytes = embedding_data[b'embedding']
                dimensions = int(embedding_data[b'dimensions'].decode()) if isinstance(embedding_data[b'dimensions'], bytes) else int(embedding_data[b'dimensions'])
                stored_embedding = np.frombuffer(stored_embedding_bytes, dtype=np.float32)

                # Ensure the embedding has the right shape
                if len(stored_embedding) != dimensions:
                    logger.warning(f"Embedding dimensions mismatch: expected {dimensions}, got {len(stored_embedding)}")
                    continue

                # Calculate cosine similarity
                dot_product = np.dot(query_embedding_np, stored_embedding)
                query_norm = np.linalg.norm(query_embedding_np)
                stored_norm = np.linalg.norm(stored_embedding)

                if query_norm == 0 or stored_norm == 0:
                    similarity = 0
                else:
                    similarity = dot_product / (query_norm * stored_norm)

                # Decode byte fields if necessary
                fact_uuid = embedding_data[b'fact_uuid'].decode() if isinstance(embedding_data[b'fact_uuid'], bytes) else embedding_data[b'fact_uuid']
                episode_uuid = embedding_data[b'episode_uuid'].decode() if isinstance(embedding_data[b'episode_uuid'], bytes) else embedding_data[b'episode_uuid']
                body = embedding_data[b'body'].decode() if isinstance(embedding_data[b'body'], bytes) else embedding_data[b'body']
                embedding_id = embedding_data[b'embedding_id'].decode() if isinstance(embedding_data[b'embedding_id'], bytes) else embedding_data[b'embedding_id']

                # Add to results
                search_results.append({
                    "embedding_id": embedding_id,
                    "fact_uuid": fact_uuid,
                    "episode_uuid": episode_uuid,
                    "body": body,
                    "score": float(similarity)
                })
            except Exception as e:
                logger.warning(f"Error processing embedding {key}: {e}")
                continue

        # Sort by similarity score (descending)
        search_results.sort(key=lambda x: x["score"], reverse=True)

        # Return top k results
        return search_results[:top_k]

    except Exception as e:
        logger.error(f"Error performing semantic search: {e}")
        return []

def hybrid_search(query_text: str, query_embedding: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
    """
    Perform hybrid search combining text and vector similarity.

    Args:
        query_text: Text query for keyword search
        query_embedding: Embedding vector for semantic search
        top_k: Number of results to return

    Returns:
        List of matching facts with scores
    """
    try:
        client = get_redis_vector_search_client()

        # Convert query embedding to numpy array
        query_embedding_np = np.array(query_embedding, dtype=np.float32)

        # Get all embedding keys
        all_keys = client.keys("embedding:*")

        # Manually calculate cosine similarity for each embedding
        search_results = []

        for key in all_keys:
            try:
                # Get the embedding data
                embedding_data = client.hgetall(key)

                # Skip if no embedding data
                if not embedding_data:
                    continue

                # Get the embedding
                if b'embedding' not in embedding_data:
                    continue

                # Get the body text
                if b'body' not in embedding_data:
                    continue

                body = embedding_data[b'body'].decode() if isinstance(embedding_data[b'body'], bytes) else embedding_data[b'body']

                # Simple text matching - check if query terms are in the body
                # Skip if no match
                if not any(term.lower() in body.lower() for term in query_text.split()):
                    continue

                # Convert the stored embedding back to a numpy array
                stored_embedding_bytes = embedding_data[b'embedding']

                # Get dimensions from the stored data
                dimensions = int(embedding_data[b'dimensions'].decode()) if isinstance(embedding_data[b'dimensions'], bytes) else int(embedding_data[b'dimensions'])

                # Convert bytes to numpy array
                stored_embedding = np.frombuffer(stored_embedding_bytes, dtype=np.float32)

                # Check if dimensions match
                if len(stored_embedding) != dimensions:
                    logger.warning(f"Embedding dimensions mismatch: expected {dimensions}, got {len(stored_embedding)}")
                    continue

                # Check if query and stored embedding dimensions match
                if len(query_embedding_np) != len(stored_embedding):
                    logger.warning(f"Query and stored embedding dimensions mismatch: query {len(query_embedding_np)}, stored {len(stored_embedding)}")
                    continue

                # Calculate cosine similarity
                try:
                    dot_product = np.dot(query_embedding_np, stored_embedding)
                    query_norm = np.linalg.norm(query_embedding_np)
                    stored_norm = np.linalg.norm(stored_embedding)

                    if query_norm == 0 or stored_norm == 0:
                        similarity = 0
                    else:
                        similarity = dot_product / (query_norm * stored_norm)
                except Exception as e:
                    logger.warning(f"Error calculating similarity: {e}")
                    continue

                # Decode byte fields if necessary
                fact_uuid = embedding_data[b'fact_uuid'].decode() if isinstance(embedding_data[b'fact_uuid'], bytes) else embedding_data[b'fact_uuid']
                episode_uuid = embedding_data[b'episode_uuid'].decode() if isinstance(embedding_data[b'episode_uuid'], bytes) else embedding_data[b'episode_uuid']
                embedding_id = embedding_data[b'embedding_id'].decode() if isinstance(embedding_data[b'embedding_id'], bytes) else embedding_data[b'embedding_id']

                # Add to results
                search_results.append({
                    "embedding_id": embedding_id,
                    "fact_uuid": fact_uuid,
                    "episode_uuid": episode_uuid,
                    "body": body,
                    "score": float(similarity)
                })
            except Exception as e:
                logger.warning(f"Error processing embedding {key}: {e}")
                continue

        # Sort by similarity score (descending)
        search_results.sort(key=lambda x: x["score"], reverse=True)

        # Return top k results
        return search_results[:top_k]

    except Exception as e:
        logger.error(f"Error performing hybrid search: {e}")
        return []
