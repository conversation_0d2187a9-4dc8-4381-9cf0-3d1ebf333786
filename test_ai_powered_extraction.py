#!/usr/bin/env python3
"""
Test the AI-powered Intelligent Reference Extractor with API keys.
"""

import asyncio
import os
from pathlib import Path
from services.intelligent_reference_extractor import get_intelligent_reference_extractor
from utils.logging_utils import get_logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

async def test_ai_powered_extraction():
    """Test the AI-powered reference extraction."""
    print("🤖 Testing AI-Powered Intelligent Reference Extraction")
    print("=" * 70)
    
    # Check API keys
    openrouter_key = os.getenv('OPEN_ROUTER_API_KEY') or os.getenv('OPENROUTER_API_KEY')
    mistral_key = os.getenv('MISTRAL_API_KEY')
    
    print(f"🔑 API Key Status:")
    print(f"   OpenRouter: {'✅ Available' if openrouter_key else '❌ Missing'}")
    print(f"   Mistral: {'✅ Available' if mistral_key else '❌ Missing'}")
    
    if not openrouter_key and not mistral_key:
        print(f"❌ No API keys found! Please check your .env file.")
        return False
    
    # Sample text with many references
    sample_text = """
    REFERENCES
    
    Australian Institute of Health and Welfare. Chronic Kidney Disease: Australian Facts. Aust Gov. 2023;(Feburary):1-121.
    
    Liyanage T, Toyama T, Hockham C, et al. BMJ Glob Heal. 2022;7(1):1-9. doi:10.1136/bmjgh-***********
    
    Zoccali, C., Vanholder, R., Massy, Z. A., Ortiz, A., Sarafidis (2017). Nature Reviews Nephrology, 13(6), 344–358. https://doi.org/10.1038/nrneph.2017.52
    
    Hill, N. R., Fatoba, S. T., Oke, J. L., Hirst, J. A., Callaghan, A. O., Lasserson, D. S., & Hobbs, F. D. R. (2016). PLoS ONE, 11(7), 1–18.
    
    Miwa, K., Tanaka, M., Okazaki, S., Furukado, S., Yagita, Y., Sakaguchi, M., Mochizuki, H., & Kitagawa, K. (2014). Neurology, 82(12), 1051–1057.
    
    Bowe B, Xie Y, Xu E, Al-Aly Z. J Am Soc Nephrol. 2021;32(11):2851-2862. doi:10.1681/ASN.**********
    
    Mahalingasivam V, Su G, Iwagami M, Davids MR, Wetmore JB, Nitsch D. Nat Rev Nephrol. 2022;18(8):485-498. doi:10.1038/s41581-022-00570-3
    
    Huang C, Huang L, Wang Y, et al. Lancet. 2021;397(10270):220-232. doi:10.1016/S0140-6736(20)32656-8
    
    Hsu CM, Gupta S, Tighiouart H, et al. Am J Kidney Dis. 2022;79(3):404-416.e1. doi:10.1053/j.ajkd.2021.11.004
    
    Gur E, Levy D, Topaz G, et al. Clin Exp Nephrol. 2022;26(5):445-452. doi:10.1007/s10157-022-02180-6
    
    Self WH, Shotwell MS, Gibbs KW, et al. JAMA. 2023;329(14):1170-1182. doi:10.1001/jama.2023.3546
    
    Investigators WC for the R-C. JAMA. 2023;329(14):1183-1196. doi:10.1001/jama.2023.4480
    
    Some text with in-text citations like Smith et al. (2022) and (Jones, 2021) should also be found.
    
    Additional references:
    Bergström J. J Am Soc Nephrol. 1995;6(5). https://journals.lww.com/jasn/Fulltext/1995/11000/Nutrition_and_mortality_in_hemodialysis_.2.aspx
    
    Wetzels, J. F. M., Kiemeney, L. A. L. M., Swinkels, D. W., Willems, H. L., & Heijer, M. Den. (2007). Kidney International, 72(5), 632–637. https://doi.org/10.1038/sj.ki.5002374
    
    Image from: El-arif G, Farhat A, Khazaal S, et al. Molecules. 2021;26(22):1-31.
    """
    
    try:
        # Get the intelligent extractor
        print(f"\n🔧 Initializing Intelligent Reference Extractor...")
        extractor = get_intelligent_reference_extractor()
        
        if not extractor.ai_available:
            print(f"❌ AI extraction not available despite API keys being present")
            return False
        
        print(f"✅ AI extraction is available!")
        
        # Test extraction
        print(f"\n🚀 Starting comprehensive extraction with AI...")
        print(f"📄 Sample text: {len(sample_text):,} characters")
        print(f"📊 Expected references: ~15+ (including AI-found ones)")
        
        result = await extractor.extract_references_comprehensive(
            sample_text, 
            "test_ai_extraction.pdf"
        )
        
        print(f"\n✅ AI-powered extraction completed!")
        print(f"📊 Total references found: {result['total_found']}")
        print(f"🎯 Confidence score: {result['confidence_score']}")
        print(f"🔧 Extraction methods: {result['extraction_methods']}")
        
        # Check AI contribution
        ai_refs = result['extraction_methods'].get('ai_powered', 0)
        pattern_refs = result['extraction_methods'].get('pattern_based', 0)
        line_refs = result['extraction_methods'].get('line_analysis', 0)
        
        print(f"\n📊 Method Breakdown:")
        print(f"   🤖 AI-powered: {ai_refs} references")
        print(f"   🔍 Pattern-based: {pattern_refs} references")
        print(f"   📝 Line analysis: {line_refs} references")
        print(f"   📚 Section-based: {result['extraction_methods'].get('section_based', 0)} references")
        
        # Show sample references
        print(f"\n📋 Sample references found:")
        for i, ref_data in enumerate(result['references'][:10], 1):
            ref_text = ref_data['text']
            print(f"{i:2d}. {ref_text[:120]}{'...' if len(ref_text) > 120 else ''}")
        
        if result['total_found'] > 10:
            print(f"... and {result['total_found'] - 10} more references")
        
        # Performance assessment
        if ai_refs > 0:
            print(f"\n🎉 AI EXTRACTION WORKING!")
            print(f"   AI found {ai_refs} additional references")
            print(f"   Total improvement with AI: {result['total_found']} references")
        else:
            print(f"\n⚠️ AI extraction didn't find additional references")
            print(f"   This might be normal if pattern-based extraction was comprehensive")
        
        if result['total_found'] >= 15:
            print(f"\n🎉 EXCELLENT: Found {result['total_found']} references!")
        elif result['total_found'] >= 10:
            print(f"\n✅ GOOD: Found {result['total_found']} references")
        else:
            print(f"\n⚠️ FAIR: Found {result['total_found']} references (expected 15+)")
        
        return True
        
    except Exception as e:
        print(f"❌ AI-powered extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_with_kidney_document():
    """Test AI extraction with the actual kidney document."""
    print(f"\n" + "=" * 70)
    print(f"🔬 Testing AI Extraction with Kidney Document")
    print(f"=" * 70)
    
    # Find the kidney document
    document_path = Path("uploads/5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal Oseki.pdf")
    
    if not document_path.exists():
        print(f"❌ Kidney document not found: {document_path}")
        return False
    
    try:
        # Extract text first (simplified - would use OCR in real scenario)
        print(f"📄 Found kidney document: {document_path.name}")
        print(f"📊 File size: {document_path.stat().st_size:,} bytes")
        
        # For this test, we'll use the unified pipeline to get the text
        from unified_ingestion_pipeline import get_unified_pipeline
        
        print(f"🔧 Using unified pipeline to extract text...")
        pipeline = await get_unified_pipeline()
        
        # Process with AI-powered reference extraction
        result = await pipeline.process_document(
            file_path=document_path,
            extract_references=True,
            extract_entities=False,
            extract_metadata=False,
            generate_embeddings=False
        )
        
        if result.get('success', False):
            refs_found = result.get('references', 0)
            print(f"\n✅ AI-powered pipeline extraction completed!")
            print(f"📊 References found: {refs_found}")
            
            if refs_found >= 100:
                print(f"🎉 OUTSTANDING: Found {refs_found} references with AI!")
                print(f"   This is a massive improvement over the original 6 references")
            elif refs_found >= 50:
                print(f"🎉 EXCELLENT: Found {refs_found} references with AI!")
            elif refs_found >= 30:
                print(f"✅ GOOD: Found {refs_found} references")
            else:
                print(f"⚠️ FAIR: Found {refs_found} references")
            
            return True
        else:
            print(f"❌ Pipeline processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Kidney document test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Testing AI-Powered Reference Extraction")
    print("=" * 70)
    
    # Test 1: Sample text with AI
    sample_success = await test_ai_powered_extraction()
    
    # Test 2: Kidney document with AI
    kidney_success = await test_with_kidney_document()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 AI-Powered Test Results:")
    print(f"   Sample Text: {'✅ PASS' if sample_success else '❌ FAIL'}")
    print(f"   Kidney Document: {'✅ PASS' if kidney_success else '❌ FAIL'}")
    
    if sample_success and kidney_success:
        print(f"\n🎉 AI-POWERED EXTRACTION FULLY WORKING!")
        print(f"The system now uses AI to find references that patterns miss!")
    else:
        print(f"\n⚠️ Some AI tests failed - check API keys and configuration")

if __name__ == "__main__":
    asyncio.run(main())
