#!/usr/bin/env python3
"""
Check facts that don't have entity mentions.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter

async def check_facts_without_entities():
    adapter = GraphitiFalkorDBAdapter('graphiti')
    
    # Check facts that don't have entity mentions
    query = '''
    MATCH (f:Fact)
    WHERE NOT (f)-[:MENTIONS]->(:Entity)
    RETURN f.uuid as fact_uuid, f.body as fact_text
    LIMIT 5
    '''
    result = adapter.execute_cypher(query)
    print('Facts without entity mentions:')
    if result and len(result) > 1:
        headers = result[0]
        for row in result[1]:
            fact_data = dict(zip(headers, row))
            print(f'  Fact UUID: {fact_data["fact_uuid"]}')
            print(f'  Text: {fact_data["fact_text"][:200]}...')
            print('  ---')
    else:
        print('  No facts without entities found')

if __name__ == "__main__":
    asyncio.run(check_facts_without_entities())
