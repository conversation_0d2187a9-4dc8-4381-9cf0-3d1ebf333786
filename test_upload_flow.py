#!/usr/bin/env python3
"""
Test the complete upload flow to identify issues.
"""

import asyncio
import uuid
from pathlib import Path
from utils.enhanced_progress_tracker import EnhancedProgressTracker
from utils.websocket_manager import get_websocket_manager
from unified_ingestion_pipeline import UnifiedIngestionPipeline

async def test_upload_flow():
    """Test the complete upload flow."""
    
    print("🧪 Testing complete upload flow...")
    
    # Test file path (use any existing PDF)
    test_file = Path("uploads").glob("*.pdf")
    test_file = next(test_file, None)
    
    if not test_file:
        print("❌ No PDF files found in uploads directory")
        return
    
    print(f"📄 Testing with file: {test_file.name}")
    
    try:
        # 1. Test Enhanced Progress Tracker
        operation_id = str(uuid.uuid4())
        websocket_manager = get_websocket_manager()
        tracker = EnhancedProgressTracker(operation_id, test_file.name, websocket_manager)
        
        print("✅ Enhanced Progress Tracker created")
        
        # 2. Test Unified Pipeline
        pipeline = UnifiedIngestionPipeline()
        await pipeline.initialize()
        
        print("✅ Unified Pipeline initialized")
        
        # 3. Test Database Connection
        if pipeline.database_adapter:
            test_query = "MATCH (e:Episode) RETURN count(e) as count LIMIT 1"
            result = pipeline.database_adapter.execute_cypher(test_query)
            print(f"✅ Database connection OK - Result: {result}")
        else:
            print("❌ No database adapter")
        
        # 4. Test Processing (just text extraction)
        print("🔄 Testing text extraction...")
        
        from utils.mistral_ocr import MistralOCRProcessor
        ocr_processor = MistralOCRProcessor()
        
        extracted_text = await ocr_processor.process_pdf(test_file)
        print(f"✅ Text extracted: {len(extracted_text)} characters")
        
        # 5. Test Episode Creation
        print("🔄 Testing episode creation...")
        
        chunks = ["Test chunk 1", "Test chunk 2"]
        episode_id = await pipeline._create_episode_and_facts(test_file, chunks)
        
        if episode_id:
            print(f"✅ Episode created: {episode_id}")
            
            # Verify episode exists
            verify_query = f"MATCH (e:Episode {{uuid: '{episode_id}'}}) RETURN e.uuid"
            verify_result = pipeline.database_adapter.execute_cypher(verify_query)
            print(f"📊 Episode verification: {verify_result}")
        else:
            print("❌ Episode creation failed")
        
        print("🎉 Upload flow test completed!")
        
    except Exception as e:
        print(f"❌ Error in upload flow test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_upload_flow())
