#!/usr/bin/env python3
"""
Compare entity extraction quality and speed between OpenRouter Llama Maverick and Ollama qwen3-4b.
"""

import asyncio
import time
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

# Sample medical text for testing
SAMPLE_MEDICAL_TEXT = """
Ginger (Zingiber officinale) contains several bioactive compounds including gingerols, shogaols, and paradols. 
These compounds exhibit anti-inflammatory properties and may help reduce oxidative stress. Studies have shown 
that ginger supplementation can improve symptoms of osteoarthritis and reduce nausea in cancer patients 
undergoing chemotherapy. The recommended dosage is typically 1-3 grams per day. Ginger may interact with 
anticoagulant medications such as warfarin, so patients should consult their healthcare provider before use.
"""

async def test_openrouter_extraction():
    """Test entity extraction using OpenRouter + Llama Maverick."""
    print("🔄 Testing OpenRouter + Llama Maverick...")
    
    # Temporarily set environment for OpenRouter
    original_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
    original_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
    
    os.environ['ENTITY_EXTRACTION_PROVIDER'] = 'openrouter'
    os.environ['ENTITY_EXTRACTION_MODEL'] = 'meta-llama/llama-4-maverick'
    
    try:
        extractor = LLMEntityExtractor()
        
        start_time = time.time()
        entities = await extractor.extract_entities(SAMPLE_MEDICAL_TEXT)
        end_time = time.time()
        
        extraction_time = end_time - start_time
        
        print(f"   ✅ OpenRouter Results:")
        print(f"      Time: {extraction_time:.2f} seconds")
        print(f"      Entities found: {len(entities)}")
        
        # Show sample entities
        for i, entity in enumerate(entities[:5]):
            name = entity.get('name', 'Unknown')
            entity_type = entity.get('type', 'Unknown')
            print(f"      {i+1}. {name} ({entity_type})")
        
        return {
            'provider': 'OpenRouter + Llama Maverick',
            'time': extraction_time,
            'count': len(entities),
            'entities': entities
        }
        
    except Exception as e:
        print(f"   ❌ OpenRouter Error: {e}")
        return {
            'provider': 'OpenRouter + Llama Maverick',
            'time': 0,
            'count': 0,
            'entities': [],
            'error': str(e)
        }
    finally:
        # Restore original settings
        if original_provider:
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_provider
        if original_model:
            os.environ['ENTITY_EXTRACTION_MODEL'] = original_model

async def test_ollama_extraction():
    """Test entity extraction using Ollama + qwen3-4b."""
    print("\n🔄 Testing Ollama + qwen3-4b...")
    
    # Temporarily set environment for Ollama
    original_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
    original_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
    
    os.environ['ENTITY_EXTRACTION_PROVIDER'] = 'local'
    os.environ['ENTITY_EXTRACTION_MODEL'] = 'qwen3-4b'
    
    try:
        extractor = LLMEntityExtractor()
        
        start_time = time.time()
        entities = await extractor.extract_entities(SAMPLE_MEDICAL_TEXT)
        end_time = time.time()
        
        extraction_time = end_time - start_time
        
        print(f"   ✅ Ollama Results:")
        print(f"      Time: {extraction_time:.2f} seconds")
        print(f"      Entities found: {len(entities)}")
        
        # Show sample entities
        for i, entity in enumerate(entities[:5]):
            name = entity.get('name', 'Unknown')
            entity_type = entity.get('type', 'Unknown')
            print(f"      {i+1}. {name} ({entity_type})")
        
        return {
            'provider': 'Ollama + qwen3-4b',
            'time': extraction_time,
            'count': len(entities),
            'entities': entities
        }
        
    except Exception as e:
        print(f"   ❌ Ollama Error: {e}")
        return {
            'provider': 'Ollama + qwen3-4b',
            'time': 0,
            'count': 0,
            'entities': [],
            'error': str(e)
        }
    finally:
        # Restore original settings
        if original_provider:
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_provider
        if original_model:
            os.environ['ENTITY_EXTRACTION_MODEL'] = original_model

def analyze_entity_quality(results1: Dict, results2: Dict):
    """Analyze and compare entity extraction quality."""
    print(f"\n📊 ENTITY EXTRACTION COMPARISON")
    print("=" * 50)
    
    # Speed comparison
    if results1.get('time', 0) > 0 and results2.get('time', 0) > 0:
        speed_diff = abs(results1['time'] - results2['time'])
        faster = results1['provider'] if results1['time'] < results2['time'] else results2['provider']
        print(f"🏃 Speed Winner: {faster}")
        print(f"   {results1['provider']}: {results1['time']:.2f}s")
        print(f"   {results2['provider']}: {results2['time']:.2f}s")
        print(f"   Difference: {speed_diff:.2f}s")
    
    # Count comparison
    print(f"\n📈 Entity Count:")
    print(f"   {results1['provider']}: {results1['count']} entities")
    print(f"   {results2['provider']}: {results2['count']} entities")
    
    # Quality analysis
    if results1.get('entities') and results2.get('entities'):
        print(f"\n🔍 Entity Types Found:")
        
        # Get unique entity types from each
        types1 = set(e.get('type', 'Unknown') for e in results1['entities'])
        types2 = set(e.get('type', 'Unknown') for e in results2['entities'])
        
        print(f"   {results1['provider']}: {', '.join(sorted(types1))}")
        print(f"   {results2['provider']}: {', '.join(sorted(types2))}")
        
        # Common entities
        names1 = set(e.get('name', '').lower() for e in results1['entities'])
        names2 = set(e.get('name', '').lower() for e in results2['entities'])
        common = names1.intersection(names2)
        
        print(f"\n🤝 Common Entities Found: {len(common)}")
        if common:
            print(f"   {', '.join(list(common)[:5])}")
    
    # Error reporting
    if results1.get('error'):
        print(f"\n❌ {results1['provider']} Error: {results1['error']}")
    if results2.get('error'):
        print(f"\n❌ {results2['provider']} Error: {results2['error']}")

async def main():
    """Main comparison function."""
    print("🚀 ENTITY EXTRACTION COMPARISON TEST")
    print("=" * 50)
    print(f"📝 Sample Text: {SAMPLE_MEDICAL_TEXT[:100]}...")
    
    # Test both approaches
    openrouter_results = await test_openrouter_extraction()
    ollama_results = await test_ollama_extraction()
    
    # Analyze results
    analyze_entity_quality(openrouter_results, ollama_results)
    
    # Recommendation
    print(f"\n💡 RECOMMENDATION:")
    if openrouter_results.get('error') and not ollama_results.get('error'):
        print("   Use Ollama + qwen3-4b (OpenRouter failed)")
    elif ollama_results.get('error') and not openrouter_results.get('error'):
        print("   Use OpenRouter + Llama Maverick (Ollama failed)")
    elif not openrouter_results.get('error') and not ollama_results.get('error'):
        if openrouter_results['time'] < ollama_results['time']:
            print("   OpenRouter is faster - good for real-time processing")
        else:
            print("   Ollama is faster - good for batch processing")
        
        if openrouter_results['count'] > ollama_results['count']:
            print("   OpenRouter found more entities - potentially better recall")
        elif ollama_results['count'] > openrouter_results['count']:
            print("   Ollama found more entities - potentially better recall")
        else:
            print("   Both found similar number of entities")
    else:
        print("   Both approaches had errors - check configuration")

if __name__ == "__main__":
    asyncio.run(main())
