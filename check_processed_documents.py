#!/usr/bin/env python3
"""
Check which documents have already been processed in FalkorDB.
"""

import falkordb
from pathlib import Path

def main():
    try:
        # Connect to FalkorDB
        client = falkordb.FalkorDB(host='localhost', port=6379)
        graph = client.select_graph('graphiti_knowledge')

        # Get all processed documents
        result = graph.query('MATCH (d:Document) RETURN d.file_path AS file_path, d.name AS name ORDER BY d.created_at')
        
        print(f"📊 Found {len(result.result_set)} processed documents:")
        print("=" * 80)
        
        processed_files = set()
        for i, record in enumerate(result.result_set, 1):
            file_path = record[0]
            doc_name = record[1]
            print(f"{i:2d}. {Path(file_path).name}")
            processed_files.add(Path(file_path).name)
        
        # Check uploads directory
        uploads_dir = Path("uploads")
        all_files = []
        for ext in ['.pdf', '.txt', '.docx', '.doc', '.pptx', '.ppt']:
            all_files.extend(uploads_dir.glob(f'*{ext}'))
        
        print(f"\n📁 Total files in uploads directory: {len(all_files)}")
        print(f"✅ Already processed: {len(processed_files)}")
        print(f"⏳ Remaining to process: {len(all_files) - len(processed_files)}")
        
        # Show unprocessed files
        unprocessed = []
        for file_path in all_files:
            if file_path.name not in processed_files:
                unprocessed.append(file_path)
        
        if unprocessed:
            print(f"\n📋 Next {min(10, len(unprocessed))} files to process:")
            print("-" * 50)
            for i, file_path in enumerate(unprocessed[:10], 1):
                print(f"{i:2d}. {file_path.name}")
            
            if len(unprocessed) > 10:
                print(f"    ... and {len(unprocessed) - 10} more files")
        else:
            print("\n✅ All files have been processed!")

    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
