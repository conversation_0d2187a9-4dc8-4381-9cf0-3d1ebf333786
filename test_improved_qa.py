#!/usr/bin/env python3
"""
Test the improved Q&A system.
"""

import requests
import json

def test_improved_qa():
    """Test the improved Q&A system."""
    print("🔄 Testing Improved Q&A System...")
    
    # Test the problematic question
    response = requests.post(
        'http://localhost:9753/api/qa/answer',
        json={
            'question': 'can you tell me about antioxidants and cancer',
            'max_facts': 8,
            'response_length': 'detailed',
            'temperature': 0.5
        },
        timeout=120
    )
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        answer = data.get('answer', 'No answer')
        references = data.get('references', [])
        
        print(f"Answer length: {len(answer)} characters")
        print(f"References: {len(references)} sources")
        
        # Check for repetitive content
        if answer.count('NADPH Quinone Reductase') > 3:
            print("⚠️ Still contains repetitive NADPH content")
        else:
            print("✅ No excessive repetition detected")
        
        # Check if it addresses the question properly
        if 'antioxidant' in answer.lower() and 'cancer' in answer.lower():
            print("✅ Answer addresses both antioxidants and cancer")
        else:
            print("⚠️ Answer may not fully address the question")
        
        # Show a preview
        print(f"\nAnswer preview:")
        print(f"{answer[:500]}...")
        
        if references:
            print(f"\nFirst few references:")
            for i, ref in enumerate(references[:3]):
                title = ref.get('title', 'Unknown')
                print(f"  [{i+1}] {title}")
        
        return True
    else:
        print(f"❌ Error: {response.text}")
        return False

if __name__ == "__main__":
    test_improved_qa()
