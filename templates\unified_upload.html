{% extends "base_modern.html" %}

{% block title %}Document Upload - Graphiti{% endblock %}

{% block extra_css %}
<style>
    .unified-dropzone {
        border: 3px dashed #0d6efd;
        border-radius: 15px;
        padding: 50px;
        text-align: center;
        background: linear-gradient(135deg, rgba(13, 110, 253, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .unified-dropzone::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }
    
    .unified-dropzone:hover::before {
        transform: translateX(100%);
    }
    
    .unified-dropzone:hover {
        border-color: #0b5ed7;
        background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
    }
    
    .unified-dropzone.drag-over {
        border-color: #198754;
        background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%);
    }
    
    .upload-icon {
        font-size: 3rem;
        color: #0d6efd;
        margin-bottom: 1rem;
    }
    
    .file-item {
        transition: all 0.2s ease;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 8px;
        background: white;
    }
    
    .file-item:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }
    
    .progress-container {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .settings-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 24px;
        border: 1px solid #dee2e6;
    }
    
    .processing-stats {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .file-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
    
    .onenote-badge {
        background-color: #7b2cbf;
        color: white;
    }
    
    .pdf-badge {
        background-color: #dc3545;
        color: white;
    }
    
    .doc-badge {
        background-color: #0d6efd;
        color: white;
    }
    
    .text-badge {
        background-color: #198754;
        color: white;
    }
    
    .other-badge {
        background-color: #6c757d;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-cloud-upload text-primary"></i> Unified Document Upload</h2>
                    <p class="text-muted">Upload single files, multiple files, or entire folders. Supports PDF, Word, OneNote (.one), and more.</p>
                </div>
                <div class="processing-stats">
                    <div class="row text-center">
                        <div class="col">
                            <h5 class="mb-0 text-primary" id="active-operations">0</h5>
                            <small class="text-muted">Active</small>
                        </div>
                        <div class="col">
                            <h5 class="mb-0 text-success" id="completed-operations">0</h5>
                            <small class="text-muted">Completed</small>
                        </div>
                        <div class="col">
                            <h5 class="mb-0 text-info" id="total-files">0</h5>
                            <small class="text-muted">Total Files</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Alert Container -->
            <div id="alert-container"></div>
            
            <!-- Upload Area -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="unified-dropzone" id="dropzone">
                        <div class="upload-icon">
                            <i class="bi bi-cloud-upload"></i>
                        </div>
                        <h4 class="mb-3">Drag and drop files or folders here</h4>
                        <div class="d-flex gap-2 justify-content-center mb-3">
                            <button type="button" class="btn btn-primary btn-lg" id="select-files-button">
                                <i class="bi bi-file-earmark"></i> Select Files
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-lg" id="select-folder-button">
                                <i class="bi bi-folder"></i> Select Folder
                            </button>
                        </div>
                        <p class="text-muted">
                            <strong>Supported formats:</strong> 
                            <span class="badge onenote-badge">OneNote (.one)</span>
                            <span class="badge pdf-badge">PDF</span>
                            <span class="badge doc-badge">Word (DOC/DOCX)</span>
                            <span class="badge text-badge">Text (TXT/MD)</span>
                            <span class="badge other-badge">Excel, PowerPoint, HTML, and more</span>
                        </p>
                        <input type="file" id="file-input" class="d-none" multiple 
                               accept=".pdf,.txt,.md,.rtf,.doc,.docx,.odt,.html,.htm,.xml,.csv,.xls,.xlsx,.ppt,.pptx,.epub,.one">
                        <input type="file" id="folder-input" class="d-none" webkitdirectory directory multiple>
                    </div>
                </div>
            </div>
            
            <!-- File List -->
            <div id="file-list-container" class="card mb-4" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-files"></i> Selected Files</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-danger" id="clear-files-button">
                            <i class="bi bi-trash"></i> Clear All
                        </button>
                        <button type="button" class="btn btn-sm btn-success" id="upload-button" disabled>
                            <i class="bi bi-upload"></i> Upload All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="file-list"></div>
                </div>
            </div>
            
            <!-- Old Progress Container - REMOVED -->
            <!-- Using Enhanced Progress UI instead -->
        </div>
        
        <!-- Settings Sidebar -->
        <div class="col-lg-4">
            <div class="settings-panel">
                <h5 class="mb-3"><i class="bi bi-gear"></i> Processing Settings</h5>
                
                <!-- Chunk Settings -->
                <div class="mb-3">
                    <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                    <input type="number" class="form-control" id="chunk-size" value="1200" min="100" max="10000">
                    <div class="form-text">Size of text chunks for processing (100-10000)</div>
                </div>
                
                <div class="mb-3">
                    <label for="overlap" class="form-label">Chunk Overlap (characters)</label>
                    <input type="number" class="form-control" id="overlap" value="0" min="0" max="500">
                    <div class="form-text">Overlap between consecutive chunks</div>
                </div>
                
                <!-- Processing Options -->
                <div class="mb-3">
                    <label class="form-label">Processing Options</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extract-entities" checked>
                        <label class="form-check-label" for="extract-entities">
                            Extract Entities
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extract-references" checked>
                        <label class="form-check-label" for="extract-references">
                            Extract References
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extract-metadata" checked>
                        <label class="form-check-label" for="extract-metadata">
                            Extract Metadata
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="generate-embeddings" checked>
                        <label class="form-check-label" for="generate-embeddings">
                            Generate Embeddings
                        </label>
                    </div>
                </div>
                
                <!-- Parallel Processing -->
                <div class="mb-3">
                    <label for="max-parallel" class="form-label">Max Parallel Processes</label>
                    <input type="number" class="form-control" id="max-parallel" value="4" min="1" max="8">
                    <div class="form-text">Number of files to process simultaneously</div>
                </div>
                
                <!-- Duplicate Detection -->
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="duplicate-detection" checked>
                        <label class="form-check-label" for="duplicate-detection">
                            Enable Duplicate Detection
                        </label>
                    </div>
                    <div class="form-text">Check for similar documents before processing</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Detection Modal -->
<div class="modal fade" id="duplicateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Duplicate Document Detected</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="duplicate-modal-body">
                <!-- Duplicate detection content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="skip-duplicate-btn">Skip This File</button>
                <button type="button" class="btn btn-primary" id="process-anyway-btn">Process Anyway</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/unified_upload.js?v=20250123"></script>
<script src="/static/js/duplicate_detection.js"></script>
<script src="/static/js/websocket_upload.js"></script>
<script src="/static/js/enhanced_progress_ui.js?v=20250123"></script>

<script>
// Simple test and direct integration
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Enhanced Progress UI: DOM loaded');

    // Force global assignment if not already done
    if (!window.enhancedProgressUI && typeof enhancedProgressUI !== 'undefined') {
        window.enhancedProgressUI = enhancedProgressUI;
        console.log('🔧 Manually assigned enhancedProgressUI to window');
    }

    // If still not available, create it directly
    if (!window.enhancedProgressUI) {
        console.log('🔧 Creating enhanced progress UI directly in template');
        try {
            window.enhancedProgressUI = new EnhancedProgressUI();
            console.log('✅ Enhanced Progress UI created successfully');
        } catch (error) {
            console.error('❌ Failed to create Enhanced Progress UI:', error);
        }
    }

    // Test if enhanced progress UI is available
    setTimeout(() => {
        console.log('📊 Testing Enhanced Progress UI availability...');
        console.log('📊 window.enhancedProgressUI:', window.enhancedProgressUI);
        console.log('📊 typeof enhancedProgressUI:', typeof enhancedProgressUI);

        if (window.enhancedProgressUI) {
            console.log('✅ Enhanced Progress UI is available!');

            // Test creating a progress card
            console.log('🧪 Testing progress card creation...');
            try {
                // Create a test progress card
                window.enhancedProgressUI.startTracking('test-123', 'Test Document.pdf');
                console.log('✅ Test progress card created successfully!');

                // Remove test card after 3 seconds
                setTimeout(() => {
                    window.enhancedProgressUI.hideOperation('test-123');
                    console.log('🧹 Test progress card removed');
                }, 3000);
            } catch (error) {
                console.error('❌ Error creating test progress card:', error);
            }
        } else {
            console.error('❌ Enhanced Progress UI not available');
        }

        // Check for upload manager
        console.log('📊 window.unifiedUploadManager:', window.unifiedUploadManager);

        // Simple direct integration - listen for upload events
        if (window.unifiedUploadManager) {
            console.log('✅ Upload manager found, setting up direct integration');

            // Override the method directly
            const originalMethod = window.unifiedUploadManager.handleSingleUploadResponse;
            window.unifiedUploadManager.handleSingleUploadResponse = function(result) {
                console.log('🚀 DIRECT INTEGRATION: Upload response received');
                console.log('📊 Result:', result);

                // Start enhanced progress tracking immediately
                if (window.enhancedProgressUI && result.operation_id && result.filename) {
                    console.log('🎯 Starting enhanced progress tracking NOW');
                    window.enhancedProgressUI.startTracking(result.operation_id, result.filename);

                    // Skip the old progress tracking since we have enhanced tracking
                    console.log('🚫 Skipping old progress tracking - using enhanced version');

                    // Only update counters and active operations, skip creating old progress items
                    if (result.operation_id) {
                        this.activeOperations.add(result.operation_id);
                        this.updateCounters();
                    }
                    return; // Don't call original method
                }

                // Fallback: Call original method if enhanced tracking fails
                if (originalMethod) {
                    console.log('⚠️ Enhanced tracking not available, using original method');
                    originalMethod.call(this, result);
                }
            };

            console.log('✅ Direct integration setup complete');
        } else {
            console.error('❌ Upload manager not found');
        }
    }, 500); // Longer delay to ensure everything is loaded
});
</script>
{% endblock %}
