#!/usr/bin/env python3
"""
Simple test of entity extraction integration
"""

import asyncio

async def test_simple_entity_integration():
    """Test the entity extraction integration simply."""
    
    print("🧪 SIMPLE ENTITY INTEGRATION TEST")
    print("=" * 40)
    
    try:
        # Test 1: Import the existing entity extraction module
        print("1️⃣ Testing existing entity extraction module...")
        from entity_extraction.main import extract_entities_from_text
        print(f"   ✅ Entity extraction module imported successfully")
        
        # Test 2: Test with simple text
        print("\n2️⃣ Testing entity extraction...")
        test_text = """
        This document discusses Lactate and Pyruvate levels in patients.
        L-Carnitine and Coenzyme Q10 are important nutrients for mitochondrial function.
        Vitamin B6 deficiency can affect Tryptophan metabolism and Serotonin production.
        """
        
        entities = await extract_entities_from_text(
            api_key=None,  # Will use environment variable
            text=test_text
        )
        
        print(f"   ✅ Entity extraction successful")
        print(f"   🏷️ Extracted {len(entities)} entities")
        
        if entities:
            for i, entity in enumerate(entities[:3], 1):
                name = entity.get('name', 'Unknown')
                entity_type = entity.get('type', 'Unknown')
                print(f"      {i}. {name} ({entity_type})")
        
        # Test 3: Test unified pipeline import
        print("\n3️⃣ Testing unified pipeline import...")
        from unified_ingestion_pipeline import get_unified_pipeline
        pipeline = await get_unified_pipeline()
        print(f"   ✅ Unified pipeline imported successfully")
        
        # Test 4: Test initialization
        print("\n4️⃣ Testing initialization...")
        await pipeline._initialize_components()
        print(f"   ✅ Components initialized")
        
        # Check entity extractor
        if hasattr(pipeline, 'entity_extractor') and pipeline.entity_extractor:
            print(f"   ✅ Entity extractor available")
        else:
            print(f"   ❌ Entity extractor not available")
            return False
        
        print(f"\n🎉 SIMPLE ENTITY INTEGRATION TEST COMPLETE!")
        print(f"✅ Entity extraction is properly integrated!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_simple_entity_integration())
