#!/usr/bin/env python3
"""
Fix OneNote Token File

This script fixes the token file format for LangChain OneNote loader.
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.onenote_auth_manager import OneNoteAuthManager

def fix_token_file():
    """Fix the token file format."""
    print("🔧 Fixing OneNote Token File")
    print("=" * 35)
    
    # Initialize auth manager
    auth_manager = OneNoteAuthManager()
    
    # Check current token file
    print(f"📁 Token file location: {auth_manager.token_file}")
    
    if not auth_manager.token_file.exists():
        print("❌ Token file not found")
        return False
    
    # Read current token file
    try:
        with open(auth_manager.token_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        print(f"📊 Current file size: {len(content)} characters")
        print(f"📋 Content preview: {content[:100]}...")
        
        # Check if it's JSON or raw token
        try:
            token_data = json.loads(content)
            print("✅ File contains JSON data")
            
            # Extract access token
            access_token = token_data.get('access_token')
            if access_token:
                print(f"✅ Found access token: {len(access_token)} characters")
                
                # Create LangChain-compatible token file
                langchain_token_file = auth_manager.credentials_dir / "langchain_onenote_token.txt"
                
                with open(langchain_token_file, 'w', encoding='utf-8') as f:
                    f.write(access_token)
                
                print(f"✅ Created LangChain token file: {langchain_token_file}")
                print(f"📊 Token file size: {len(access_token)} characters")
                
                # Also update the main token file to store just the access token
                # But keep the JSON version as backup
                backup_file = auth_manager.credentials_dir / "onenote_graph_token_backup.json"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(token_data, f, indent=2)
                
                print(f"💾 Backup created: {backup_file}")
                
                # Update main token file with just access token
                with open(auth_manager.token_file, 'w', encoding='utf-8') as f:
                    f.write(access_token)
                
                print(f"✅ Updated main token file with access token only")
                
                return True
            else:
                print("❌ No access token found in JSON")
                return False
                
        except json.JSONDecodeError:
            print("✅ File already contains raw token")
            # File already contains raw token, just verify it's valid
            if len(content) > 100 and content.startswith('EwB'):  # Microsoft tokens typically start with EwB
                print("✅ Token format looks correct")
                
                # Create LangChain-compatible token file
                langchain_token_file = auth_manager.credentials_dir / "langchain_onenote_token.txt"
                
                with open(langchain_token_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ Created LangChain token file: {langchain_token_file}")
                return True
            else:
                print("❌ Token format doesn't look correct")
                return False
                
    except Exception as e:
        print(f"❌ Error reading token file: {e}")
        return False

def test_token_file():
    """Test if the token file works with OneNote loader."""
    print(f"\n🧪 Testing Token File")
    print("-" * 25)
    
    try:
        from langchain_community.document_loaders.onenote import OneNoteLoader
        
        print("✅ OneNote loader available")
        
        # Try to create a loader with token
        try:
            loader = OneNoteLoader(
                notebook_name="Biochemistry",
                section_name="Brain",
                auth_with_token=True
            )
            print("✅ OneNote loader created successfully with token")
            return True
            
        except Exception as e:
            print(f"❌ OneNote loader failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ OneNote loader not available: {e}")
        return False

def main():
    """Main function."""
    print("🌟" * 50)
    print("🔧 OneNote Token File Fix")
    print("🌟" * 50)
    
    # Fix token file
    fix_success = fix_token_file()
    
    if fix_success:
        print("\n✅ Token file fixed successfully!")
        
        # Test the token file
        test_success = test_token_file()
        
        if test_success:
            print("\n🎉 SUCCESS! Token file is working correctly!")
            print("\n📋 You can now run the OneNote processing again:")
            print("   python process_brain_onenote_full.py")
        else:
            print("\n⚠️ Token file fixed but testing failed")
            print("📋 You may need to re-authenticate")
    else:
        print("\n❌ Failed to fix token file")
        print("📋 You may need to re-authenticate:")
        print("   python setup_onenote_token.py")
    
    print("\n🌟" * 50)
    print("🎉 Fix Complete!")
    print("🌟" * 50)

if __name__ == "__main__":
    main()
