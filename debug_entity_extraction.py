#!/usr/bin/env python3
"""
Debug entity extraction to see what's happening.
"""

import asyncio
from database.falkordb_adapter import get_falkordb_adapter

async def debug_entity_extraction():
    """Debug the entity extraction process."""
    
    print("🔍 Debugging entity extraction...")
    
    try:
        adapter = await get_falkordb_adapter()
        
        # 1. Check latest document
        print("\n1. Checking latest document...")
        query = '''
        MATCH (ep:Episode)
        WHERE ep.name CONTAINS 'Nutritional Diagnosis'
        RETURN ep.uuid as uuid, ep.name as name, ep.processed_at as processed_at
        ORDER BY ep.processed_at DESC
        LIMIT 1
        '''
        result = adapter.execute_cypher(query)
        
        if not result or len(result) <= 1 or not result[1]:
            print("❌ No Nutritional Diagnosis documents found")
            return
            
        episode_id = result[1][0][0]
        episode_name = result[1][0][1]
        processed_at = result[1][0][2]
        
        print(f"✅ Found episode: {episode_id}")
        print(f"   Name: {episode_name}")
        print(f"   Processed: {processed_at}")
        
        # 2. Check facts for this episode
        print("\n2. Checking facts...")
        fact_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid as uuid, f.chunk_index as chunk_index, f.body as body
        ORDER BY f.chunk_index
        '''
        fact_result = adapter.execute_cypher(fact_query)
        
        if fact_result and len(fact_result) > 1 and fact_result[1]:
            print(f"✅ Found {len(fact_result[1])} facts:")
            for i, fact_row in enumerate(fact_result[1]):
                body_length = len(fact_row[2]) if len(fact_row) > 2 and fact_row[2] else 0
                print(f"   Fact {i+1}: {fact_row[0]} (chunk {fact_row[1]}, {body_length} chars)")
        else:
            print("❌ No facts found for this episode")
            return
        
        # 3. Check entities linked to facts
        print("\n3. Checking entities linked to facts...")
        entity_query = f'''
        MATCH (ep:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(e:Entity)
        RETURN f.uuid as fact_uuid, e.name as entity_name, e.type as entity_type
        ORDER BY f.chunk_index, e.name
        '''
        entity_result = adapter.execute_cypher(entity_query)
        
        if entity_result and len(entity_result) > 1 and entity_result[1]:
            print(f"✅ Found {len(entity_result[1])} entity-fact relationships:")
            for entity_row in entity_result[1]:
                print(f"   Fact {entity_row[0][:8]}... -> {entity_row[1]} ({entity_row[2]})")
        else:
            print("❌ No entities linked to facts")
        
        # 4. Check if entities exist at all (not linked to facts)
        print("\n4. Checking standalone entities...")
        standalone_query = '''
        MATCH (e:Entity)
        WHERE e.created_at > timestamp() - 3600000
        RETURN e.name as name, e.type as type, e.extraction_method as method
        ORDER BY e.created_at DESC
        LIMIT 10
        '''
        standalone_result = adapter.execute_cypher(standalone_query)
        
        if standalone_result and len(standalone_result) > 1 and standalone_result[1]:
            print(f"✅ Found {len(standalone_result[1])} recent standalone entities:")
            for entity_row in standalone_result[1]:
                method = entity_row[2] if len(entity_row) > 2 else 'unknown'
                print(f"   {entity_row[0]} ({entity_row[1]}) - method: {method}")
        else:
            print("❌ No recent standalone entities found")
        
        # 5. Summary
        print("\n📊 Summary:")
        print(f"   Episode: {episode_id}")
        print(f"   Facts: {len(fact_result[1]) if fact_result and len(fact_result) > 1 else 0}")
        print(f"   Entity-Fact links: {len(entity_result[1]) if entity_result and len(entity_result) > 1 else 0}")
        print(f"   Standalone entities: {len(standalone_result[1]) if standalone_result and len(standalone_result) > 1 else 0}")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_entity_extraction())
