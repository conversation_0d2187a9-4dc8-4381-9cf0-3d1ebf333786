#!/usr/bin/env python3
"""
Test multiple questions with the improved Q&A system.
"""

import requests
import json

def test_question(question, description):
    """Test a single question."""
    print(f"\n🔍 Testing: {description}")
    print(f"   Question: '{question}'")
    
    try:
        response = requests.post(
            'http://localhost:9753/api/qa/answer',
            json={
                'question': question,
                'max_facts': 6,
                'response_length': 'brief',
                'temperature': 0.5
            },
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('answer', 'No answer')
            references = data.get('references', [])
            
            # Check for quality indicators
            repetition_check = answer.count('NADPH Quinone Reductase') <= 2
            length_check = len(answer) > 500
            reference_check = len(references) > 0
            
            print(f"   ✅ Length: {len(answer)} chars, References: {len(references)}")
            print(f"   ✅ No excessive repetition: {repetition_check}")
            print(f"   Preview: {answer[:150]}...")
            
            return repetition_check and length_check and reference_check
        else:
            print(f"   ❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    """Test multiple questions."""
    print("🚀 TESTING IMPROVED Q&A SYSTEM")
    print("=" * 50)
    
    test_questions = [
        ("cocoa and human health", "Cocoa Health Benefits"),
        ("ginger health benefits", "Ginger Benefits"),
        ("vitamin C immune system", "Vitamin C & Immunity"),
        ("what are polyphenols", "Polyphenols Information")
    ]
    
    results = []
    for question, description in test_questions:
        result = test_question(question, description)
        results.append((description, result))
    
    # Summary
    print(f"\n📊 IMPROVED Q&A RESULTS:")
    print("=" * 30)
    
    working = 0
    for description, result in results:
        status = "✅" if result else "❌"
        print(f"   {status} {description}")
        if result:
            working += 1
    
    print(f"\n🎯 SUCCESS RATE: {working}/{len(results)} ({working/len(results)*100:.1f}%)")
    
    if working == len(results):
        print("\n🎉 ALL Q&A IMPROVEMENTS WORKING!")
        print("   - No repetitive content")
        print("   - Comprehensive answers")
        print("   - Proper citations")
        print("   - Relevant topic coverage")
    else:
        print(f"\n⚠️ {len(results) - working} questions still need improvement")

if __name__ == "__main__":
    main()
