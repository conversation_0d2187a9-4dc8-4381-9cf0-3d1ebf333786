#!/usr/bin/env python3
"""
Check episodes that need entity extraction.
"""

import asyncio
from database.falkordb_adapter import GraphitiFalkorDBAdapter

async def check_episode_for_facts():
    adapter = GraphitiFalkorDBAdapter('graphiti')
    
    # Get the episode that contains these facts
    query = '''
    MATCH (ep:Episode)-[:CONTAINS]->(f:Fact)
    WHERE NOT (f)-[:MENTIONS]->(:Entity)
    RETURN ep.uuid as episode_uuid, ep.name as episode_name, count(f) as fact_count
    '''
    result = adapter.execute_cypher(query)
    print('Episodes with facts needing entity extraction:')
    if result and len(result) > 1:
        headers = result[0]
        for row in result[1]:
            data = dict(zip(headers, row))
            print(f'  Episode UUID: {data["episode_uuid"]}')
            print(f'  Episode Name: {data["episode_name"]}')
            print(f'  Facts without entities: {data["fact_count"]}')
            print('  ---')

if __name__ == "__main__":
    asyncio.run(check_episode_for_facts())
