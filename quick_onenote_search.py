#!/usr/bin/env python3
"""
Quick OneNote File Search

Fast search for OneNote (.one) files in common locations.
"""

import os
from pathlib import Path
import json
from datetime import datetime
from typing import List, Dict, Any

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def search_directory(directory: str, max_depth: int = 3) -> List[Dict[str, Any]]:
    """Search for .one files in a directory with limited depth."""
    onenote_files = []
    
    if not os.path.exists(directory):
        return onenote_files
    
    print(f"🔍 Searching: {directory}")
    
    try:
        path_obj = Path(directory)
        
        # Use iterdir with depth control instead of rglob for speed
        def search_recursive(current_path: Path, current_depth: int):
            if current_depth > max_depth:
                return
            
            try:
                for item in current_path.iterdir():
                    if item.is_file() and item.suffix.lower() == '.one':
                        try:
                            stat = item.stat()
                            file_info = {
                                'path': str(item),
                                'name': item.name,
                                'size': stat.st_size,
                                'size_formatted': format_file_size(stat.st_size),
                                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                                'parent_folder': str(item.parent),
                                'accessible': True
                            }
                            onenote_files.append(file_info)
                            print(f"   ✓ Found: {item.name} ({file_info['size_formatted']})")
                        except Exception as e:
                            print(f"   ❌ Error processing {item}: {e}")
                    
                    elif item.is_dir() and not item.name.startswith('.'):
                        # Skip hidden directories and continue recursion
                        search_recursive(item, current_depth + 1)
                        
            except PermissionError:
                print(f"   ⚠️  Permission denied: {current_path}")
            except Exception as e:
                print(f"   ❌ Error in {current_path}: {e}")
        
        search_recursive(path_obj, 0)
        
    except Exception as e:
        print(f"   ❌ Error searching {directory}: {e}")
    
    return onenote_files

def main():
    """Main function to quickly find OneNote files."""
    
    print("🚀 Quick OneNote File Search")
    print("=" * 40)
    
    # Common OneNote locations
    search_locations = [
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/OneDrive"),
        os.path.expanduser("~/OneDrive - Personal"),
        os.path.expanduser("~/OneDrive for Business"),
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Downloads"),
        "C:\\Users\\<USER>\\Documents",
    ]
    
    # Add user-specific paths
    username = os.getenv('USERNAME', 'User')
    additional_paths = [
        f"C:\\Users\\<USER>\\Documents",
        f"C:\\Users\\<USER>\\OneDrive",
        f"C:\\Users\\<USER>\\Desktop",
    ]
    
    search_locations.extend(additional_paths)
    
    # Remove duplicates and non-existent paths
    search_locations = list(set([path for path in search_locations if os.path.exists(path)]))
    
    print(f"Searching in {len(search_locations)} locations...")
    
    all_onenote_files = []
    
    for location in search_locations:
        files = search_directory(location, max_depth=4)  # Limit depth for speed
        all_onenote_files.extend(files)
    
    # Remove duplicates based on path
    seen_paths = set()
    unique_files = []
    for file_info in all_onenote_files:
        if file_info['path'] not in seen_paths:
            seen_paths.add(file_info['path'])
            unique_files.append(file_info)
    
    print(f"\n✅ Found {len(unique_files)} unique OneNote files!")
    
    if unique_files:
        # Calculate total size
        total_size = sum(f.get('size', 0) for f in unique_files)
        
        print(f"📊 Summary:")
        print(f"   Total files: {len(unique_files)}")
        print(f"   Total size: {format_file_size(total_size)}")
        
        # Group by parent folder
        by_folder = {}
        for file_info in unique_files:
            folder = file_info.get('parent_folder', 'Unknown')
            if folder not in by_folder:
                by_folder[folder] = []
            by_folder[folder].append(file_info)
        
        print(f"\n📁 Files by folder:")
        for folder, files in sorted(by_folder.items()):
            print(f"   {folder}: {len(files)} files")
            for file_info in files:
                print(f"      • {file_info['name']} ({file_info['size_formatted']})")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as JSON
        json_file = f"onenote_files_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(unique_files, f, indent=2, ensure_ascii=False)
        print(f"\n📄 Saved detailed report: {json_file}")
        
        # Save processing list
        processing_file = f"onenote_processing_list_{timestamp}.txt"
        with open(processing_file, 'w', encoding='utf-8') as f:
            f.write("# OneNote Files for Processing\n")
            f.write("# Edit this list to select which files you want to process\n")
            f.write("# Remove the # from lines you want to process\n\n")
            
            for file_info in sorted(unique_files, key=lambda x: x.get('name', '').lower()):
                f.write(f"# {file_info['path']}\n")
        
        print(f"📋 Saved processing list: {processing_file}")
        
        print(f"\n📋 Next Steps:")
        print("1. Review the files found above")
        print("2. Edit the processing list file to select which files to process")
        print("3. Remove the # from the lines you want to process")
        print("4. Use the selected file paths for batch processing in Graphiti")
        
    else:
        print("\n❌ No OneNote files found in common locations.")
        print("\nTry running the full search script if files might be in other locations:")
        print("python discover_onenote_files.py")
    
    print(f"\n✨ Quick search complete!")

if __name__ == "__main__":
    main()
