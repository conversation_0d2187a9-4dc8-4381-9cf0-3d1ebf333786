#!/usr/bin/env python3
"""
Reprocess the kidney document with the new Intelligent Reference Extractor.
"""

import asyncio
import sys
from pathlib import Path
from unified_ingestion_pipeline import get_unified_pipeline
from services.intelligent_reference_extractor import get_intelligent_reference_extractor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def reprocess_kidney_document():
    """Reprocess the kidney document with intelligent reference extraction."""
    print("🔄 Reprocessing Kidney Document with Intelligent Reference Extractor")
    print("=" * 70)
    
    # Look for the kidney document
    document_name = "5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal Oseki.pdf"
    
    # Common locations to check
    possible_paths = [
        document_name,
        f"uploads/{document_name}",
        f"documents/{document_name}",
        f"processed_documents/{document_name}",
        f"../uploads/{document_name}",
        f"temp_uploads/{document_name}"
    ]
    
    document_path = None
    for path in possible_paths:
        if Path(path).exists():
            document_path = Path(path)
            print(f"📄 Found document: {document_path}")
            break
    
    if not document_path:
        print(f"❌ Document not found in expected locations:")
        for path in possible_paths:
            print(f"   - {path}")
        print(f"\n🔍 Searching current directory...")
        
        # Search current directory for any PDF with "Kidney" in the name
        current_dir = Path(".")
        kidney_files = list(current_dir.glob("*Kidney*.pdf"))
        
        if kidney_files:
            print(f"📄 Found kidney documents:")
            for i, file in enumerate(kidney_files, 1):
                print(f"   {i}. {file}")
            
            # Use the first one
            document_path = kidney_files[0]
            print(f"✅ Using: {document_path}")
        else:
            print(f"❌ No kidney documents found in current directory")
            return False
    
    # Get file info
    file_size = document_path.stat().st_size
    print(f"📊 File size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    try:
        # Get the unified pipeline
        print(f"\n🔧 Initializing unified pipeline...")
        pipeline = await get_unified_pipeline()
        
        # Process with focus on references
        print(f"🚀 Starting intelligent reference extraction...")
        print(f"   📄 Document: {document_path.name}")
        print(f"   🧠 Using: Intelligent Reference Extractor")
        print(f"   🎯 Expected: 40+ references (vs previous 6)")
        
        result = await pipeline.process_document(
            file_path=document_path,
            chunk_size=1200,
            overlap=0,
            extract_entities=True,  # Keep entity extraction
            extract_references=True,  # Focus on references
            extract_metadata=True,
            generate_embeddings=True
        )
        
        if result.get('success', False):
            print(f"\n✅ Processing completed successfully!")
            
            # Extract results
            references_found = result.get('references', 0)
            entities_found = result.get('entities', 0)
            chunks_created = result.get('chunks', 0)
            embeddings_generated = result.get('embeddings', 0)
            text_length = result.get('text_length', 0)
            
            print(f"\n📊 Results Summary:")
            print(f"   📄 Text extracted: {text_length:,} characters")
            print(f"   🧩 Chunks created: {chunks_created}")
            print(f"   🏷️ Entities extracted: {entities_found}")
            print(f"   📚 References found: {references_found}")
            print(f"   🔗 Embeddings generated: {embeddings_generated}")
            
            # Performance assessment
            print(f"\n🎯 Reference Extraction Performance:")
            if references_found >= 40:
                print(f"   🎉 EXCELLENT: Found {references_found} references!")
                print(f"   📈 Improvement: {references_found/6:.1f}x better than before")
            elif references_found >= 30:
                print(f"   ✅ VERY GOOD: Found {references_found} references")
                print(f"   📈 Improvement: {references_found/6:.1f}x better than before")
            elif references_found >= 20:
                print(f"   ✅ GOOD: Found {references_found} references")
                print(f"   📈 Improvement: {references_found/6:.1f}x better than before")
            elif references_found >= 10:
                print(f"   ⚠️ FAIR: Found {references_found} references")
                print(f"   📈 Improvement: {references_found/6:.1f}x better than before")
            else:
                print(f"   ❌ POOR: Only found {references_found} references")
                print(f"   📉 This is concerning - expected 40+")
            
            # Check CSV file
            csv_path = result.get('reference_csv_path')
            if csv_path and Path(csv_path).exists():
                print(f"\n📄 References saved to CSV:")
                print(f"   📁 Path: {csv_path}")
                
                # Read and display first few references
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_path)
                    print(f"   📊 CSV contains {len(df)} references")
                    
                    if len(df) > 0:
                        print(f"\n📋 First 5 references from CSV:")
                        for i, row in df.head().iterrows():
                            ref_text = row.get('reference_text', 'N/A')
                            print(f"   {i+1}. {ref_text[:100]}{'...' if len(ref_text) > 100 else ''}")
                    
                except Exception as e:
                    print(f"   ⚠️ Could not read CSV: {e}")
            else:
                print(f"\n❌ No CSV file created")
            
            # Success message
            print(f"\n🎉 SUCCESS: Kidney document reprocessed with Intelligent Reference Extractor!")
            print(f"   The system should now find all the references you listed.")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"\n❌ Processing failed: {error}")
            return False
            
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_extraction():
    """Test the intelligent extractor directly on the document."""
    print(f"\n" + "=" * 70)
    print(f"🧪 Testing Direct Intelligent Reference Extraction")
    print(f"=" * 70)
    
    # Find the document
    document_name = "5d2b67d0-76d5-4e63-abb2-b230c9a8a210_23 Kidney - Micheal Oseki.pdf"
    document_path = None
    
    for path in [document_name, f"uploads/{document_name}", f"documents/{document_name}"]:
        if Path(path).exists():
            document_path = Path(path)
            break
    
    if not document_path:
        # Look for any kidney document
        kidney_files = list(Path(".").glob("*Kidney*.pdf"))
        if kidney_files:
            document_path = kidney_files[0]
    
    if not document_path:
        print(f"❌ Cannot find kidney document for direct testing")
        return
    
    try:
        # Read the PDF text (simplified - would need proper PDF extraction)
        print(f"📄 Document found: {document_path}")
        print(f"⚠️ Note: Direct text extraction from PDF not implemented in this test")
        print(f"   Use the unified pipeline reprocessing above for full functionality")
        
    except Exception as e:
        print(f"❌ Direct extraction test failed: {e}")

async def main():
    """Main function."""
    print(f"🚀 Kidney Document Reprocessing with Intelligent Reference Extractor")
    print(f"=" * 70)
    
    # Reprocess the document
    success = await reprocess_kidney_document()
    
    if success:
        print(f"\n🎯 MISSION ACCOMPLISHED!")
        print(f"The Intelligent Reference Extractor has been applied to your kidney document.")
        print(f"You should now see 40+ references instead of the previous 6.")
        print(f"Check the CSV file for the complete list of extracted references.")
    else:
        print(f"\n❌ MISSION FAILED!")
        print(f"Please check the error messages above and try again.")
        print(f"Make sure the document path is correct and accessible.")

if __name__ == "__main__":
    asyncio.run(main())
