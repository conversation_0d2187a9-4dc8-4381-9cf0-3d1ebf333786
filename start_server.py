#!/usr/bin/env python3
"""
Simple startup script for Graphiti server
"""

import os
import sys
import subprocess

def main():
    """Start the Graphiti server"""
    print("🚀 Starting Graphiti Knowledge Graph Server...")
    
    # Set the working directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Add current directory to Python path
    sys.path.insert(0, script_dir)
    
    try:
        # Import and run the app
        import app
        print("✅ App module imported successfully")
        
        # Run the main function
        if hasattr(app, 'main'):
            app.main()
        else:
            # Run uvicorn directly
            import uvicorn
            from utils.config import HOST, PORT
            
            print(f"🚀 Starting Graphiti Knowledge Graph Application")
            display_host = "localhost" if HOST == "0.0.0.0" else HOST
            print(f"📍 Server: http://{display_host}:{PORT}")
            print(f"📊 Dashboard: http://{display_host}:{PORT}/")
            print(f"📚 API Docs: http://{display_host}:{PORT}/docs")
            print(f"🔧 Settings: http://{display_host}:{PORT}/settings")
            print("=" * 50)
            
            uvicorn.run(
                "app:app",
                host=HOST,
                port=PORT,
                reload=False
            )
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
