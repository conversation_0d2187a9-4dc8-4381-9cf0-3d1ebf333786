#!/usr/bin/env python3
"""
Resume aggressive reference processing, skipping the stuck document
"""

import asyncio
import os
import csv
from pathlib import Path
from datetime import datetime

async def resume_processing():
    """Resume processing from where we left off, skipping the stuck document."""
    
    print("🔄 RESUMING AGGRESSIVE REFERENCE PROCESSING")
    print("=" * 60)
    
    # Kill the stuck process first
    print("1️⃣ Stopping stuck process...")
    import psutil
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] in ['python.exe', 'python']:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'complete_missing_references.py' in cmdline:
                    print(f"   🛑 Terminating stuck process: {proc.info['pid']}")
                    proc.terminate()
                    await asyncio.sleep(2)
                    if proc.is_running():
                        proc.kill()
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print("   ✅ Stuck process terminated")
    
    # Get all documents from database
    print("\n2️⃣ Getting document list...")
    
    try:
        from database.falkordb_adapter import get_falkordb_adapter
        
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Episode)
        RETURN e.uuid as document_id, 
               e.name as filename, 
               e.file_path as file_path
        ORDER BY e.processed_at DESC
        """
        
        result = adapter.execute_cypher(query)
        
        documents = []
        if result and len(result) > 1:
            for row in result[1]:
                doc_data = {
                    'document_id': row[0] if len(row) > 0 else '',
                    'filename': row[1] if len(row) > 1 else '',
                    'file_path': row[2] if len(row) > 2 else ''
                }
                documents.append(doc_data)
        
        print(f"   📄 Found {len(documents)} total documents")
        
        # Check which documents already have aggressive CSV files
        ref_dir = Path("references")
        existing_aggressive = set()
        
        if ref_dir.exists():
            for csv_file in ref_dir.glob("*_aggressive_references.csv"):
                doc_id = csv_file.name.split('_')[0]
                existing_aggressive.add(doc_id)
        
        print(f"   📁 Found {len(existing_aggressive)} existing aggressive CSV files")
        
        # Find documents that still need processing
        remaining_docs = []
        stuck_doc_id = "8da5e82b-7f9a-412e-9ebe-ed72bd947911"  # Ross Walker Q10
        
        for doc in documents:
            doc_id = doc['document_id']
            if doc_id not in existing_aggressive and doc_id != stuck_doc_id:
                remaining_docs.append(doc)
        
        print(f"   ❌ Documents still needing processing: {len(remaining_docs)}")
        print(f"   🚫 Skipping stuck document: Ross Walker Q10 (will retry later)")
        
        if not remaining_docs:
            print("\n🎉 All processable documents are complete!")
            print("   📊 Let's count the final results...")
            await count_final_results()
            return
        
        # Show what we'll process
        print(f"\n📋 REMAINING DOCUMENTS TO PROCESS:")
        for i, doc in enumerate(remaining_docs[:10], 1):
            filename = doc['filename'][:50] + "..." if len(doc['filename']) > 50 else doc['filename']
            print(f"   {i:2d}. {filename}")
        
        if len(remaining_docs) > 10:
            print(f"   ... and {len(remaining_docs) - 10} more documents")
        
        # Process remaining documents
        print(f"\n🚀 Resuming processing of {len(remaining_docs)} documents...")
        
        from services.aggressive_reference_extractor import get_aggressive_reference_extractor
        from utils.mistral_ocr import MistralOCRProcessor
        
        extractor = await get_aggressive_reference_extractor()
        ocr_processor = MistralOCRProcessor()
        
        successful_count = 0
        total_new_refs = 0
        
        for i, doc in enumerate(remaining_docs, 1):
            print(f"\n📖 Processing {i}/{len(remaining_docs)}: {doc['filename'][:40]}...")
            
            try:
                file_path = doc['file_path']
                if not file_path or not os.path.exists(file_path):
                    print(f"   ⚠️ File not found, skipping")
                    continue
                
                # Extract text with timeout
                print(f"   🔍 Extracting text...")
                try:
                    ocr_result = await asyncio.wait_for(
                        ocr_processor.process_pdf(file_path),
                        timeout=120  # 2 minute timeout
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ OCR timeout, skipping")
                    continue
                
                if not ocr_result.get('success', False):
                    print(f"   ❌ OCR failed, skipping")
                    continue
                
                extracted_text = ocr_result.get('text', '')
                print(f"   📝 Extracted {len(extracted_text):,} characters")
                
                if len(extracted_text) < 100:
                    print(f"   ⚠️ Text too short, skipping")
                    continue
                
                # Extract references with timeout and retry logic
                print(f"   🚀 Extracting references...")
                try:
                    ref_result = await asyncio.wait_for(
                        extractor.extract_references_from_text(extracted_text, doc['filename']),
                        timeout=300  # 5 minute timeout (increased)
                    )
                except asyncio.TimeoutError:
                    print(f"   ⏰ Reference extraction timeout, trying with shorter text...")
                    # Try with first 50,000 characters if timeout
                    try:
                        short_text = extracted_text[:50000]
                        ref_result = await asyncio.wait_for(
                            extractor.extract_references_from_text(short_text, doc['filename']),
                            timeout=120
                        )
                        print(f"   ✅ Processed with shortened text")
                    except asyncio.TimeoutError:
                        print(f"   ❌ Still timeout, skipping this document")
                        continue
                
                references_found = ref_result.get('total_found', 0)
                confidence = ref_result.get('confidence_score', 0.0)
                
                # Save to CSV
                csv_filename = f"{doc['document_id']}_{Path(file_path).stem}_aggressive_references.csv"
                csv_path = ref_dir / csv_filename
                
                await save_references_to_csv(ref_result, csv_path)
                
                print(f"   ✅ Found {references_found} references (confidence: {confidence:.2f})")
                print(f"   💾 Saved to: {csv_filename}")
                
                successful_count += 1
                total_new_refs += references_found
                
                # Brief pause every 3 documents
                if i % 3 == 0:
                    print(f"   ⏸️ Brief pause...")
                    await asyncio.sleep(3)
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        print(f"\n🎉 RESUMED PROCESSING COMPLETE!")
        print(f"   ✅ Successfully processed: {successful_count}/{len(remaining_docs)} documents")
        print(f"   📚 New references found: {total_new_refs}")
        
        # Count final results
        await count_final_results()
        
        # Handle the stuck document
        print(f"\n🔧 HANDLING STUCK DOCUMENT:")
        print(f"   📄 Ross Walker Q10 document was skipped due to timeout")
        print(f"   💡 Options:")
        print(f"      1. Try processing it manually with shorter timeout")
        print(f"      2. Skip it entirely (it's just 1 document)")
        print(f"      3. Try processing just the first part of the document")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def save_references_to_csv(ref_result, csv_path):
    """Save reference results to CSV file"""
    try:
        detailed_refs = ref_result.get('detailed_references', [])
        
        if not detailed_refs:
            simple_refs = ref_result.get('references', [])
            detailed_refs = [{'text': ref, 'extraction_method': 'aggressive'} for ref in simple_refs]
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'reference_number', 'text', 'authors', 'title', 'journal', 
                'year', 'doi', 'pmid', 'url', 'confidence', 
                'extraction_method', 'source_section'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for i, ref in enumerate(detailed_refs, 1):
                writer.writerow({
                    'reference_number': ref.get('number', i),
                    'text': ref.get('text', ''),
                    'authors': ref.get('authors', ''),
                    'title': ref.get('title', ''),
                    'journal': ref.get('journal', ''),
                    'year': ref.get('year', ''),
                    'doi': ref.get('doi', ''),
                    'pmid': ref.get('pmid', ''),
                    'url': ref.get('url', ''),
                    'confidence': ref.get('confidence', 0.0),
                    'extraction_method': ref.get('extraction_method', 'aggressive'),
                    'source_section': ref.get('source_section', 'document')
                })
        
    except Exception as e:
        print(f"   ⚠️ Error saving CSV: {e}")

async def count_final_results():
    """Count the final reference results"""
    print(f"\n📊 FINAL REFERENCE COUNT:")
    
    ref_dir = Path("references")
    total_refs = 0
    aggressive_refs = 0
    file_count = 0
    
    if ref_dir.exists():
        for csv_file in ref_dir.glob("*.csv"):
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    ref_count = len(rows) - 1  # Subtract header
                    
                    if ref_count > 0:
                        total_refs += ref_count
                        file_count += 1
                        
                        if 'aggressive' in csv_file.name:
                            aggressive_refs += ref_count
                            
            except Exception:
                continue
    
    print(f"   📁 Total CSV files: {file_count}")
    print(f"   🚀 Aggressive references: {aggressive_refs}")
    print(f"   📚 Total references: {total_refs}")
    
    improvement = total_refs / 396 if total_refs > 396 else 1
    print(f"   📈 Improvement over original: {improvement:.1f}x ({total_refs} vs 396)")
    
    if total_refs >= 3000:
        print(f"   🎉 EXCELLENT! We've achieved massive improvement!")
    elif total_refs >= 1000:
        print(f"   🎯 GREAT! Significant improvement achieved!")
    else:
        print(f"   📊 Good progress, more documents to process")

if __name__ == "__main__":
    asyncio.run(resume_processing())
