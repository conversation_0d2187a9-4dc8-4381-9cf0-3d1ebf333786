#!/usr/bin/env python3
"""
Simple OneNote Brain Ingestion Pipeline

This script provides a working automated ingestion pipeline using direct Redis connections.
"""

import asyncio
import sys
import os
import time
import json
import re
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.onenote_auth_manager import OneNoteAuthManager

class SimpleBrainIngestion:
    """Simple automated ingestion pipeline."""
    
    def __init__(self):
        self.auth_manager = OneNoteAuthManager()
        self.start_time = None
        self.stats = {
            'pages_processed': 0,
            'entities_added': 0,
            'references_added': 0,
            'chunks_processed': 0,
            'total_characters': 0,
            'errors': []
        }
        
        # Initialize Redis connections
        import redis
        self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        self.falkor_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
    def log_progress(self, message):
        """Log progress with timestamp."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] {message}")
        
    def log_error(self, error_msg):
        """Log an error."""
        self.stats['errors'].append(error_msg)
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] ❌ ERROR: {error_msg}")
        
    def extract_entities_simple(self, text):
        """Simple entity extraction using regex patterns."""
        entities = []
        
        # Medical/health entities
        patterns = {
            'Disease': r'\b(?:alzheimer|parkinson|diabetes|cancer|depression|anxiety|hypertension|stroke|dementia)\b',
            'Medication': r'\b(?:aspirin|ibuprofen|acetaminophen|metformin|insulin|warfarin|statins)\b',
            'Nutrient': r'\b(?:vitamin [A-Z]|calcium|iron|magnesium|zinc|omega-3|folate|b12|d3)\b',
            'Herb': r'\b(?:ginger|turmeric|garlic|ginseng|echinacea|ginkgo|curcumin|gingerol)\b',
            'Process': r'\b(?:metabolism|digestion|absorption|synthesis|oxidation|inflammation|neuroprotection)\b',
            'Research': r'\b(?:study|trial|research|analysis|investigation|experiment)\b'
        }
        
        for entity_type, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                entities.append({
                    'type': entity_type,
                    'name': match.lower(),
                    'text': match
                })
        
        return entities
    
    def extract_references_simple(self, text):
        """Simple reference extraction using regex patterns."""
        references = []
        
        # DOI pattern
        doi_pattern = r'doi:\s*(10\.\d+/[^\s]+)'
        for doi in re.findall(doi_pattern, text, re.IGNORECASE):
            references.append({'type': 'doi', 'doi': doi, 'text': f"DOI: {doi}"})
        
        # PMID pattern
        pmid_pattern = r'PMID:\s*(\d+)'
        for pmid in re.findall(pmid_pattern, text, re.IGNORECASE):
            references.append({'type': 'pmid', 'pmid': pmid, 'text': f"PMID: {pmid}"})
        
        # Numbered references
        numbered_pattern = r'^\s*(\d+)\.\s+(.+?)(?=\n\s*\d+\.|$)'
        for num, ref_text in re.findall(numbered_pattern, text, re.MULTILINE | re.DOTALL):
            if len(ref_text.strip()) > 20:  # Only meaningful references
                references.append({'type': 'numbered', 'number': int(num), 'text': ref_text.strip()})
        
        # Bracketed references
        bracketed_pattern = r'\[(\d+)\]'
        for num in re.findall(bracketed_pattern, text):
            references.append({'type': 'bracketed', 'number': int(num), 'text': f"[{num}]"})
        
        return references
    
    async def get_brain_content(self):
        """Get all content from the Brain section."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            access_token = self.auth_manager.get_valid_token()
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the Brain section
            self.log_progress("🔍 Finding Brain section...")
            sections_url = "https://graph.microsoft.com/v1.0/me/onenote/sections?$filter=displayName eq 'Brain'"
            sections_response = requests.get(sections_url, headers=headers, timeout=30)
            
            if sections_response.status_code != 200:
                self.log_error(f"Failed to get Brain section: {sections_response.status_code}")
                return []
            
            sections = sections_response.json().get('value', [])
            if not sections:
                self.log_error("Brain section not found")
                return []
            
            brain_section_id = sections[0]['id']
            self.log_progress(f"✅ Found Brain section")
            
            # Get pages in the Brain section
            self.log_progress("📄 Getting pages from Brain section...")
            pages_url = f"https://graph.microsoft.com/v1.0/me/onenote/sections/{brain_section_id}/pages"
            pages_response = requests.get(pages_url, headers=headers, timeout=60)
            
            if pages_response.status_code != 200:
                self.log_error(f"Failed to get pages: {pages_response.status_code}")
                return []
            
            pages = pages_response.json().get('value', [])
            self.log_progress(f"✅ Found {len(pages)} pages in Brain section")
            
            # Get content for each page
            all_content = []
            
            for i, page in enumerate(pages, 1):
                page_title = page.get('title', f'Page {i}')
                page_id = page.get('id')
                
                self.log_progress(f"📄 [{i}/{len(pages)}] Getting content: {page_title}")
                
                # Get page content
                content_url = f"https://graph.microsoft.com/v1.0/me/onenote/pages/{page_id}/content"
                content_response = requests.get(content_url, headers=headers, timeout=60)
                
                if content_response.status_code == 200:
                    html_content = content_response.text
                    
                    # Extract text from HTML
                    soup = BeautifulSoup(html_content, 'html.parser')
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    text = soup.get_text()
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    clean_text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    all_content.append({
                        'page_title': page_title,
                        'page_id': page_id,
                        'content': clean_text,
                        'content_length': len(clean_text)
                    })
                    
                    self.log_progress(f"✅ {page_title}: {len(clean_text)} characters")
                    
                    # Special attention to ginger page
                    if 'ginger' in page_title.lower():
                        self.log_progress(f"🌶️ GINGER PAGE: {len(clean_text)} characters extracted!")
                else:
                    self.log_error(f"Failed to get content for {page_title}: {content_response.status_code}")
                
                # Brief pause to avoid rate limiting
                await asyncio.sleep(0.5)
            
            return all_content
            
        except Exception as e:
            self.log_error(f"Error getting Brain content: {e}")
            return []
    
    def store_entities_in_falkor(self, entities, metadata):
        """Store entities in FalkorDB."""
        for entity in entities:
            try:
                # Create entity node in FalkorDB
                query = f"""
                MERGE (e:Entity {{name: '{entity['name']}', type: '{entity['type']}'}})
                SET e.source = 'OneNote',
                    e.page_title = '{metadata['page_title']}',
                    e.text = '{entity['text'].replace("'", "\\'")}',
                    e.created_at = timestamp()
                RETURN e
                """
                
                result = self.falkor_client.execute_command("GRAPH.QUERY", "knowledge_graph", query)
                self.stats['entities_added'] += 1
                
            except Exception as e:
                self.log_error(f"Error storing entity {entity.get('name', '')}: {e}")
    
    def store_references_in_redis(self, references, metadata):
        """Store references in Redis."""
        for i, reference in enumerate(references):
            try:
                # Store reference data
                ref_data = {
                    'type': reference['type'],
                    'text': reference['text'],
                    'source': 'OneNote',
                    'page_title': metadata['page_title'],
                    'created_at': time.time()
                }
                
                if 'doi' in reference:
                    ref_data['doi'] = reference['doi']
                if 'pmid' in reference:
                    ref_data['pmid'] = reference['pmid']
                if 'number' in reference:
                    ref_data['number'] = reference['number']
                
                # Store in Redis as JSON
                ref_key = f"reference:{metadata['page_title']}:{reference['type']}:{i}"
                self.redis_client.set(ref_key, json.dumps(ref_data))
                self.stats['references_added'] += 1
                
            except Exception as e:
                self.log_error(f"Error storing reference: {e}")
    
    async def process_content_chunk(self, content, metadata):
        """Process a single content chunk through the pipeline."""
        try:
            chunk_id = f"{metadata['page_title']}_{metadata.get('chunk_index', 0)}"
            self.log_progress(f"🔄 Processing chunk: {chunk_id}")
            
            # Extract entities
            entities = self.extract_entities_simple(content)
            self.log_progress(f"🧠 Extracted {len(entities)} entities")
            
            # Extract references
            references = self.extract_references_simple(content)
            self.log_progress(f"📚 Extracted {len(references)} references")
            
            # Store entities in FalkorDB
            self.store_entities_in_falkor(entities, metadata)
            
            # Store references in Redis
            self.store_references_in_redis(references, metadata)
            
            # Update stats
            self.stats['chunks_processed'] += 1
            self.stats['total_characters'] += len(content)
            
            self.log_progress(f"✅ Chunk processed: {len(entities)}E, {len(references)}R")
            
            return True
            
        except Exception as e:
            self.log_error(f"Error processing chunk {chunk_id}: {e}")
            return False
    
    async def chunk_content(self, content, chunk_size=1200, overlap=0):
        """Split content into chunks for processing."""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(content):
                sentence_end = content.rfind('.', end - 100, end)
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
        
        return chunks
    
    async def process_all_content(self):
        """Process all OneNote Brain content through the pipeline."""
        self.start_time = time.time()
        
        self.log_progress("🚀 Starting Simple OneNote Brain Ingestion")
        self.log_progress(f"📊 Current entity count in dashboard: 13,748")
        
        # Get all content
        all_content = await self.get_brain_content()
        
        if not all_content:
            self.log_error("No content found to process")
            return False
        
        self.log_progress(f"📋 Processing {len(all_content)} pages through pipeline...")
        
        # Process each page
        for page_data in all_content:
            page_title = page_data['page_title']
            content = page_data['content']
            
            self.log_progress(f"📄 Processing page: {page_title}")
            
            # Chunk the content
            chunks = await self.chunk_content(content, chunk_size=1200, overlap=0)
            self.log_progress(f"📋 Split into {len(chunks)} chunks")
            
            # Process each chunk
            for i, chunk in enumerate(chunks):
                metadata = {
                    'source': 'OneNote',
                    'notebook': 'Biochemistry',
                    'section': 'Brain',
                    'page_title': page_title,
                    'page_id': page_data['page_id'],
                    'chunk_index': i,
                    'total_chunks': len(chunks)
                }
                
                success = await self.process_content_chunk(chunk, metadata)
                
                if success and i == 0:  # Count page once
                    self.stats['pages_processed'] += 1
                
                # Brief pause between chunks
                await asyncio.sleep(0.1)
        
        return True
    
    async def generate_final_report(self):
        """Generate final processing report."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 60)
        print("📊 SIMPLE INGESTION COMPLETE")
        print("=" * 60)
        print(f"⏰ Total processing time: {elapsed:.1f} seconds")
        print(f"📄 Pages processed: {self.stats['pages_processed']}")
        print(f"📋 Chunks processed: {self.stats['chunks_processed']}")
        print(f"🧠 Entities added to knowledge graph: {self.stats['entities_added']}")
        print(f"📚 References stored: {self.stats['references_added']}")
        print(f"📊 Total characters processed: {self.stats['total_characters']:,}")
        print(f"❌ Errors encountered: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            print(f"\n❌ Error Details:")
            for i, error in enumerate(self.stats['errors'], 1):
                print(f"   {i}. {error}")
        
        print(f"\n📈 Entity Count Change:")
        print(f"   Before: 13,748 entities")
        print(f"   Added: {self.stats['entities_added']} entities")
        print(f"   Expected After: {13748 + self.stats['entities_added']} entities")
        print(f"   📋 CHECK DASHBOARD NOW for actual count!")

async def main():
    """Main ingestion function."""
    print("🌟" * 60)
    print("🧠 SIMPLE ONENOTE BRAIN INGESTION")
    print("🌟" * 60)
    
    # Check database connections
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        print("✅ Redis connection: Working")
        
        falkor_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        falkor_client.execute_command("GRAPH.QUERY", "knowledge_graph", "RETURN 1")
        print("✅ FalkorDB connection: Working")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("📋 Please start Redis/FalkorDB: docker-compose up -d")
        return
    
    # Initialize and run pipeline
    pipeline = SimpleBrainIngestion()
    
    success = await pipeline.process_all_content()
    
    await pipeline.generate_final_report()
    
    if success:
        print("\n🎉 SUCCESS! Simple ingestion completed!")
        print("\n📋 Your OneNote Brain content is now in the knowledge graph!")
        print("📊 Check the dashboard for updated entity count")
    else:
        print("\n❌ Simple ingestion failed")
        print("📋 Check errors above and retry")
    
    print("\n🌟" * 60)
    print("🎉 SIMPLE PIPELINE COMPLETE!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
