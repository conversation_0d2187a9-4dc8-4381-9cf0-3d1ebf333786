#!/usr/bin/env python3
"""
OneNote Notebook Discovery Script

This script finds OneNote notebooks by analyzing the folder structure
of .one files and identifying the notebook containers.
"""

import os
from pathlib import Path
import json
from datetime import datetime
from typing import List, Dict, Any, Set
from collections import defaultdict

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def find_onenote_files(search_locations: List[str]) -> List[Dict[str, Any]]:
    """Find all OneNote files in the specified locations."""
    onenote_files = []
    
    for location in search_locations:
        if not os.path.exists(location):
            continue
            
        print(f"🔍 Searching for .one files in: {location}")
        
        try:
            path_obj = Path(location)
            
            # Search for .one files recursively
            for one_file in path_obj.rglob("*.one"):
                try:
                    stat = one_file.stat()
                    file_info = {
                        'path': str(one_file),
                        'name': one_file.name,
                        'size': stat.st_size,
                        'size_formatted': format_file_size(stat.st_size),
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'parent_folder': str(one_file.parent),
                        'parts': one_file.parts,
                        'accessible': True
                    }
                    onenote_files.append(file_info)
                    print(f"   ✓ Found: {one_file.name}")
                except Exception as e:
                    print(f"   ❌ Error processing {one_file}: {e}")
                    
        except PermissionError:
            print(f"   ⚠️  Permission denied: {location}")
        except Exception as e:
            print(f"   ❌ Error searching {location}: {e}")
    
    return onenote_files

def analyze_notebook_structure(onenote_files: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the folder structure to identify OneNote notebooks."""
    
    notebooks = {}
    folder_analysis = defaultdict(list)
    
    # Group files by their parent folders
    for file_info in onenote_files:
        parent_folder = file_info['parent_folder']
        folder_analysis[parent_folder].append(file_info)
    
    print(f"\n📊 Analyzing {len(folder_analysis)} folders containing .one files...")
    
    for folder_path, files in folder_analysis.items():
        folder_obj = Path(folder_path)
        
        # Calculate folder statistics
        total_size = sum(f['size'] for f in files)
        file_count = len(files)
        
        # Get folder metadata
        try:
            folder_stat = folder_obj.stat()
            folder_created = datetime.fromtimestamp(folder_stat.st_ctime).isoformat()
            folder_modified = datetime.fromtimestamp(folder_stat.st_mtime).isoformat()
        except:
            folder_created = "Unknown"
            folder_modified = "Unknown"
        
        # Determine if this looks like a notebook
        is_notebook = file_count > 1  # Notebooks typically have multiple pages
        
        # Look for notebook indicators
        notebook_indicators = []
        folder_name = folder_obj.name.lower()
        
        # Check for common OneNote folder patterns
        if any(indicator in folder_name for indicator in ['notebook', 'notes', 'onenote']):
            notebook_indicators.append("Name contains notebook keywords")
            is_notebook = True
        
        # Check if folder contains section groups (subfolders with .one files)
        section_groups = []
        try:
            for item in folder_obj.iterdir():
                if item.is_dir():
                    subfolder_ones = list(item.glob("*.one"))
                    if subfolder_ones:
                        section_groups.append({
                            'name': item.name,
                            'path': str(item),
                            'page_count': len(subfolder_ones),
                            'pages': [f.name for f in subfolder_ones]
                        })
        except:
            pass
        
        if section_groups:
            notebook_indicators.append(f"Contains {len(section_groups)} section groups")
            is_notebook = True
        
        # Create notebook entry
        notebook_info = {
            'notebook_name': folder_obj.name,
            'notebook_path': folder_path,
            'is_likely_notebook': is_notebook,
            'indicators': notebook_indicators,
            'page_count': file_count,
            'total_size': total_size,
            'total_size_formatted': format_file_size(total_size),
            'created': folder_created,
            'modified': folder_modified,
            'section_groups': section_groups,
            'pages': [
                {
                    'name': f['name'],
                    'path': f['path'],
                    'size': f['size'],
                    'size_formatted': f['size_formatted'],
                    'modified': f['modified']
                }
                for f in sorted(files, key=lambda x: x['name'].lower())
            ]
        }
        
        notebooks[folder_path] = notebook_info
        
        if is_notebook:
            print(f"   📓 Notebook: {folder_obj.name} ({file_count} pages, {format_file_size(total_size)})")
        else:
            print(f"   📄 Single page folder: {folder_obj.name}")
    
    return notebooks

def save_notebook_analysis(notebooks: Dict[str, Any], output_dir: str = "."):
    """Save the notebook analysis results."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save detailed JSON report
    json_file = os.path.join(output_dir, f"onenote_notebooks_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(notebooks, f, indent=2, ensure_ascii=False)
    print(f"📄 Saved detailed notebook analysis: {json_file}")
    
    # Save notebook summary
    summary_file = os.path.join(output_dir, f"onenote_notebooks_summary_{timestamp}.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("OneNote Notebooks Discovery Report\n")
        f.write("=" * 50 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        likely_notebooks = {k: v for k, v in notebooks.items() if v['is_likely_notebook']}
        single_pages = {k: v for k, v in notebooks.items() if not v['is_likely_notebook']}
        
        f.write(f"📓 NOTEBOOKS FOUND: {len(likely_notebooks)}\n")
        f.write(f"📄 SINGLE PAGE FOLDERS: {len(single_pages)}\n\n")
        
        if likely_notebooks:
            f.write("NOTEBOOKS:\n")
            f.write("-" * 30 + "\n")
            
            for notebook_path, notebook in sorted(likely_notebooks.items()):
                f.write(f"\n📓 {notebook['notebook_name']}\n")
                f.write(f"   Path: {notebook_path}\n")
                f.write(f"   Pages: {notebook['page_count']}\n")
                f.write(f"   Size: {notebook['total_size_formatted']}\n")
                f.write(f"   Modified: {notebook['modified']}\n")
                
                if notebook['section_groups']:
                    f.write(f"   Section Groups: {len(notebook['section_groups'])}\n")
                    for sg in notebook['section_groups']:
                        f.write(f"      • {sg['name']} ({sg['page_count']} pages)\n")
                
                f.write(f"   Pages:\n")
                for page in notebook['pages'][:10]:  # Show first 10 pages
                    f.write(f"      • {page['name']} ({page['size_formatted']})\n")
                
                if len(notebook['pages']) > 10:
                    f.write(f"      ... and {len(notebook['pages']) - 10} more pages\n")
        
        if single_pages:
            f.write(f"\n\nSINGLE PAGE FOLDERS:\n")
            f.write("-" * 30 + "\n")
            
            for folder_path, folder in sorted(single_pages.items()):
                f.write(f"\n📄 {folder['notebook_name']}\n")
                f.write(f"   Path: {folder_path}\n")
                f.write(f"   Page: {folder['pages'][0]['name']} ({folder['pages'][0]['size_formatted']})\n")
    
    print(f"📝 Saved notebook summary: {summary_file}")
    
    # Save processing list for notebooks
    processing_file = os.path.join(output_dir, f"notebook_processing_list_{timestamp}.txt")
    with open(processing_file, 'w', encoding='utf-8') as f:
        f.write("# OneNote Notebooks for Processing\n")
        f.write("# Edit this list to select which notebooks/folders you want to process\n")
        f.write("# Remove the # from lines you want to process\n")
        f.write("# You can process entire notebooks or individual pages\n\n")
        
        likely_notebooks = {k: v for k, v in notebooks.items() if v['is_likely_notebook']}
        
        f.write("# NOTEBOOKS (process entire notebook folders):\n")
        for notebook_path, notebook in sorted(likely_notebooks.items()):
            f.write(f"# NOTEBOOK: {notebook['notebook_name']} ({notebook['page_count']} pages)\n")
            f.write(f"# {notebook_path}\n\n")
        
        f.write("# INDIVIDUAL PAGES (if you prefer to select specific pages):\n")
        for notebook_path, notebook in sorted(notebooks.items()):
            f.write(f"# From: {notebook['notebook_name']}\n")
            for page in notebook['pages']:
                f.write(f"# {page['path']}\n")
            f.write("\n")
    
    print(f"📋 Saved processing list: {processing_file}")

def main():
    """Main function to discover OneNote notebooks."""
    
    print("🚀 OneNote Notebook Discovery Tool")
    print("=" * 50)
    
    # Common OneNote locations
    search_locations = [
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/OneDrive"),
        os.path.expanduser("~/OneDrive - Personal"),
        os.path.expanduser("~/OneDrive for Business"),
        os.path.expanduser("~/Desktop"),
        "C:\\Users\\<USER>\\Documents",
    ]
    
    # Add user-specific paths
    username = os.getenv('USERNAME', 'User')
    additional_paths = [
        f"C:\\Users\\<USER>\\Documents",
        f"C:\\Users\\<USER>\\OneDrive",
    ]
    
    search_locations.extend(additional_paths)
    
    # Remove duplicates and non-existent paths
    search_locations = list(set([path for path in search_locations if os.path.exists(path)]))
    
    print(f"Searching in {len(search_locations)} locations for OneNote files...")
    
    # Find all OneNote files
    onenote_files = find_onenote_files(search_locations)
    
    if not onenote_files:
        print("\n❌ No OneNote files found.")
        return
    
    print(f"\n✅ Found {len(onenote_files)} OneNote files total")
    
    # Analyze notebook structure
    notebooks = analyze_notebook_structure(onenote_files)
    
    # Print summary
    likely_notebooks = {k: v for k, v in notebooks.items() if v['is_likely_notebook']}
    single_pages = {k: v for k, v in notebooks.items() if not v['is_likely_notebook']}
    
    print(f"\n📊 SUMMARY:")
    print(f"   📓 Notebooks found: {len(likely_notebooks)}")
    print(f"   📄 Single page folders: {len(single_pages)}")
    
    if likely_notebooks:
        print(f"\n📓 NOTEBOOKS:")
        for notebook_path, notebook in sorted(likely_notebooks.items()):
            print(f"   • {notebook['notebook_name']} ({notebook['page_count']} pages, {notebook['total_size_formatted']})")
            if notebook['section_groups']:
                print(f"     └─ {len(notebook['section_groups'])} section groups")
    
    # Save results
    save_notebook_analysis(notebooks)
    
    print(f"\n📋 Next Steps:")
    print("1. Review the notebook analysis reports")
    print("2. Edit the processing list to select notebooks or individual pages")
    print("3. Use the selected paths for batch processing in Graphiti")
    print("4. Consider processing entire notebooks for better context")
    
    print(f"\n✨ Notebook discovery complete!")

if __name__ == "__main__":
    main()
